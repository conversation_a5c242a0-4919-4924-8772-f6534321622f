[{"/Users/<USER>/Downloads/Flood/frontend/src/index.js": "1", "/Users/<USER>/Downloads/Flood/frontend/src/App.js": "2", "/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js": "3", "/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js": "4", "/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js": "5", "/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js": "6", "/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js": "7", "/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js": "8", "/Users/<USER>/Downloads/Flood/frontend/src/components/VoiceEmergencyAssistant.js": "9", "/Users/<USER>/Downloads/Flood/frontend/src/components/CommunityReports.js": "10", "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/AnimatedComponents.js": "11", "/Users/<USER>/Downloads/Flood/frontend/src/hooks/useScrollAnimation.js": "12", "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/LoadingAnimation.js": "13", "/Users/<USER>/Downloads/Flood/frontend/src/components/LocationSelector.js": "14", "/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js": "15"}, {"size": 254, "mtime": 1747753433573, "results": "16", "hashOfConfig": "17"}, {"size": 52067, "mtime": 1749101966578, "results": "18", "hashOfConfig": "17"}, {"size": 17971, "mtime": 1747764370243, "results": "19", "hashOfConfig": "17"}, {"size": 13277, "mtime": 1747903365036, "results": "20", "hashOfConfig": "17"}, {"size": 69670, "mtime": 1749102132246, "results": "21", "hashOfConfig": "17"}, {"size": 8680, "mtime": 1749101946658, "results": "22", "hashOfConfig": "17"}, {"size": 28839, "mtime": 1749101524651, "results": "23", "hashOfConfig": "17"}, {"size": 38847, "mtime": 1749102233995, "results": "24", "hashOfConfig": "17"}, {"size": 11970, "mtime": 1747757312237, "results": "25", "hashOfConfig": "17"}, {"size": 28486, "mtime": 1747799193533, "results": "26", "hashOfConfig": "17"}, {"size": 5615, "mtime": 1747761188824, "results": "27", "hashOfConfig": "17"}, {"size": 1564, "mtime": 1747761161281, "results": "28", "hashOfConfig": "17"}, {"size": 4145, "mtime": 1747761615192, "results": "29", "hashOfConfig": "17"}, {"size": 8969, "mtime": 1747799643617, "results": "30", "hashOfConfig": "17"}, {"size": 3672, "mtime": 1747801467631, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1xqa9ev", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/Flood/frontend/src/index.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/App.js", ["77", "78", "79"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/ResultDisplay.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js", ["80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/RiskFactorsChart.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js", ["91", "92"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/VoiceEmergencyAssistant.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/CommunityReports.js", ["93", "94", "95", "96"], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/AnimatedComponents.js", ["97"], [], "/Users/<USER>/Downloads/Flood/frontend/src/hooks/useScrollAnimation.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/animations/LoadingAnimation.js", [], [], "/Users/<USER>/Downloads/Flood/frontend/src/components/LocationSelector.js", ["98", "99", "100"], [], "/Users/<USER>/Downloads/Flood/frontend/src/theme/neuromorphicUtils.js", [], [], {"ruleId": "101", "severity": 1, "message": "102", "line": 16, "column": 10, "nodeType": "103", "messageId": "104", "endLine": 16, "endColumn": 28}, {"ruleId": "101", "severity": 1, "message": "105", "line": 16, "column": 54, "nodeType": "103", "messageId": "104", "endLine": 16, "endColumn": 75}, {"ruleId": "101", "severity": 1, "message": "106", "line": 16, "column": 77, "nodeType": "103", "messageId": "104", "endLine": 16, "endColumn": 93}, {"ruleId": "101", "severity": 1, "message": "107", "line": 9, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 9, "endColumn": 14}, {"ruleId": "101", "severity": 1, "message": "108", "line": 10, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 10, "endColumn": 12}, {"ruleId": "101", "severity": 1, "message": "109", "line": 12, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 12, "endColumn": 10}, {"ruleId": "101", "severity": 1, "message": "110", "line": 21, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 21, "endColumn": 7}, {"ruleId": "101", "severity": 1, "message": "111", "line": 23, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 23, "endColumn": 7}, {"ruleId": "101", "severity": 1, "message": "112", "line": 24, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 24, "endColumn": 14}, {"ruleId": "101", "severity": 1, "message": "113", "line": 49, "column": 8, "nodeType": "103", "messageId": "104", "endLine": 49, "endColumn": 15}, {"ruleId": "101", "severity": 1, "message": "114", "line": 50, "column": 8, "nodeType": "103", "messageId": "104", "endLine": 50, "endColumn": 18}, {"ruleId": "101", "severity": 1, "message": "115", "line": 552, "column": 26, "nodeType": "103", "messageId": "104", "endLine": 552, "endColumn": 43}, {"ruleId": "101", "severity": 1, "message": "116", "line": 568, "column": 13, "nodeType": "103", "messageId": "104", "endLine": 568, "endColumn": 23}, {"ruleId": "101", "severity": 1, "message": "117", "line": 653, "column": 9, "nodeType": "103", "messageId": "104", "endLine": 653, "endColumn": 14}, {"ruleId": "101", "severity": 1, "message": "118", "line": 14, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 14, "endColumn": 10}, {"ruleId": "101", "severity": 1, "message": "119", "line": 35, "column": 8, "nodeType": "103", "messageId": "104", "endLine": 35, "endColumn": 22}, {"ruleId": "101", "severity": 1, "message": "120", "line": 26, "column": 3, "nodeType": "103", "messageId": "104", "endLine": 26, "endColumn": 9}, {"ruleId": "121", "severity": 1, "message": "122", "line": 176, "column": 6, "nodeType": "123", "endLine": 176, "endColumn": 8, "suggestions": "124"}, {"ruleId": "121", "severity": 1, "message": "122", "line": 199, "column": 6, "nodeType": "123", "endLine": 199, "endColumn": 8, "suggestions": "125"}, {"ruleId": "101", "severity": 1, "message": "126", "line": 252, "column": 9, "nodeType": "103", "messageId": "104", "endLine": 252, "endColumn": 27}, {"ruleId": "127", "severity": 1, "message": "128", "line": 225, "column": 1, "nodeType": "129", "endLine": 234, "endColumn": 3}, {"ruleId": "101", "severity": 1, "message": "130", "line": 49, "column": 10, "nodeType": "103", "messageId": "104", "endLine": 49, "endColumn": 21}, {"ruleId": "101", "severity": 1, "message": "131", "line": 49, "column": 23, "nodeType": "103", "messageId": "104", "endLine": 49, "endColumn": 37}, {"ruleId": "121", "severity": 1, "message": "132", "line": 112, "column": 6, "nodeType": "123", "endLine": 112, "endColumn": 24, "suggestions": "133"}, "no-unused-vars", "'neuromorphicStyles' is defined but never used.", "Identifier", "unusedVar", "'getNeuromorphicShadow' is defined but never used.", "'getPressedEffect' is defined but never used.", "'ZoomControl' is defined but never used.", "'Rectangle' is defined but never used.", "'Polygon' is defined but never used.", "'Chip' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'MapIcon' is defined but never used.", "'LayersIcon' is defined but never used.", "'setHeatMapVisible' is assigned a value but never used.", "'zoneCenter' is assigned a value but never used.", "'theme' is assigned a value but never used.", "'Divider' is defined but never used.", "'LocationOnIcon' is defined but never used.", "'Rating' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mapAlertToReport'. Either include it or remove the dependency array.", "ArrayExpression", ["134"], ["135"], "'handleSliderChange' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'searchQuery' is assigned a value but never used.", "'setSearchQuery' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'findNearestCity'. Either include it or remove the dependency array.", ["136"], {"desc": "137", "fix": "138"}, {"desc": "137", "fix": "139"}, {"desc": "140", "fix": "141"}, "Update the dependencies array to be: [mapAlertToReport]", {"range": "142", "text": "143"}, {"range": "144", "text": "143"}, "Update the dependencies array to be: [findNearestCity, onLocationSelect]", {"range": "145", "text": "146"}, [4769, 4771], "[mapAlertToReport]", [5425, 5427], [4524, 4542], "[findNearestCity, onLocationSelect]"]