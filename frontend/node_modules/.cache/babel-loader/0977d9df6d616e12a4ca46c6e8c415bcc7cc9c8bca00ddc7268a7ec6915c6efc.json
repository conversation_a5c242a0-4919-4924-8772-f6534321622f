{"ast": null, "code": "import React,{useState,useEffect}from'react';import{App<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Typography,Box,Button,useTheme,useMediaQuery,IconButton,Menu,MenuItem,Drawer,List,ListItem,ListItemIcon,ListItemText,Divider}from'@mui/material';import WaterDropIcon from'@mui/icons-material/WaterDrop';import MenuIcon from'@mui/icons-material/Menu';import InfoIcon from'@mui/icons-material/Info';import DescriptionIcon from'@mui/icons-material/Description';import HomeIcon from'@mui/icons-material/Home';import{useSpring,animated}from'react-spring';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const AnimatedWaterDrop=()=>{const[isAnimating,setIsAnimating]=useState(false);const dropAnimation=useSpring({transform:isAnimating?'translateY(10px)':'translateY(0px)',config:{tension:300,friction:10},loop:{reverse:true}});useEffect(()=>{const interval=setInterval(()=>{setIsAnimating(prev=>!prev);},2000);return()=>clearInterval(interval);},[]);return/*#__PURE__*/_jsx(animated.div,{style:dropAnimation,children:/*#__PURE__*/_jsx(WaterDropIcon,{sx:{fontSize:28,color:'#ffffff',filter:'drop-shadow(0 0 5px rgba(76, 201, 240, 0.5))'}})});};const Header=()=>{const theme=useTheme();const isMobile=useMediaQuery(theme.breakpoints.down('sm'));const[mobileMenuOpen,setMobileMenuOpen]=useState(false);const[anchorEl,setAnchorEl]=useState(null);const handleMenuClick=event=>{setAnchorEl(event.currentTarget);};const handleMenuClose=()=>{setAnchorEl(null);};const toggleMobileMenu=()=>{setMobileMenuOpen(!mobileMenuOpen);};const logoAnimation=useSpring({from:{opacity:0,transform:'translateX(-20px)'},to:{opacity:1,transform:'translateX(0)'},config:{tension:280,friction:20},delay:200});const buttonAnimation=useSpring({from:{opacity:0,transform:'translateY(-10px)'},to:{opacity:1,transform:'translateY(0)'},config:{tension:280,friction:20},delay:400});return/*#__PURE__*/_jsx(AppBar,{position:\"static\",elevation:0,sx:{background:'linear-gradient(90deg, #3A86FF 0%, #4CC9F0 100%)',boxShadow:'0 4px 20px rgba(58, 134, 255, 0.15)'},children:/*#__PURE__*/_jsxs(Toolbar,{sx:{py:1},children:[/*#__PURE__*/_jsx(animated.div,{style:logoAnimation,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,display:'flex',alignItems:'center'},children:/*#__PURE__*/_jsx(AnimatedWaterDrop,{})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",component:\"div\",sx:{flexGrow:1,fontWeight:700,letterSpacing:'0.5px',textShadow:'0 2px 4px rgba(0,0,0,0.1)'},children:\"Flood Risk Prediction\"})]})}),/*#__PURE__*/_jsx(Box,{sx:{flexGrow:1}}),isMobile?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",edge:\"end\",onClick:toggleMobileMenu,sx:{ml:2,'&:hover':{background:'rgba(255,255,255,0.1)'}},children:/*#__PURE__*/_jsx(MenuIcon,{})}),/*#__PURE__*/_jsx(Drawer,{anchor:\"right\",open:mobileMenuOpen,onClose:toggleMobileMenu,PaperProps:{sx:{width:240,background:'linear-gradient(135deg, #3A86FF 0%, #4CC9F0 100%)',color:'#fff'}},children:/*#__PURE__*/_jsxs(Box,{sx:{p:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,mb:2},children:\"Menu\"}),/*#__PURE__*/_jsx(Divider,{sx:{backgroundColor:'rgba(255,255,255,0.2)'}}),/*#__PURE__*/_jsxs(List,{children:[/*#__PURE__*/_jsxs(ListItem,{button:true,sx:{borderRadius:2,mb:1,backgroundColor:'rgba(255, 255, 255, 0.95)',border:'2px solid rgba(26, 32, 44, 0.2)',backdropFilter:'blur(10px)',transition:'all 0.3s ease','&:hover':{backgroundColor:'rgba(255, 255, 255, 1)',border:'2px solid #1a202c',transform:'translateX(6px)',boxShadow:'0 4px 12px rgba(0, 0, 0, 0.2)'}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{color:'#1a202c'},children:/*#__PURE__*/_jsx(HomeIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"Home\",sx:{'& .MuiListItemText-primary':{color:'#1a202c',fontWeight:700,fontSize:'16px'}}})]}),/*#__PURE__*/_jsxs(ListItem,{button:true,sx:{borderRadius:2,mb:1,backgroundColor:'rgba(255, 255, 255, 0.95)',border:'2px solid rgba(26, 32, 44, 0.2)',backdropFilter:'blur(10px)',transition:'all 0.3s ease','&:hover':{backgroundColor:'rgba(255, 255, 255, 1)',border:'2px solid #1a202c',transform:'translateX(6px)',boxShadow:'0 4px 12px rgba(0, 0, 0, 0.2)'}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{color:'#1a202c'},children:/*#__PURE__*/_jsx(InfoIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"About\",sx:{'& .MuiListItemText-primary':{color:'#1a202c',fontWeight:700,fontSize:'16px'}}})]}),/*#__PURE__*/_jsxs(ListItem,{button:true,sx:{borderRadius:2,backgroundColor:'rgba(255, 255, 255, 0.95)',border:'2px solid rgba(26, 32, 44, 0.2)',backdropFilter:'blur(10px)',transition:'all 0.3s ease','&:hover':{backgroundColor:'rgba(255, 255, 255, 1)',border:'2px solid #1a202c',transform:'translateX(6px)',boxShadow:'0 4px 12px rgba(0, 0, 0, 0.2)'}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{color:'#1a202c'},children:/*#__PURE__*/_jsx(DescriptionIcon,{})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"Documentation\",sx:{'& .MuiListItemText-primary':{color:'#1a202c',fontWeight:700,fontSize:'16px'}}})]})]})]})})]}):/*#__PURE__*/_jsx(animated.div,{style:buttonAnimation,children:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Button,{sx:{mx:1,fontWeight:700,borderRadius:2,px:3,py:1,color:'#1a202c',backgroundColor:'rgba(255, 255, 255, 0.95)',border:'2px solid rgba(26, 32, 44, 0.2)',backdropFilter:'blur(10px)',textShadow:'none',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.15)',transition:'all 0.3s ease','&:hover':{backgroundColor:'rgba(255, 255, 255, 1)',border:'2px solid #1a202c',transform:'translateY(-2px)',boxShadow:'0 6px 16px rgba(0, 0, 0, 0.25)',color:'#1a202c'},'&:active':{transform:'translateY(0px)',backgroundColor:'rgba(240, 244, 248, 1)'}},startIcon:/*#__PURE__*/_jsx(HomeIcon,{sx:{color:'#1a202c',fontSize:'20px'}}),children:\"Home\"}),/*#__PURE__*/_jsx(Button,{sx:{mx:1,fontWeight:700,borderRadius:2,px:3,py:1,color:'#1a202c',backgroundColor:'rgba(255, 255, 255, 0.95)',border:'2px solid rgba(26, 32, 44, 0.2)',backdropFilter:'blur(10px)',textShadow:'none',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.15)',transition:'all 0.3s ease','&:hover':{backgroundColor:'rgba(255, 255, 255, 1)',border:'2px solid #1a202c',transform:'translateY(-2px)',boxShadow:'0 6px 16px rgba(0, 0, 0, 0.25)',color:'#1a202c'},'&:active':{transform:'translateY(0px)',backgroundColor:'rgba(240, 244, 248, 1)'}},startIcon:/*#__PURE__*/_jsx(InfoIcon,{sx:{color:'#1a202c',fontSize:'20px'}}),children:\"About\"}),/*#__PURE__*/_jsx(Button,{sx:{mx:1,fontWeight:700,borderRadius:2,px:3,py:1,color:'#1a202c',backgroundColor:'rgba(255, 255, 255, 0.95)',border:'2px solid rgba(26, 32, 44, 0.2)',backdropFilter:'blur(10px)',textShadow:'none',boxShadow:'0 2px 8px rgba(0, 0, 0, 0.15)',transition:'all 0.3s ease','&:hover':{backgroundColor:'rgba(255, 255, 255, 1)',border:'2px solid #1a202c',transform:'translateY(-2px)',boxShadow:'0 6px 16px rgba(0, 0, 0, 0.25)',color:'#1a202c'},'&:active':{transform:'translateY(0px)',backgroundColor:'rgba(240, 244, 248, 1)'}},startIcon:/*#__PURE__*/_jsx(DescriptionIcon,{sx:{color:'#1a202c',fontSize:'20px'}}),onClick:handleMenuClick,children:\"Documentation\"}),/*#__PURE__*/_jsxs(Menu,{anchorEl:anchorEl,open:Boolean(anchorEl),onClose:handleMenuClose,PaperProps:{elevation:8,sx:{borderRadius:3,mt:1,background:'rgba(255, 255, 255, 0.98)',backdropFilter:'blur(20px)',border:'2px solid rgba(26, 32, 44, 0.2)',boxShadow:'0 12px 32px rgba(0,0,0,0.2)','& .MuiMenuItem-root':{color:'#1a202c',fontWeight:600,padding:'12px 20px',borderRadius:2,margin:'4px 8px',transition:'all 0.3s ease',fontSize:'15px','&:hover':{backgroundColor:'rgba(58, 134, 255, 0.1)',transform:'translateX(4px)',boxShadow:'0 4px 12px rgba(58, 134, 255, 0.2)',border:'1px solid rgba(58, 134, 255, 0.3)'}}}},children:[/*#__PURE__*/_jsx(MenuItem,{onClick:handleMenuClose,children:\"API Documentation\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:handleMenuClose,children:\"User Guide\"}),/*#__PURE__*/_jsx(MenuItem,{onClick:handleMenuClose,children:\"Model Information\"})]})]})})]})});};export default Header;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AppBar", "<PERSON><PERSON><PERSON>", "Typography", "Box", "<PERSON><PERSON>", "useTheme", "useMediaQuery", "IconButton", "<PERSON><PERSON>", "MenuItem", "Drawer", "List", "ListItem", "ListItemIcon", "ListItemText", "Divider", "WaterDropIcon", "MenuIcon", "InfoIcon", "DescriptionIcon", "HomeIcon", "useSpring", "animated", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "AnimatedWaterDrop", "isAnimating", "setIsAnimating", "dropAnimation", "transform", "config", "tension", "friction", "loop", "reverse", "interval", "setInterval", "prev", "clearInterval", "div", "style", "children", "sx", "fontSize", "color", "filter", "Header", "theme", "isMobile", "breakpoints", "down", "mobileMenuOpen", "setMobileMenuOpen", "anchorEl", "setAnchorEl", "handleMenuClick", "event", "currentTarget", "handleMenuClose", "toggleMobileMenu", "logoAnimation", "from", "opacity", "to", "delay", "buttonAnimation", "position", "elevation", "background", "boxShadow", "py", "display", "alignItems", "mr", "variant", "component", "flexGrow", "fontWeight", "letterSpacing", "textShadow", "edge", "onClick", "ml", "anchor", "open", "onClose", "PaperProps", "width", "p", "mb", "backgroundColor", "button", "borderRadius", "border", "<PERSON><PERSON>ilter", "transition", "primary", "mx", "px", "startIcon", "Boolean", "mt", "padding", "margin"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/Header.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  App<PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  Typography,\n  Box,\n  Button,\n  useTheme,\n  useMediaQuery,\n  IconButton,\n  Menu,\n  MenuItem,\n  Drawer,\n  List,\n  ListItem,\n  ListItemIcon,\n  ListItemText,\n  Divider\n} from '@mui/material';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport MenuIcon from '@mui/icons-material/Menu';\nimport InfoIcon from '@mui/icons-material/Info';\nimport DescriptionIcon from '@mui/icons-material/Description';\nimport HomeIcon from '@mui/icons-material/Home';\nimport { useSpring, animated } from 'react-spring';\n\nconst AnimatedWaterDrop = () => {\n  const [isAnimating, setIsAnimating] = useState(false);\n\n  const dropAnimation = useSpring({\n    transform: isAnimating ? 'translateY(10px)' : 'translateY(0px)',\n    config: { tension: 300, friction: 10 },\n    loop: { reverse: true }\n  });\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setIsAnimating(prev => !prev);\n    }, 2000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <animated.div style={dropAnimation}>\n      <WaterDropIcon\n        sx={{\n          fontSize: 28,\n          color: '#ffffff',\n          filter: 'drop-shadow(0 0 5px rgba(76, 201, 240, 0.5))'\n        }}\n      />\n    </animated.div>\n  );\n};\n\nconst Header = () => {\n  const theme = useTheme();\n  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\n  const [anchorEl, setAnchorEl] = useState(null);\n\n  const handleMenuClick = (event) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  const toggleMobileMenu = () => {\n    setMobileMenuOpen(!mobileMenuOpen);\n  };\n\n  const logoAnimation = useSpring({\n    from: { opacity: 0, transform: 'translateX(-20px)' },\n    to: { opacity: 1, transform: 'translateX(0)' },\n    config: { tension: 280, friction: 20 },\n    delay: 200\n  });\n\n  const buttonAnimation = useSpring({\n    from: { opacity: 0, transform: 'translateY(-10px)' },\n    to: { opacity: 1, transform: 'translateY(0)' },\n    config: { tension: 280, friction: 20 },\n    delay: 400\n  });\n\n  return (\n    <AppBar\n      position=\"static\"\n      elevation={0}\n      sx={{\n        background: 'linear-gradient(90deg, #3A86FF 0%, #4CC9F0 100%)',\n        boxShadow: '0 4px 20px rgba(58, 134, 255, 0.15)'\n      }}\n    >\n      <Toolbar sx={{ py: 1 }}>\n        <animated.div style={logoAnimation}>\n          <Box sx={{ display: 'flex', alignItems: 'center' }}>\n            <Box sx={{ mr: 2, display: 'flex', alignItems: 'center' }}>\n              <AnimatedWaterDrop />\n            </Box>\n            <Typography\n              variant=\"h6\"\n              component=\"div\"\n              sx={{\n                flexGrow: 1,\n                fontWeight: 700,\n                letterSpacing: '0.5px',\n                textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }}\n            >\n              Flood Risk Prediction\n            </Typography>\n          </Box>\n        </animated.div>\n\n        <Box sx={{ flexGrow: 1 }} />\n\n        {isMobile ? (\n          <>\n            <IconButton\n              color=\"inherit\"\n              edge=\"end\"\n              onClick={toggleMobileMenu}\n              sx={{\n                ml: 2,\n                '&:hover': {\n                  background: 'rgba(255,255,255,0.1)'\n                }\n              }}\n            >\n              <MenuIcon />\n            </IconButton>\n\n            <Drawer\n              anchor=\"right\"\n              open={mobileMenuOpen}\n              onClose={toggleMobileMenu}\n              PaperProps={{\n                sx: {\n                  width: 240,\n                  background: 'linear-gradient(135deg, #3A86FF 0%, #4CC9F0 100%)',\n                  color: '#fff'\n                }\n              }}\n            >\n              <Box sx={{ p: 2 }}>\n                <Typography variant=\"h6\" sx={{ fontWeight: 600, mb: 2 }}>\n                  Menu\n                </Typography>\n                <Divider sx={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />\n                <List>\n                  <ListItem\n                    button\n                    sx={{\n                      borderRadius: 2,\n                      mb: 1,\n                      backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                      border: '2px solid rgba(26, 32, 44, 0.2)',\n                      backdropFilter: 'blur(10px)',\n                      transition: 'all 0.3s ease',\n                      '&:hover': {\n                        backgroundColor: 'rgba(255, 255, 255, 1)',\n                        border: '2px solid #1a202c',\n                        transform: 'translateX(6px)',\n                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'\n                      }\n                    }}\n                  >\n                    <ListItemIcon sx={{ color: '#1a202c' }}>\n                      <HomeIcon />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary=\"Home\"\n                      sx={{\n                        '& .MuiListItemText-primary': {\n                          color: '#1a202c',\n                          fontWeight: 700,\n                          fontSize: '16px'\n                        }\n                      }}\n                    />\n                  </ListItem>\n                  <ListItem\n                    button\n                    sx={{\n                      borderRadius: 2,\n                      mb: 1,\n                      backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                      border: '2px solid rgba(26, 32, 44, 0.2)',\n                      backdropFilter: 'blur(10px)',\n                      transition: 'all 0.3s ease',\n                      '&:hover': {\n                        backgroundColor: 'rgba(255, 255, 255, 1)',\n                        border: '2px solid #1a202c',\n                        transform: 'translateX(6px)',\n                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'\n                      }\n                    }}\n                  >\n                    <ListItemIcon sx={{ color: '#1a202c' }}>\n                      <InfoIcon />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary=\"About\"\n                      sx={{\n                        '& .MuiListItemText-primary': {\n                          color: '#1a202c',\n                          fontWeight: 700,\n                          fontSize: '16px'\n                        }\n                      }}\n                    />\n                  </ListItem>\n                  <ListItem\n                    button\n                    sx={{\n                      borderRadius: 2,\n                      backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                      border: '2px solid rgba(26, 32, 44, 0.2)',\n                      backdropFilter: 'blur(10px)',\n                      transition: 'all 0.3s ease',\n                      '&:hover': {\n                        backgroundColor: 'rgba(255, 255, 255, 1)',\n                        border: '2px solid #1a202c',\n                        transform: 'translateX(6px)',\n                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.2)'\n                      }\n                    }}\n                  >\n                    <ListItemIcon sx={{ color: '#1a202c' }}>\n                      <DescriptionIcon />\n                    </ListItemIcon>\n                    <ListItemText\n                      primary=\"Documentation\"\n                      sx={{\n                        '& .MuiListItemText-primary': {\n                          color: '#1a202c',\n                          fontWeight: 700,\n                          fontSize: '16px'\n                        }\n                      }}\n                    />\n                  </ListItem>\n                </List>\n              </Box>\n            </Drawer>\n          </>\n        ) : (\n          <animated.div style={buttonAnimation}>\n            <Box>\n              <Button\n                sx={{\n                  mx: 1,\n                  fontWeight: 700,\n                  borderRadius: 2,\n                  px: 3,\n                  py: 1,\n                  color: '#1a202c',\n                  backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                  border: '2px solid rgba(26, 32, 44, 0.2)',\n                  backdropFilter: 'blur(10px)',\n                  textShadow: 'none',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    backgroundColor: 'rgba(255, 255, 255, 1)',\n                    border: '2px solid #1a202c',\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.25)',\n                    color: '#1a202c'\n                  },\n                  '&:active': {\n                    transform: 'translateY(0px)',\n                    backgroundColor: 'rgba(240, 244, 248, 1)'\n                  }\n                }}\n                startIcon={<HomeIcon sx={{ color: '#1a202c', fontSize: '20px' }} />}\n              >\n                Home\n              </Button>\n              <Button\n                sx={{\n                  mx: 1,\n                  fontWeight: 700,\n                  borderRadius: 2,\n                  px: 3,\n                  py: 1,\n                  color: '#1a202c',\n                  backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                  border: '2px solid rgba(26, 32, 44, 0.2)',\n                  backdropFilter: 'blur(10px)',\n                  textShadow: 'none',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    backgroundColor: 'rgba(255, 255, 255, 1)',\n                    border: '2px solid #1a202c',\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.25)',\n                    color: '#1a202c'\n                  },\n                  '&:active': {\n                    transform: 'translateY(0px)',\n                    backgroundColor: 'rgba(240, 244, 248, 1)'\n                  }\n                }}\n                startIcon={<InfoIcon sx={{ color: '#1a202c', fontSize: '20px' }} />}\n              >\n                About\n              </Button>\n              <Button\n                sx={{\n                  mx: 1,\n                  fontWeight: 700,\n                  borderRadius: 2,\n                  px: 3,\n                  py: 1,\n                  color: '#1a202c',\n                  backgroundColor: 'rgba(255, 255, 255, 0.95)',\n                  border: '2px solid rgba(26, 32, 44, 0.2)',\n                  backdropFilter: 'blur(10px)',\n                  textShadow: 'none',\n                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',\n                  transition: 'all 0.3s ease',\n                  '&:hover': {\n                    backgroundColor: 'rgba(255, 255, 255, 1)',\n                    border: '2px solid #1a202c',\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 6px 16px rgba(0, 0, 0, 0.25)',\n                    color: '#1a202c'\n                  },\n                  '&:active': {\n                    transform: 'translateY(0px)',\n                    backgroundColor: 'rgba(240, 244, 248, 1)'\n                  }\n                }}\n                startIcon={<DescriptionIcon sx={{ color: '#1a202c', fontSize: '20px' }} />}\n                onClick={handleMenuClick}\n              >\n                Documentation\n              </Button>\n              <Menu\n                anchorEl={anchorEl}\n                open={Boolean(anchorEl)}\n                onClose={handleMenuClose}\n                PaperProps={{\n                  elevation: 8,\n                  sx: {\n                    borderRadius: 3,\n                    mt: 1,\n                    background: 'rgba(255, 255, 255, 0.98)',\n                    backdropFilter: 'blur(20px)',\n                    border: '2px solid rgba(26, 32, 44, 0.2)',\n                    boxShadow: '0 12px 32px rgba(0,0,0,0.2)',\n                    '& .MuiMenuItem-root': {\n                      color: '#1a202c',\n                      fontWeight: 600,\n                      padding: '12px 20px',\n                      borderRadius: 2,\n                      margin: '4px 8px',\n                      transition: 'all 0.3s ease',\n                      fontSize: '15px',\n                      '&:hover': {\n                        backgroundColor: 'rgba(58, 134, 255, 0.1)',\n                        transform: 'translateX(4px)',\n                        boxShadow: '0 4px 12px rgba(58, 134, 255, 0.2)',\n                        border: '1px solid rgba(58, 134, 255, 0.3)'\n                      }\n                    }\n                  }\n                }}\n              >\n                <MenuItem onClick={handleMenuClose}>API Documentation</MenuItem>\n                <MenuItem onClick={handleMenuClose}>User Guide</MenuItem>\n                <MenuItem onClick={handleMenuClose}>Model Information</MenuItem>\n              </Menu>\n            </Box>\n          </animated.div>\n        )}\n      </Toolbar>\n    </AppBar>\n  );\n};\n\nexport default Header;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OACEC,MAAM,CACNC,OAAO,CACPC,UAAU,CACVC,GAAG,CACHC,MAAM,CACNC,QAAQ,CACRC,aAAa,CACbC,UAAU,CACVC,IAAI,CACJC,QAAQ,CACRC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,OAAO,KACF,eAAe,CACtB,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,OAASC,SAAS,CAAEC,QAAQ,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAEnD,KAAM,CAAAC,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAACC,WAAW,CAAEC,cAAc,CAAC,CAAGjC,QAAQ,CAAC,KAAK,CAAC,CAErD,KAAM,CAAAkC,aAAa,CAAGX,SAAS,CAAC,CAC9BY,SAAS,CAAEH,WAAW,CAAG,kBAAkB,CAAG,iBAAiB,CAC/DI,MAAM,CAAE,CAAEC,OAAO,CAAE,GAAG,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACtCC,IAAI,CAAE,CAAEC,OAAO,CAAE,IAAK,CACxB,CAAC,CAAC,CAEFvC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwC,QAAQ,CAAGC,WAAW,CAAC,IAAM,CACjCT,cAAc,CAACU,IAAI,EAAI,CAACA,IAAI,CAAC,CAC/B,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAMC,aAAa,CAACH,QAAQ,CAAC,CACtC,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEf,IAAA,CAACF,QAAQ,CAACqB,GAAG,EAACC,KAAK,CAAEZ,aAAc,CAAAa,QAAA,cACjCrB,IAAA,CAACR,aAAa,EACZ8B,EAAE,CAAE,CACFC,QAAQ,CAAE,EAAE,CACZC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,8CACV,CAAE,CACH,CAAC,CACU,CAAC,CAEnB,CAAC,CAED,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAAC,KAAK,CAAG9C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA+C,QAAQ,CAAG9C,aAAa,CAAC6C,KAAK,CAACE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAC5D,KAAM,CAACC,cAAc,CAAEC,iBAAiB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAAC2D,QAAQ,CAAEC,WAAW,CAAC,CAAG5D,QAAQ,CAAC,IAAI,CAAC,CAE9C,KAAM,CAAA6D,eAAe,CAAIC,KAAK,EAAK,CACjCF,WAAW,CAACE,KAAK,CAACC,aAAa,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5BJ,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED,KAAM,CAAAK,gBAAgB,CAAGA,CAAA,GAAM,CAC7BP,iBAAiB,CAAC,CAACD,cAAc,CAAC,CACpC,CAAC,CAED,KAAM,CAAAS,aAAa,CAAG3C,SAAS,CAAC,CAC9B4C,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEjC,SAAS,CAAE,mBAAoB,CAAC,CACpDkC,EAAE,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEjC,SAAS,CAAE,eAAgB,CAAC,CAC9CC,MAAM,CAAE,CAAEC,OAAO,CAAE,GAAG,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACtCgC,KAAK,CAAE,GACT,CAAC,CAAC,CAEF,KAAM,CAAAC,eAAe,CAAGhD,SAAS,CAAC,CAChC4C,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEjC,SAAS,CAAE,mBAAoB,CAAC,CACpDkC,EAAE,CAAE,CAAED,OAAO,CAAE,CAAC,CAAEjC,SAAS,CAAE,eAAgB,CAAC,CAC9CC,MAAM,CAAE,CAAEC,OAAO,CAAE,GAAG,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACtCgC,KAAK,CAAE,GACT,CAAC,CAAC,CAEF,mBACE5C,IAAA,CAACxB,MAAM,EACLsE,QAAQ,CAAC,QAAQ,CACjBC,SAAS,CAAE,CAAE,CACbzB,EAAE,CAAE,CACF0B,UAAU,CAAE,kDAAkD,CAC9DC,SAAS,CAAE,qCACb,CAAE,CAAA5B,QAAA,cAEFnB,KAAA,CAACzB,OAAO,EAAC6C,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAE,CAAE,CAAA7B,QAAA,eACrBrB,IAAA,CAACF,QAAQ,CAACqB,GAAG,EAACC,KAAK,CAAEoB,aAAc,CAAAnB,QAAA,cACjCnB,KAAA,CAACvB,GAAG,EAAC2C,EAAE,CAAE,CAAE6B,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA/B,QAAA,eACjDrB,IAAA,CAACrB,GAAG,EAAC2C,EAAE,CAAE,CAAE+B,EAAE,CAAE,CAAC,CAAEF,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA/B,QAAA,cACxDrB,IAAA,CAACK,iBAAiB,GAAE,CAAC,CAClB,CAAC,cACNL,IAAA,CAACtB,UAAU,EACT4E,OAAO,CAAC,IAAI,CACZC,SAAS,CAAC,KAAK,CACfjC,EAAE,CAAE,CACFkC,QAAQ,CAAE,CAAC,CACXC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,OAAO,CACtBC,UAAU,CAAE,2BACd,CAAE,CAAAtC,QAAA,CACH,uBAED,CAAY,CAAC,EACV,CAAC,CACM,CAAC,cAEfrB,IAAA,CAACrB,GAAG,EAAC2C,EAAE,CAAE,CAAEkC,QAAQ,CAAE,CAAE,CAAE,CAAE,CAAC,CAE3B5B,QAAQ,cACP1B,KAAA,CAAAE,SAAA,EAAAiB,QAAA,eACErB,IAAA,CAACjB,UAAU,EACTyC,KAAK,CAAC,SAAS,CACfoC,IAAI,CAAC,KAAK,CACVC,OAAO,CAAEtB,gBAAiB,CAC1BjB,EAAE,CAAE,CACFwC,EAAE,CAAE,CAAC,CACL,SAAS,CAAE,CACTd,UAAU,CAAE,uBACd,CACF,CAAE,CAAA3B,QAAA,cAEFrB,IAAA,CAACP,QAAQ,GAAE,CAAC,CACF,CAAC,cAEbO,IAAA,CAACd,MAAM,EACL6E,MAAM,CAAC,OAAO,CACdC,IAAI,CAAEjC,cAAe,CACrBkC,OAAO,CAAE1B,gBAAiB,CAC1B2B,UAAU,CAAE,CACV5C,EAAE,CAAE,CACF6C,KAAK,CAAE,GAAG,CACVnB,UAAU,CAAE,mDAAmD,CAC/DxB,KAAK,CAAE,MACT,CACF,CAAE,CAAAH,QAAA,cAEFnB,KAAA,CAACvB,GAAG,EAAC2C,EAAE,CAAE,CAAE8C,CAAC,CAAE,CAAE,CAAE,CAAA/C,QAAA,eAChBrB,IAAA,CAACtB,UAAU,EAAC4E,OAAO,CAAC,IAAI,CAAChC,EAAE,CAAE,CAAEmC,UAAU,CAAE,GAAG,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAhD,QAAA,CAAC,MAEzD,CAAY,CAAC,cACbrB,IAAA,CAACT,OAAO,EAAC+B,EAAE,CAAE,CAAEgD,eAAe,CAAE,uBAAwB,CAAE,CAAE,CAAC,cAC7DpE,KAAA,CAACf,IAAI,EAAAkC,QAAA,eACHnB,KAAA,CAACd,QAAQ,EACPmF,MAAM,MACNjD,EAAE,CAAE,CACFkD,YAAY,CAAE,CAAC,CACfH,EAAE,CAAE,CAAC,CACLC,eAAe,CAAE,2BAA2B,CAC5CG,MAAM,CAAE,iCAAiC,CACzCC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTL,eAAe,CAAE,wBAAwB,CACzCG,MAAM,CAAE,mBAAmB,CAC3BhE,SAAS,CAAE,iBAAiB,CAC5BwC,SAAS,CAAE,+BACb,CACF,CAAE,CAAA5B,QAAA,eAEFrB,IAAA,CAACX,YAAY,EAACiC,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,cACrCrB,IAAA,CAACJ,QAAQ,GAAE,CAAC,CACA,CAAC,cACfI,IAAA,CAACV,YAAY,EACXsF,OAAO,CAAC,MAAM,CACdtD,EAAE,CAAE,CACF,4BAA4B,CAAE,CAC5BE,KAAK,CAAE,SAAS,CAChBiC,UAAU,CAAE,GAAG,CACflC,QAAQ,CAAE,MACZ,CACF,CAAE,CACH,CAAC,EACM,CAAC,cACXrB,KAAA,CAACd,QAAQ,EACPmF,MAAM,MACNjD,EAAE,CAAE,CACFkD,YAAY,CAAE,CAAC,CACfH,EAAE,CAAE,CAAC,CACLC,eAAe,CAAE,2BAA2B,CAC5CG,MAAM,CAAE,iCAAiC,CACzCC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTL,eAAe,CAAE,wBAAwB,CACzCG,MAAM,CAAE,mBAAmB,CAC3BhE,SAAS,CAAE,iBAAiB,CAC5BwC,SAAS,CAAE,+BACb,CACF,CAAE,CAAA5B,QAAA,eAEFrB,IAAA,CAACX,YAAY,EAACiC,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,cACrCrB,IAAA,CAACN,QAAQ,GAAE,CAAC,CACA,CAAC,cACfM,IAAA,CAACV,YAAY,EACXsF,OAAO,CAAC,OAAO,CACftD,EAAE,CAAE,CACF,4BAA4B,CAAE,CAC5BE,KAAK,CAAE,SAAS,CAChBiC,UAAU,CAAE,GAAG,CACflC,QAAQ,CAAE,MACZ,CACF,CAAE,CACH,CAAC,EACM,CAAC,cACXrB,KAAA,CAACd,QAAQ,EACPmF,MAAM,MACNjD,EAAE,CAAE,CACFkD,YAAY,CAAE,CAAC,CACfF,eAAe,CAAE,2BAA2B,CAC5CG,MAAM,CAAE,iCAAiC,CACzCC,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTL,eAAe,CAAE,wBAAwB,CACzCG,MAAM,CAAE,mBAAmB,CAC3BhE,SAAS,CAAE,iBAAiB,CAC5BwC,SAAS,CAAE,+BACb,CACF,CAAE,CAAA5B,QAAA,eAEFrB,IAAA,CAACX,YAAY,EAACiC,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAU,CAAE,CAAAH,QAAA,cACrCrB,IAAA,CAACL,eAAe,GAAE,CAAC,CACP,CAAC,cACfK,IAAA,CAACV,YAAY,EACXsF,OAAO,CAAC,eAAe,CACvBtD,EAAE,CAAE,CACF,4BAA4B,CAAE,CAC5BE,KAAK,CAAE,SAAS,CAChBiC,UAAU,CAAE,GAAG,CACflC,QAAQ,CAAE,MACZ,CACF,CAAE,CACH,CAAC,EACM,CAAC,EACP,CAAC,EACJ,CAAC,CACA,CAAC,EACT,CAAC,cAEHvB,IAAA,CAACF,QAAQ,CAACqB,GAAG,EAACC,KAAK,CAAEyB,eAAgB,CAAAxB,QAAA,cACnCnB,KAAA,CAACvB,GAAG,EAAA0C,QAAA,eACFrB,IAAA,CAACpB,MAAM,EACL0C,EAAE,CAAE,CACFuD,EAAE,CAAE,CAAC,CACLpB,UAAU,CAAE,GAAG,CACfe,YAAY,CAAE,CAAC,CACfM,EAAE,CAAE,CAAC,CACL5B,EAAE,CAAE,CAAC,CACL1B,KAAK,CAAE,SAAS,CAChB8C,eAAe,CAAE,2BAA2B,CAC5CG,MAAM,CAAE,iCAAiC,CACzCC,cAAc,CAAE,YAAY,CAC5Bf,UAAU,CAAE,MAAM,CAClBV,SAAS,CAAE,+BAA+B,CAC1C0B,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTL,eAAe,CAAE,wBAAwB,CACzCG,MAAM,CAAE,mBAAmB,CAC3BhE,SAAS,CAAE,kBAAkB,CAC7BwC,SAAS,CAAE,gCAAgC,CAC3CzB,KAAK,CAAE,SACT,CAAC,CACD,UAAU,CAAE,CACVf,SAAS,CAAE,iBAAiB,CAC5B6D,eAAe,CAAE,wBACnB,CACF,CAAE,CACFS,SAAS,cAAE/E,IAAA,CAACJ,QAAQ,EAAC0B,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAS,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAE,CAAAF,QAAA,CACrE,MAED,CAAQ,CAAC,cACTrB,IAAA,CAACpB,MAAM,EACL0C,EAAE,CAAE,CACFuD,EAAE,CAAE,CAAC,CACLpB,UAAU,CAAE,GAAG,CACfe,YAAY,CAAE,CAAC,CACfM,EAAE,CAAE,CAAC,CACL5B,EAAE,CAAE,CAAC,CACL1B,KAAK,CAAE,SAAS,CAChB8C,eAAe,CAAE,2BAA2B,CAC5CG,MAAM,CAAE,iCAAiC,CACzCC,cAAc,CAAE,YAAY,CAC5Bf,UAAU,CAAE,MAAM,CAClBV,SAAS,CAAE,+BAA+B,CAC1C0B,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTL,eAAe,CAAE,wBAAwB,CACzCG,MAAM,CAAE,mBAAmB,CAC3BhE,SAAS,CAAE,kBAAkB,CAC7BwC,SAAS,CAAE,gCAAgC,CAC3CzB,KAAK,CAAE,SACT,CAAC,CACD,UAAU,CAAE,CACVf,SAAS,CAAE,iBAAiB,CAC5B6D,eAAe,CAAE,wBACnB,CACF,CAAE,CACFS,SAAS,cAAE/E,IAAA,CAACN,QAAQ,EAAC4B,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAS,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAE,CAAAF,QAAA,CACrE,OAED,CAAQ,CAAC,cACTrB,IAAA,CAACpB,MAAM,EACL0C,EAAE,CAAE,CACFuD,EAAE,CAAE,CAAC,CACLpB,UAAU,CAAE,GAAG,CACfe,YAAY,CAAE,CAAC,CACfM,EAAE,CAAE,CAAC,CACL5B,EAAE,CAAE,CAAC,CACL1B,KAAK,CAAE,SAAS,CAChB8C,eAAe,CAAE,2BAA2B,CAC5CG,MAAM,CAAE,iCAAiC,CACzCC,cAAc,CAAE,YAAY,CAC5Bf,UAAU,CAAE,MAAM,CAClBV,SAAS,CAAE,+BAA+B,CAC1C0B,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTL,eAAe,CAAE,wBAAwB,CACzCG,MAAM,CAAE,mBAAmB,CAC3BhE,SAAS,CAAE,kBAAkB,CAC7BwC,SAAS,CAAE,gCAAgC,CAC3CzB,KAAK,CAAE,SACT,CAAC,CACD,UAAU,CAAE,CACVf,SAAS,CAAE,iBAAiB,CAC5B6D,eAAe,CAAE,wBACnB,CACF,CAAE,CACFS,SAAS,cAAE/E,IAAA,CAACL,eAAe,EAAC2B,EAAE,CAAE,CAAEE,KAAK,CAAE,SAAS,CAAED,QAAQ,CAAE,MAAO,CAAE,CAAE,CAAE,CAC3EsC,OAAO,CAAE1B,eAAgB,CAAAd,QAAA,CAC1B,eAED,CAAQ,CAAC,cACTnB,KAAA,CAAClB,IAAI,EACHiD,QAAQ,CAAEA,QAAS,CACnB+B,IAAI,CAAEgB,OAAO,CAAC/C,QAAQ,CAAE,CACxBgC,OAAO,CAAE3B,eAAgB,CACzB4B,UAAU,CAAE,CACVnB,SAAS,CAAE,CAAC,CACZzB,EAAE,CAAE,CACFkD,YAAY,CAAE,CAAC,CACfS,EAAE,CAAE,CAAC,CACLjC,UAAU,CAAE,2BAA2B,CACvC0B,cAAc,CAAE,YAAY,CAC5BD,MAAM,CAAE,iCAAiC,CACzCxB,SAAS,CAAE,6BAA6B,CACxC,qBAAqB,CAAE,CACrBzB,KAAK,CAAE,SAAS,CAChBiC,UAAU,CAAE,GAAG,CACfyB,OAAO,CAAE,WAAW,CACpBV,YAAY,CAAE,CAAC,CACfW,MAAM,CAAE,SAAS,CACjBR,UAAU,CAAE,eAAe,CAC3BpD,QAAQ,CAAE,MAAM,CAChB,SAAS,CAAE,CACT+C,eAAe,CAAE,yBAAyB,CAC1C7D,SAAS,CAAE,iBAAiB,CAC5BwC,SAAS,CAAE,oCAAoC,CAC/CwB,MAAM,CAAE,mCACV,CACF,CACF,CACF,CAAE,CAAApD,QAAA,eAEFrB,IAAA,CAACf,QAAQ,EAAC4E,OAAO,CAAEvB,eAAgB,CAAAjB,QAAA,CAAC,mBAAiB,CAAU,CAAC,cAChErB,IAAA,CAACf,QAAQ,EAAC4E,OAAO,CAAEvB,eAAgB,CAAAjB,QAAA,CAAC,YAAU,CAAU,CAAC,cACzDrB,IAAA,CAACf,QAAQ,EAAC4E,OAAO,CAAEvB,eAAgB,CAAAjB,QAAA,CAAC,mBAAiB,CAAU,CAAC,EAC5D,CAAC,EACJ,CAAC,CACM,CACf,EACM,CAAC,CACJ,CAAC,CAEb,CAAC,CAED,cAAe,CAAAK,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}