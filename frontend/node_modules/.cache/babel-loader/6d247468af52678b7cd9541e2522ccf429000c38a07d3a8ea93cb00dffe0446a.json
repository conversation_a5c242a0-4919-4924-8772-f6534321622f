{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Downloads/Flood/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{Box,TextField,MenuItem,Button,Grid,Slider,Typography,FormControl,InputLabel,Select,CircularProgress,Alert,Snackbar}from'@mui/material';import SendIcon from'@mui/icons-material/Send';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const PredictionForm=_ref=>{let{options,onSubmit,loading}=_ref;const[formData,setFormData]=useState({rainfall:150,temperature:30,humidity:85,discharge:800,water_level:6.5,elevation:150,land_cover:'',soil_type:'',population_density:1200,infrastructure:'Yes',historical_floods:'No'});const[validationAlert,setValidationAlert]=useState({open:false,message:'',severity:'warning'});// Define validation limits for each field\nconst fieldLimits={rainfall:{min:0,max:500},temperature:{min:0,max:50},humidity:{min:0,max:100},discharge:{min:0,max:2000},water_level:{min:0,max:15},elevation:{min:0,max:500},population_density:{min:0,max:10000}};const handleChange=e=>{const{name,value}=e.target;// Convert to number for validation if it's a numeric field\nconst numericValue=parseFloat(value);// Check if this field has validation limits\nif(fieldLimits[name]){const{min,max}=fieldLimits[name];// If value is empty, allow it (user might be typing)\nif(value===''){setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:''}));return;}// If value is not a valid number, don't update\nif(isNaN(numericValue)){return;}// Check if value is outside the allowed range\nif(numericValue<min||numericValue>max){// Show validation alert\nsetValidationAlert({open:true,message:\"\".concat(name.replace('_',' ').toUpperCase(),\": Value must be between \").concat(min,\" and \").concat(max),severity:'warning'});// Clamp the value within the allowed range\nconst clampedValue=Math.min(Math.max(numericValue,min),max);setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:clampedValue}));}else{// Value is within range, update normally\nsetFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:numericValue}));}}else{// For non-numeric fields, update directly\nsetFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));}};const handleCloseAlert=()=>{setValidationAlert(prev=>_objectSpread(_objectSpread({},prev),{},{open:false}));};const handleSliderChange=name=>(e,newValue)=>{// Ensure the slider value is within the defined limits\nif(fieldLimits[name]){const{min,max}=fieldLimits[name];const clampedValue=Math.min(Math.max(newValue,min),max);setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:clampedValue}));}else{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:newValue}));}};const handleSubmit=e=>{e.preventDefault();onSubmit(formData);};return/*#__PURE__*/_jsxs(Box,{component:\"form\",onSubmit:handleSubmit,sx:{mt:{xs:2,sm:3},position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:{xs:3,sm:4,md:5},sx:{'& .MuiGrid-item':{display:'flex',flexDirection:'column'}},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:1.5,sm:2},borderRadius:2,bgcolor:'rgba(58, 134, 255, 0.05)',height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'primary.main',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'primary.light',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83D\\uDCA7\"}),\"Rainfall (mm)\"]}),/*#__PURE__*/_jsx(Slider,{value:formData.rainfall,onChange:handleSliderChange('rainfall'),\"aria-labelledby\":\"rainfall-slider\",valueLabelDisplay:\"auto\",step:10,marks:true,min:0,max:500,sx:{mt:1,mb:1}}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"rainfall\",value:formData.rainfall,onChange:handleChange,type:\"number\",size:\"small\",fullWidth:true,inputProps:{min:0,max:500,step:10,'aria-label':'Rainfall in millimeters'},helperText:\"Range: 0-500 mm\",sx:{mt:'auto'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:1.5,sm:2},borderRadius:2,bgcolor:'rgba(255, 89, 94, 0.05)',height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'secondary.main',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'secondary.main',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83C\\uDF21\\uFE0F\"}),\"Temperature (\\xB0C)\"]}),/*#__PURE__*/_jsx(Slider,{value:formData.temperature,onChange:handleSliderChange('temperature'),\"aria-labelledby\":\"temperature-slider\",valueLabelDisplay:\"auto\",step:1,marks:true,min:0,max:50,sx:{mt:1,mb:1,color:'secondary.main','& .MuiSlider-thumb':{borderColor:'secondary.main'},'& .MuiSlider-track':{background:'linear-gradient(90deg, #FF5A5F 0%, #FF9F1C 100%)'}}}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"temperature\",value:formData.temperature,onChange:handleChange,type:\"number\",size:\"small\",fullWidth:true,inputProps:{min:0,max:50,step:1,'aria-label':'Temperature in Celsius'},helperText:\"Range: 0-50\\xB0C\",sx:{mt:'auto'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:1.5,sm:2},borderRadius:2,bgcolor:'rgba(76, 201, 240, 0.05)',height:'100%',display:'flex',flexDirection:'column'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'info.main',display:'flex',alignItems:'center'},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'info.main',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83D\\uDCA6\"}),\"Humidity (%)\"]}),/*#__PURE__*/_jsx(Slider,{value:formData.humidity,onChange:handleSliderChange('humidity'),\"aria-labelledby\":\"humidity-slider\",valueLabelDisplay:\"auto\",step:5,marks:true,min:0,max:100,sx:{mt:1,mb:1,color:'info.main','& .MuiSlider-thumb':{borderColor:'info.main'},'& .MuiSlider-track':{background:'linear-gradient(90deg, #4CC9F0 0%, #3A86FF 100%)'}}}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"humidity\",value:formData.humidity,onChange:handleChange,type:\"number\",size:\"small\",fullWidth:true,inputProps:{min:0,max:100,step:5,'aria-label':'Humidity percentage'},helperText:\"Range: 0-100%\",sx:{mt:'auto'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(76, 175, 80, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(76, 175, 80, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'success.main',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'success.main',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83C\\uDF0A\"}),\"River Discharge (m\\xB3/s)\"]}),/*#__PURE__*/_jsx(Slider,{value:formData.discharge,onChange:handleSliderChange('discharge'),\"aria-labelledby\":\"discharge-slider\",valueLabelDisplay:\"auto\",step:50,marks:true,min:0,max:2000,sx:{mt:1,mb:2,color:'success.main','& .MuiSlider-thumb':{borderColor:'success.main'},'& .MuiSlider-track':{background:'linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%)'}}}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"discharge\",value:formData.discharge,onChange:handleChange,type:\"number\",size:\"small\",fullWidth:true,inputProps:{min:0,max:2000,step:50,'aria-label':'River discharge in cubic meters per second'},helperText:\"Range: 0-2000 m\\xB3/s\",sx:{mt:'auto'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(33, 150, 243, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(33, 150, 243, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'primary.main',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'primary.main',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83D\\uDCCF\"}),\"Water Level (m)\"]}),/*#__PURE__*/_jsx(Slider,{value:formData.water_level,onChange:handleSliderChange('water_level'),\"aria-labelledby\":\"water-level-slider\",valueLabelDisplay:\"auto\",step:0.1,marks:true,min:0,max:15,sx:{mt:1,mb:2,color:'primary.main','& .MuiSlider-thumb':{borderColor:'primary.main'},'& .MuiSlider-track':{background:'linear-gradient(90deg, #2196F3 0%, #42A5F5 100%)'}}}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"water_level\",value:formData.water_level,onChange:handleChange,type:\"number\",size:\"small\",fullWidth:true,inputProps:{min:0,max:15,step:0.1,'aria-label':'Water level in meters'},helperText:\"Range: 0-15 m\",sx:{mt:'auto'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(156, 39, 176, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(156, 39, 176, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'secondary.dark',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'secondary.dark',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\u26F0\\uFE0F\"}),\"Elevation (m)\"]}),/*#__PURE__*/_jsx(Slider,{value:formData.elevation,onChange:handleSliderChange('elevation'),\"aria-labelledby\":\"elevation-slider\",valueLabelDisplay:\"auto\",step:10,marks:true,min:0,max:500,sx:{mt:1,mb:2,color:'secondary.dark','& .MuiSlider-thumb':{borderColor:'secondary.dark'},'& .MuiSlider-track':{background:'linear-gradient(90deg, #9C27B0 0%, #BA68C8 100%)'}}}),/*#__PURE__*/_jsx(TextField,{margin:\"dense\",name:\"elevation\",value:formData.elevation,onChange:handleChange,type:\"number\",size:\"small\",fullWidth:true,inputProps:{min:0,max:500,step:10,'aria-label':'Elevation in meters'},helperText:\"Range: 0-500 m\",sx:{mt:'auto'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(255, 152, 0, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(255, 152, 0, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'warning.main',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'warning.main',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83C\\uDF31\"}),\"Land Cover\"]}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mt:'auto'},children:[/*#__PURE__*/_jsx(InputLabel,{id:\"land-cover-label\",children:\"Select Land Cover\"}),/*#__PURE__*/_jsx(Select,{labelId:\"land-cover-label\",name:\"land_cover\",value:formData.land_cover,onChange:handleChange,label:\"Select Land Cover\",required:true,sx:{borderRadius:2},children:options.land_cover.map(option=>/*#__PURE__*/_jsx(MenuItem,{value:option,children:option},option))})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(121, 85, 72, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(121, 85, 72, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'#795548',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'#795548',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83C\\uDFD4\\uFE0F\"}),\"Soil Type\"]}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mt:'auto'},children:[/*#__PURE__*/_jsx(InputLabel,{id:\"soil-type-label\",children:\"Select Soil Type\"}),/*#__PURE__*/_jsx(Select,{labelId:\"soil-type-label\",name:\"soil_type\",value:formData.soil_type,onChange:handleChange,label:\"Select Soil Type\",required:true,sx:{borderRadius:2},children:options.soil_type.map(option=>/*#__PURE__*/_jsx(MenuItem,{value:option,children:option},option))})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(103, 58, 183, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(103, 58, 183, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'#673AB7',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'#673AB7',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83D\\uDC65\"}),\"Population Density\"]}),/*#__PURE__*/_jsx(TextField,{fullWidth:true,label:\"Population Density\",name:\"population_density\",value:formData.population_density,onChange:handleChange,type:\"number\",required:true,inputProps:{min:0,max:10000,step:100,'aria-label':'Population density per square kilometer'},helperText:\"Range: 0-10,000 people/km\\xB2\",sx:{mt:'auto','& .MuiOutlinedInput-root':{borderRadius:2}}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(0, 150, 136, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(0, 150, 136, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'#009688',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'#009688',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83C\\uDFD7\\uFE0F\"}),\"Infrastructure Present\"]}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mt:'auto'},children:[/*#__PURE__*/_jsx(InputLabel,{id:\"infrastructure-label\",children:\"Select Infrastructure\"}),/*#__PURE__*/_jsxs(Select,{labelId:\"infrastructure-label\",name:\"infrastructure\",value:formData.infrastructure,onChange:handleChange,label:\"Select Infrastructure\",sx:{borderRadius:2},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"Yes\",children:\"Yes\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"No\",children:\"No\"})]})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:4,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(244, 67, 54, 0.05)',height:'100%',display:'flex',flexDirection:'column',border:'1px solid rgba(244, 67, 54, 0.1)'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",gutterBottom:true,sx:{fontWeight:600,color:'error.main',display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{component:\"span\",sx:{mr:1,display:'inline-flex',bgcolor:'error.main',color:'white',p:0.5,borderRadius:1,fontSize:'0.875rem'},children:\"\\uD83D\\uDCCA\"}),\"Historical Floods\"]}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,sx:{mt:'auto'},children:[/*#__PURE__*/_jsx(InputLabel,{id:\"historical-floods-label\",children:\"Select History\"}),/*#__PURE__*/_jsxs(Select,{labelId:\"historical-floods-label\",name:\"historical_floods\",value:formData.historical_floods,onChange:handleChange,label:\"Select History\",sx:{borderRadius:2},children:[/*#__PURE__*/_jsx(MenuItem,{value:\"Yes\",children:\"Yes\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"No\",children:\"No\"})]})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Box,{sx:{mt:{xs:4,sm:5,md:6},mb:{xs:2,sm:3},position:'relative',display:'flex',justifyContent:'center',p:{xs:2,sm:3},borderRadius:3,bgcolor:'rgba(58, 134, 255, 0.02)',border:'1px solid rgba(58, 134, 255, 0.1)'},children:/*#__PURE__*/_jsx(Button,{type:\"submit\",variant:\"contained\",color:\"primary\",size:\"large\",endIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:24,color:\"inherit\"}):/*#__PURE__*/_jsx(SendIcon,{}),disabled:loading||!formData.land_cover||!formData.soil_type,sx:{py:{xs:2,sm:2.5},px:{xs:6,sm:8,md:10},borderRadius:4,fontSize:{xs:'1.1rem',sm:'1.2rem'},fontWeight:700,boxShadow:'0 12px 24px rgba(58, 134, 255, 0.3)',position:'relative',overflow:'hidden',background:'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',minWidth:{xs:'200px',sm:'250px'},'&::before':{content:'\"\"',position:'absolute',top:0,left:0,width:'100%',height:'100%',background:'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',opacity:0,transition:'opacity 0.3s ease'},'&:hover':{transform:'translateY(-4px)',boxShadow:'0 16px 32px rgba(58, 134, 255, 0.4)','&::before':{opacity:1}},'&:active':{transform:'translateY(1px)',boxShadow:'0 8px 20px rgba(58, 134, 255, 0.4)'},'&:disabled':{background:'linear-gradient(45deg, #ccc 30%, #ddd 90%)',boxShadow:'0 4px 12px rgba(0, 0, 0, 0.1)',transform:'none'},transition:'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)'},children:loading?'Analyzing Data...':'Predict Flood Risk'})})})]}),/*#__PURE__*/_jsx(Snackbar,{open:validationAlert.open,autoHideDuration:4000,onClose:handleCloseAlert,anchorOrigin:{vertical:'top',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseAlert,severity:validationAlert.severity,variant:\"filled\",sx:{width:'100%',fontWeight:600,'& .MuiAlert-icon':{fontSize:'1.2rem'}},children:validationAlert.message})})]});};export default PredictionForm;", "map": {"version": 3, "names": ["React", "useState", "Box", "TextField", "MenuItem", "<PERSON><PERSON>", "Grid", "Slide<PERSON>", "Typography", "FormControl", "InputLabel", "Select", "CircularProgress", "<PERSON><PERSON>", "Snackbar", "SendIcon", "jsx", "_jsx", "jsxs", "_jsxs", "PredictionForm", "_ref", "options", "onSubmit", "loading", "formData", "setFormData", "rainfall", "temperature", "humidity", "discharge", "water_level", "elevation", "land_cover", "soil_type", "population_density", "infrastructure", "historical_floods", "validation<PERSON><PERSON><PERSON>", "setValidationAlert", "open", "message", "severity", "fieldLimits", "min", "max", "handleChange", "e", "name", "value", "target", "numericValue", "parseFloat", "prev", "_objectSpread", "isNaN", "concat", "replace", "toUpperCase", "clampedValue", "Math", "handleCloseAlert", "handleSliderChange", "newValue", "handleSubmit", "preventDefault", "component", "sx", "mt", "xs", "sm", "position", "zIndex", "children", "container", "spacing", "md", "display", "flexDirection", "item", "p", "borderRadius", "bgcolor", "height", "variant", "gutterBottom", "fontWeight", "color", "alignItems", "mr", "fontSize", "onChange", "valueLabelDisplay", "step", "marks", "mb", "margin", "type", "size", "fullWidth", "inputProps", "helperText", "borderColor", "background", "border", "id", "labelId", "label", "required", "map", "option", "justifyContent", "endIcon", "disabled", "py", "px", "boxShadow", "overflow", "min<PERSON><PERSON><PERSON>", "content", "top", "left", "width", "opacity", "transition", "transform", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/PredictionForm.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  TextField,\n  MenuItem,\n  Button,\n  Grid,\n  Slider,\n  Typography,\n  FormControl,\n  InputLabel,\n  Select,\n  CircularProgress,\n  Alert,\n  Snackbar\n} from '@mui/material';\nimport SendIcon from '@mui/icons-material/Send';\n\nconst PredictionForm = ({ options, onSubmit, loading }) => {\n  const [formData, setFormData] = useState({\n    rainfall: 150,\n    temperature: 30,\n    humidity: 85,\n    discharge: 800,\n    water_level: 6.5,\n    elevation: 150,\n    land_cover: '',\n    soil_type: '',\n    population_density: 1200,\n    infrastructure: 'Yes',\n    historical_floods: 'No'\n  });\n\n  const [validationAlert, setValidationAlert] = useState({\n    open: false,\n    message: '',\n    severity: 'warning'\n  });\n\n  // Define validation limits for each field\n  const fieldLimits = {\n    rainfall: { min: 0, max: 500 },\n    temperature: { min: 0, max: 50 },\n    humidity: { min: 0, max: 100 },\n    discharge: { min: 0, max: 2000 },\n    water_level: { min: 0, max: 15 },\n    elevation: { min: 0, max: 500 },\n    population_density: { min: 0, max: 10000 }\n  };\n\n  const handleChange = (e) => {\n    const { name, value } = e.target;\n\n    // Convert to number for validation if it's a numeric field\n    const numericValue = parseFloat(value);\n\n    // Check if this field has validation limits\n    if (fieldLimits[name]) {\n      const { min, max } = fieldLimits[name];\n\n      // If value is empty, allow it (user might be typing)\n      if (value === '') {\n        setFormData(prev => ({ ...prev, [name]: '' }));\n        return;\n      }\n\n      // If value is not a valid number, don't update\n      if (isNaN(numericValue)) {\n        return;\n      }\n\n      // Check if value is outside the allowed range\n      if (numericValue < min || numericValue > max) {\n        // Show validation alert\n        setValidationAlert({\n          open: true,\n          message: `${name.replace('_', ' ').toUpperCase()}: Value must be between ${min} and ${max}`,\n          severity: 'warning'\n        });\n\n        // Clamp the value within the allowed range\n        const clampedValue = Math.min(Math.max(numericValue, min), max);\n        setFormData(prev => ({ ...prev, [name]: clampedValue }));\n      } else {\n        // Value is within range, update normally\n        setFormData(prev => ({ ...prev, [name]: numericValue }));\n      }\n    } else {\n      // For non-numeric fields, update directly\n      setFormData(prev => ({ ...prev, [name]: value }));\n    }\n  };\n\n  const handleCloseAlert = () => {\n    setValidationAlert(prev => ({ ...prev, open: false }));\n  };\n\n  const handleSliderChange = (name) => (e, newValue) => {\n    // Ensure the slider value is within the defined limits\n    if (fieldLimits[name]) {\n      const { min, max } = fieldLimits[name];\n      const clampedValue = Math.min(Math.max(newValue, min), max);\n      setFormData(prev => ({ ...prev, [name]: clampedValue }));\n    } else {\n      setFormData(prev => ({ ...prev, [name]: newValue }));\n    }\n  };\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    onSubmit(formData);\n  };\n\n  return (\n    <Box\n      component=\"form\"\n      onSubmit={handleSubmit}\n      sx={{\n        mt: { xs: 2, sm: 3 },\n        position: 'relative',\n        zIndex: 1\n      }}\n    >\n      <Grid\n        container\n        spacing={{ xs: 3, sm: 4, md: 5 }}\n        sx={{\n          '& .MuiGrid-item': {\n            display: 'flex',\n            flexDirection: 'column'\n          }\n        }}\n      >\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 1.5, sm: 2 },\n            borderRadius: 2,\n            bgcolor: 'rgba(58, 134, 255, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'primary.main',\n                display: 'flex',\n                alignItems: 'center'\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'primary.light',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                💧\n              </Box>\n              Rainfall (mm)\n            </Typography>\n            <Slider\n              value={formData.rainfall}\n              onChange={handleSliderChange('rainfall')}\n              aria-labelledby=\"rainfall-slider\"\n              valueLabelDisplay=\"auto\"\n              step={10}\n              marks\n              min={0}\n              max={500}\n              sx={{ mt: 1, mb: 1 }}\n            />\n            <TextField\n              margin=\"dense\"\n              name=\"rainfall\"\n              value={formData.rainfall}\n              onChange={handleChange}\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              inputProps={{\n                min: 0,\n                max: 500,\n                step: 10,\n                'aria-label': 'Rainfall in millimeters'\n              }}\n              helperText=\"Range: 0-500 mm\"\n              sx={{ mt: 'auto' }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 1.5, sm: 2 },\n            borderRadius: 2,\n            bgcolor: 'rgba(255, 89, 94, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'secondary.main',\n                display: 'flex',\n                alignItems: 'center'\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'secondary.main',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                🌡️\n              </Box>\n              Temperature (°C)\n            </Typography>\n            <Slider\n              value={formData.temperature}\n              onChange={handleSliderChange('temperature')}\n              aria-labelledby=\"temperature-slider\"\n              valueLabelDisplay=\"auto\"\n              step={1}\n              marks\n              min={0}\n              max={50}\n              sx={{\n                mt: 1,\n                mb: 1,\n                color: 'secondary.main',\n                '& .MuiSlider-thumb': {\n                  borderColor: 'secondary.main',\n                },\n                '& .MuiSlider-track': {\n                  background: 'linear-gradient(90deg, #FF5A5F 0%, #FF9F1C 100%)',\n                }\n              }}\n            />\n            <TextField\n              margin=\"dense\"\n              name=\"temperature\"\n              value={formData.temperature}\n              onChange={handleChange}\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              inputProps={{\n                min: 0,\n                max: 50,\n                step: 1,\n                'aria-label': 'Temperature in Celsius'\n              }}\n              helperText=\"Range: 0-50°C\"\n              sx={{ mt: 'auto' }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 1.5, sm: 2 },\n            borderRadius: 2,\n            bgcolor: 'rgba(76, 201, 240, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'info.main',\n                display: 'flex',\n                alignItems: 'center'\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'info.main',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                💦\n              </Box>\n              Humidity (%)\n            </Typography>\n            <Slider\n              value={formData.humidity}\n              onChange={handleSliderChange('humidity')}\n              aria-labelledby=\"humidity-slider\"\n              valueLabelDisplay=\"auto\"\n              step={5}\n              marks\n              min={0}\n              max={100}\n              sx={{\n                mt: 1,\n                mb: 1,\n                color: 'info.main',\n                '& .MuiSlider-thumb': {\n                  borderColor: 'info.main',\n                },\n                '& .MuiSlider-track': {\n                  background: 'linear-gradient(90deg, #4CC9F0 0%, #3A86FF 100%)',\n                }\n              }}\n            />\n            <TextField\n              margin=\"dense\"\n              name=\"humidity\"\n              value={formData.humidity}\n              onChange={handleChange}\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              inputProps={{\n                min: 0,\n                max: 100,\n                step: 5,\n                'aria-label': 'Humidity percentage'\n              }}\n              helperText=\"Range: 0-100%\"\n              sx={{ mt: 'auto' }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(76, 175, 80, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(76, 175, 80, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'success.main',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'success.main',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                🌊\n              </Box>\n              River Discharge (m³/s)\n            </Typography>\n            <Slider\n              value={formData.discharge}\n              onChange={handleSliderChange('discharge')}\n              aria-labelledby=\"discharge-slider\"\n              valueLabelDisplay=\"auto\"\n              step={50}\n              marks\n              min={0}\n              max={2000}\n              sx={{\n                mt: 1,\n                mb: 2,\n                color: 'success.main',\n                '& .MuiSlider-thumb': {\n                  borderColor: 'success.main',\n                },\n                '& .MuiSlider-track': {\n                  background: 'linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%)',\n                }\n              }}\n            />\n            <TextField\n              margin=\"dense\"\n              name=\"discharge\"\n              value={formData.discharge}\n              onChange={handleChange}\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              inputProps={{\n                min: 0,\n                max: 2000,\n                step: 50,\n                'aria-label': 'River discharge in cubic meters per second'\n              }}\n              helperText=\"Range: 0-2000 m³/s\"\n              sx={{ mt: 'auto' }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(33, 150, 243, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(33, 150, 243, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'primary.main',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'primary.main',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                📏\n              </Box>\n              Water Level (m)\n            </Typography>\n            <Slider\n              value={formData.water_level}\n              onChange={handleSliderChange('water_level')}\n              aria-labelledby=\"water-level-slider\"\n              valueLabelDisplay=\"auto\"\n              step={0.1}\n              marks\n              min={0}\n              max={15}\n              sx={{\n                mt: 1,\n                mb: 2,\n                color: 'primary.main',\n                '& .MuiSlider-thumb': {\n                  borderColor: 'primary.main',\n                },\n                '& .MuiSlider-track': {\n                  background: 'linear-gradient(90deg, #2196F3 0%, #42A5F5 100%)',\n                }\n              }}\n            />\n            <TextField\n              margin=\"dense\"\n              name=\"water_level\"\n              value={formData.water_level}\n              onChange={handleChange}\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              inputProps={{\n                min: 0,\n                max: 15,\n                step: 0.1,\n                'aria-label': 'Water level in meters'\n              }}\n              helperText=\"Range: 0-15 m\"\n              sx={{ mt: 'auto' }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(156, 39, 176, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(156, 39, 176, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'secondary.dark',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'secondary.dark',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                ⛰️\n              </Box>\n              Elevation (m)\n            </Typography>\n            <Slider\n              value={formData.elevation}\n              onChange={handleSliderChange('elevation')}\n              aria-labelledby=\"elevation-slider\"\n              valueLabelDisplay=\"auto\"\n              step={10}\n              marks\n              min={0}\n              max={500}\n              sx={{\n                mt: 1,\n                mb: 2,\n                color: 'secondary.dark',\n                '& .MuiSlider-thumb': {\n                  borderColor: 'secondary.dark',\n                },\n                '& .MuiSlider-track': {\n                  background: 'linear-gradient(90deg, #9C27B0 0%, #BA68C8 100%)',\n                }\n              }}\n            />\n            <TextField\n              margin=\"dense\"\n              name=\"elevation\"\n              value={formData.elevation}\n              onChange={handleChange}\n              type=\"number\"\n              size=\"small\"\n              fullWidth\n              inputProps={{\n                min: 0,\n                max: 500,\n                step: 10,\n                'aria-label': 'Elevation in meters'\n              }}\n              helperText=\"Range: 0-500 m\"\n              sx={{ mt: 'auto' }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(255, 152, 0, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(255, 152, 0, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'warning.main',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'warning.main',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                🌱\n              </Box>\n              Land Cover\n            </Typography>\n            <FormControl fullWidth sx={{ mt: 'auto' }}>\n              <InputLabel id=\"land-cover-label\">Select Land Cover</InputLabel>\n              <Select\n                labelId=\"land-cover-label\"\n                name=\"land_cover\"\n                value={formData.land_cover}\n                onChange={handleChange}\n                label=\"Select Land Cover\"\n                required\n                sx={{ borderRadius: 2 }}\n              >\n                {options.land_cover.map((option) => (\n                  <MenuItem key={option} value={option}>\n                    {option}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(121, 85, 72, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(121, 85, 72, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: '#795548',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: '#795548',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                🏔️\n              </Box>\n              Soil Type\n            </Typography>\n            <FormControl fullWidth sx={{ mt: 'auto' }}>\n              <InputLabel id=\"soil-type-label\">Select Soil Type</InputLabel>\n              <Select\n                labelId=\"soil-type-label\"\n                name=\"soil_type\"\n                value={formData.soil_type}\n                onChange={handleChange}\n                label=\"Select Soil Type\"\n                required\n                sx={{ borderRadius: 2 }}\n              >\n                {options.soil_type.map((option) => (\n                  <MenuItem key={option} value={option}>\n                    {option}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(103, 58, 183, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(103, 58, 183, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: '#673AB7',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: '#673AB7',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                👥\n              </Box>\n              Population Density\n            </Typography>\n            <TextField\n              fullWidth\n              label=\"Population Density\"\n              name=\"population_density\"\n              value={formData.population_density}\n              onChange={handleChange}\n              type=\"number\"\n              required\n              inputProps={{\n                min: 0,\n                max: 10000,\n                step: 100,\n                'aria-label': 'Population density per square kilometer'\n              }}\n              helperText=\"Range: 0-10,000 people/km²\"\n              sx={{\n                mt: 'auto',\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: 2\n                }\n              }}\n            />\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(0, 150, 136, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(0, 150, 136, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: '#009688',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: '#009688',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                🏗️\n              </Box>\n              Infrastructure Present\n            </Typography>\n            <FormControl fullWidth sx={{ mt: 'auto' }}>\n              <InputLabel id=\"infrastructure-label\">Select Infrastructure</InputLabel>\n              <Select\n                labelId=\"infrastructure-label\"\n                name=\"infrastructure\"\n                value={formData.infrastructure}\n                onChange={handleChange}\n                label=\"Select Infrastructure\"\n                sx={{ borderRadius: 2 }}\n              >\n                <MenuItem value=\"Yes\">Yes</MenuItem>\n                <MenuItem value=\"No\">No</MenuItem>\n              </Select>\n            </FormControl>\n          </Box>\n        </Grid>\n\n        <Grid item xs={12} sm={6} md={4}>\n          <Box sx={{\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(244, 67, 54, 0.05)',\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            border: '1px solid rgba(244, 67, 54, 0.1)'\n          }}>\n            <Typography\n              variant=\"subtitle1\"\n              gutterBottom\n              sx={{\n                fontWeight: 600,\n                color: 'error.main',\n                display: 'flex',\n                alignItems: 'center',\n                mb: 2\n              }}\n            >\n              <Box\n                component=\"span\"\n                sx={{\n                  mr: 1,\n                  display: 'inline-flex',\n                  bgcolor: 'error.main',\n                  color: 'white',\n                  p: 0.5,\n                  borderRadius: 1,\n                  fontSize: '0.875rem'\n                }}\n              >\n                📊\n              </Box>\n              Historical Floods\n            </Typography>\n            <FormControl fullWidth sx={{ mt: 'auto' }}>\n              <InputLabel id=\"historical-floods-label\">Select History</InputLabel>\n              <Select\n                labelId=\"historical-floods-label\"\n                name=\"historical_floods\"\n                value={formData.historical_floods}\n                onChange={handleChange}\n                label=\"Select History\"\n                sx={{ borderRadius: 2 }}\n              >\n                <MenuItem value=\"Yes\">Yes</MenuItem>\n                <MenuItem value=\"No\">No</MenuItem>\n              </Select>\n            </FormControl>\n          </Box>\n        </Grid>\n\n        <Grid item xs={12}>\n          <Box sx={{\n            mt: { xs: 4, sm: 5, md: 6 },\n            mb: { xs: 2, sm: 3 },\n            position: 'relative',\n            display: 'flex',\n            justifyContent: 'center',\n            p: { xs: 2, sm: 3 },\n            borderRadius: 3,\n            bgcolor: 'rgba(58, 134, 255, 0.02)',\n            border: '1px solid rgba(58, 134, 255, 0.1)'\n          }}>\n            <Button\n              type=\"submit\"\n              variant=\"contained\"\n              color=\"primary\"\n              size=\"large\"\n              endIcon={loading ? <CircularProgress size={24} color=\"inherit\" /> : <SendIcon />}\n              disabled={loading || !formData.land_cover || !formData.soil_type}\n              sx={{\n                py: { xs: 2, sm: 2.5 },\n                px: { xs: 6, sm: 8, md: 10 },\n                borderRadius: 4,\n                fontSize: { xs: '1.1rem', sm: '1.2rem' },\n                fontWeight: 700,\n                boxShadow: '0 12px 24px rgba(58, 134, 255, 0.3)',\n                position: 'relative',\n                overflow: 'hidden',\n                background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',\n                minWidth: { xs: '200px', sm: '250px' },\n                '&::before': {\n                  content: '\"\"',\n                  position: 'absolute',\n                  top: 0,\n                  left: 0,\n                  width: '100%',\n                  height: '100%',\n                  background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',\n                  opacity: 0,\n                  transition: 'opacity 0.3s ease',\n                },\n                '&:hover': {\n                  transform: 'translateY(-4px)',\n                  boxShadow: '0 16px 32px rgba(58, 134, 255, 0.4)',\n                  '&::before': {\n                    opacity: 1,\n                  }\n                },\n                '&:active': {\n                  transform: 'translateY(1px)',\n                  boxShadow: '0 8px 20px rgba(58, 134, 255, 0.4)',\n                },\n                '&:disabled': {\n                  background: 'linear-gradient(45deg, #ccc 30%, #ddd 90%)',\n                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',\n                  transform: 'none'\n                },\n                transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',\n              }}\n            >\n              {loading ? 'Analyzing Data...' : 'Predict Flood Risk'}\n            </Button>\n          </Box>\n        </Grid>\n      </Grid>\n\n      {/* Validation Alert Snackbar */}\n      <Snackbar\n        open={validationAlert.open}\n        autoHideDuration={4000}\n        onClose={handleCloseAlert}\n        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}\n      >\n        <Alert\n          onClose={handleCloseAlert}\n          severity={validationAlert.severity}\n          variant=\"filled\"\n          sx={{\n            width: '100%',\n            fontWeight: 600,\n            '& .MuiAlert-icon': {\n              fontSize: '1.2rem'\n            }\n          }}\n        >\n          {validationAlert.message}\n        </Alert>\n      </Snackbar>\n    </Box>\n  );\n};\n\nexport default PredictionForm;\n"], "mappings": "kIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,SAAS,CACTC,QAAQ,CACRC,MAAM,CACNC,IAAI,CACJC,MAAM,CACNC,UAAU,CACVC,WAAW,CACXC,UAAU,CACVC,MAAM,CACNC,gBAAgB,CAChBC,KAAK,CACLC,QAAQ,KACH,eAAe,CACtB,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhD,KAAM,CAAAC,cAAc,CAAGC,IAAA,EAAoC,IAAnC,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,OAAQ,CAAC,CAAAH,IAAA,CACpD,KAAM,CAACI,QAAQ,CAAEC,WAAW,CAAC,CAAGzB,QAAQ,CAAC,CACvC0B,QAAQ,CAAE,GAAG,CACbC,WAAW,CAAE,EAAE,CACfC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,GAAG,CACdC,WAAW,CAAE,GAAG,CAChBC,SAAS,CAAE,GAAG,CACdC,UAAU,CAAE,EAAE,CACdC,SAAS,CAAE,EAAE,CACbC,kBAAkB,CAAE,IAAI,CACxBC,cAAc,CAAE,KAAK,CACrBC,iBAAiB,CAAE,IACrB,CAAC,CAAC,CAEF,KAAM,CAACC,eAAe,CAAEC,kBAAkB,CAAC,CAAGtC,QAAQ,CAAC,CACrDuC,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,SACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,WAAW,CAAG,CAClBhB,QAAQ,CAAE,CAAEiB,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAI,CAAC,CAC9BjB,WAAW,CAAE,CAAEgB,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,EAAG,CAAC,CAChChB,QAAQ,CAAE,CAAEe,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAI,CAAC,CAC9Bf,SAAS,CAAE,CAAEc,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,IAAK,CAAC,CAChCd,WAAW,CAAE,CAAEa,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,EAAG,CAAC,CAChCb,SAAS,CAAE,CAAEY,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,GAAI,CAAC,CAC/BV,kBAAkB,CAAE,CAAES,GAAG,CAAE,CAAC,CAAEC,GAAG,CAAE,KAAM,CAC3C,CAAC,CAED,KAAM,CAAAC,YAAY,CAAIC,CAAC,EAAK,CAC1B,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAEhC;AACA,KAAM,CAAAC,YAAY,CAAGC,UAAU,CAACH,KAAK,CAAC,CAEtC;AACA,GAAIN,WAAW,CAACK,IAAI,CAAC,CAAE,CACrB,KAAM,CAAEJ,GAAG,CAAEC,GAAI,CAAC,CAAGF,WAAW,CAACK,IAAI,CAAC,CAEtC;AACA,GAAIC,KAAK,GAAK,EAAE,CAAE,CAChBvB,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAG,EAAE,EAAG,CAAC,CAC9C,OACF,CAEA;AACA,GAAIO,KAAK,CAACJ,YAAY,CAAC,CAAE,CACvB,OACF,CAEA;AACA,GAAIA,YAAY,CAAGP,GAAG,EAAIO,YAAY,CAAGN,GAAG,CAAE,CAC5C;AACAN,kBAAkB,CAAC,CACjBC,IAAI,CAAE,IAAI,CACVC,OAAO,IAAAe,MAAA,CAAKR,IAAI,CAACS,OAAO,CAAC,GAAG,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,6BAAAF,MAAA,CAA2BZ,GAAG,UAAAY,MAAA,CAAQX,GAAG,CAAE,CAC3FH,QAAQ,CAAE,SACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAiB,YAAY,CAAGC,IAAI,CAAChB,GAAG,CAACgB,IAAI,CAACf,GAAG,CAACM,YAAY,CAAEP,GAAG,CAAC,CAAEC,GAAG,CAAC,CAC/DnB,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGW,YAAY,EAAG,CAAC,CAC1D,CAAC,IAAM,CACL;AACAjC,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGG,YAAY,EAAG,CAAC,CAC1D,CACF,CAAC,IAAM,CACL;AACAzB,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGC,KAAK,EAAG,CAAC,CACnD,CACF,CAAC,CAED,KAAM,CAAAY,gBAAgB,CAAGA,CAAA,GAAM,CAC7BtB,kBAAkB,CAACc,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAEb,IAAI,CAAE,KAAK,EAAG,CAAC,CACxD,CAAC,CAED,KAAM,CAAAsB,kBAAkB,CAAId,IAAI,EAAK,CAACD,CAAC,CAAEgB,QAAQ,GAAK,CACpD;AACA,GAAIpB,WAAW,CAACK,IAAI,CAAC,CAAE,CACrB,KAAM,CAAEJ,GAAG,CAAEC,GAAI,CAAC,CAAGF,WAAW,CAACK,IAAI,CAAC,CACtC,KAAM,CAAAW,YAAY,CAAGC,IAAI,CAAChB,GAAG,CAACgB,IAAI,CAACf,GAAG,CAACkB,QAAQ,CAAEnB,GAAG,CAAC,CAAEC,GAAG,CAAC,CAC3DnB,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGW,YAAY,EAAG,CAAC,CAC1D,CAAC,IAAM,CACLjC,WAAW,CAAC2B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAAUD,IAAI,MAAE,CAACL,IAAI,EAAGe,QAAQ,EAAG,CAAC,CACtD,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAIjB,CAAC,EAAK,CAC1BA,CAAC,CAACkB,cAAc,CAAC,CAAC,CAClB1C,QAAQ,CAACE,QAAQ,CAAC,CACpB,CAAC,CAED,mBACEN,KAAA,CAACjB,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChB3C,QAAQ,CAAEyC,YAAa,CACvBG,EAAE,CAAE,CACFC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,CACV,CAAE,CAAAC,QAAA,eAEFtD,KAAA,CAACb,IAAI,EACHoE,SAAS,MACTC,OAAO,CAAE,CAAEN,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEM,EAAE,CAAE,CAAE,CAAE,CACjCT,EAAE,CAAE,CACF,iBAAiB,CAAE,CACjBU,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CACF,CAAE,CAAAL,QAAA,eAEFxD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,0BAA0B,CACnCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAL,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,cAAc,CACrBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QACd,CAAE,CAAAf,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,eAAe,CACxBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,gBAER,EAAY,CAAC,cACbxD,IAAA,CAACV,MAAM,EACL0C,KAAK,CAAExB,QAAQ,CAACE,QAAS,CACzBgE,QAAQ,CAAE7B,kBAAkB,CAAC,UAAU,CAAE,CACzC,kBAAgB,iBAAiB,CACjC8B,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,EAAG,CACTC,KAAK,MACLlD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTsB,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAE2B,EAAE,CAAE,CAAE,CAAE,CACtB,CAAC,cACF9E,IAAA,CAACd,SAAS,EACR6F,MAAM,CAAC,OAAO,CACdhD,IAAI,CAAC,UAAU,CACfC,KAAK,CAAExB,QAAQ,CAACE,QAAS,CACzBgE,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,GAAG,CACRgD,IAAI,CAAE,EAAE,CACR,YAAY,CAAE,yBAChB,CAAE,CACFQ,UAAU,CAAC,iBAAiB,CAC5BlC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CACpB,CAAC,EACC,CAAC,CACF,CAAC,cAEPnD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,yBAAyB,CAClCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAL,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,gBAAgB,CACvBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QACd,CAAE,CAAAf,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,gBAAgB,CACzBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,oBAED,CAAK,CAAC,sBAER,EAAY,CAAC,cACbxD,IAAA,CAACV,MAAM,EACL0C,KAAK,CAAExB,QAAQ,CAACG,WAAY,CAC5B+D,QAAQ,CAAE7B,kBAAkB,CAAC,aAAa,CAAE,CAC5C,kBAAgB,oBAAoB,CACpC8B,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,CAAE,CACRC,KAAK,MACLlD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRsB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACL2B,EAAE,CAAE,CAAC,CACLR,KAAK,CAAE,gBAAgB,CACvB,oBAAoB,CAAE,CACpBe,WAAW,CAAE,gBACf,CAAC,CACD,oBAAoB,CAAE,CACpBC,UAAU,CAAE,kDACd,CACF,CAAE,CACH,CAAC,cACFtF,IAAA,CAACd,SAAS,EACR6F,MAAM,CAAC,OAAO,CACdhD,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAExB,QAAQ,CAACG,WAAY,CAC5B+D,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,EAAE,CACPgD,IAAI,CAAE,CAAC,CACP,YAAY,CAAE,wBAChB,CAAE,CACFQ,UAAU,CAAC,kBAAe,CAC1BlC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CACpB,CAAC,EACC,CAAC,CACF,CAAC,cAEPnD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,0BAA0B,CACnCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CAAE,CAAAL,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,WAAW,CAClBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QACd,CAAE,CAAAf,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,WAAW,CACpBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,eAER,EAAY,CAAC,cACbxD,IAAA,CAACV,MAAM,EACL0C,KAAK,CAAExB,QAAQ,CAACI,QAAS,CACzB8D,QAAQ,CAAE7B,kBAAkB,CAAC,UAAU,CAAE,CACzC,kBAAgB,iBAAiB,CACjC8B,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,CAAE,CACRC,KAAK,MACLlD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTsB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACL2B,EAAE,CAAE,CAAC,CACLR,KAAK,CAAE,WAAW,CAClB,oBAAoB,CAAE,CACpBe,WAAW,CAAE,WACf,CAAC,CACD,oBAAoB,CAAE,CACpBC,UAAU,CAAE,kDACd,CACF,CAAE,CACH,CAAC,cACFtF,IAAA,CAACd,SAAS,EACR6F,MAAM,CAAC,OAAO,CACdhD,IAAI,CAAC,UAAU,CACfC,KAAK,CAAExB,QAAQ,CAACI,QAAS,CACzB8D,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,GAAG,CACRgD,IAAI,CAAE,CAAC,CACP,YAAY,CAAE,qBAChB,CAAE,CACFQ,UAAU,CAAC,eAAe,CAC1BlC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CACpB,CAAC,EACC,CAAC,CACF,CAAC,cAEPnD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,yBAAyB,CAClCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,kCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,cAAc,CACrBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,cAAc,CACvBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,4BAER,EAAY,CAAC,cACbxD,IAAA,CAACV,MAAM,EACL0C,KAAK,CAAExB,QAAQ,CAACK,SAAU,CAC1B6D,QAAQ,CAAE7B,kBAAkB,CAAC,WAAW,CAAE,CAC1C,kBAAgB,kBAAkB,CAClC8B,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,EAAG,CACTC,KAAK,MACLlD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,IAAK,CACVsB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACL2B,EAAE,CAAE,CAAC,CACLR,KAAK,CAAE,cAAc,CACrB,oBAAoB,CAAE,CACpBe,WAAW,CAAE,cACf,CAAC,CACD,oBAAoB,CAAE,CACpBC,UAAU,CAAE,kDACd,CACF,CAAE,CACH,CAAC,cACFtF,IAAA,CAACd,SAAS,EACR6F,MAAM,CAAC,OAAO,CACdhD,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACK,SAAU,CAC1B6D,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,IAAI,CACTgD,IAAI,CAAE,EAAE,CACR,YAAY,CAAE,4CAChB,CAAE,CACFQ,UAAU,CAAC,uBAAoB,CAC/BlC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CACpB,CAAC,EACC,CAAC,CACF,CAAC,cAEPnD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,0BAA0B,CACnCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,mCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,cAAc,CACrBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,cAAc,CACvBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,kBAER,EAAY,CAAC,cACbxD,IAAA,CAACV,MAAM,EACL0C,KAAK,CAAExB,QAAQ,CAACM,WAAY,CAC5B4D,QAAQ,CAAE7B,kBAAkB,CAAC,aAAa,CAAE,CAC5C,kBAAgB,oBAAoB,CACpC8B,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,GAAI,CACVC,KAAK,MACLlD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,EAAG,CACRsB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACL2B,EAAE,CAAE,CAAC,CACLR,KAAK,CAAE,cAAc,CACrB,oBAAoB,CAAE,CACpBe,WAAW,CAAE,cACf,CAAC,CACD,oBAAoB,CAAE,CACpBC,UAAU,CAAE,kDACd,CACF,CAAE,CACH,CAAC,cACFtF,IAAA,CAACd,SAAS,EACR6F,MAAM,CAAC,OAAO,CACdhD,IAAI,CAAC,aAAa,CAClBC,KAAK,CAAExB,QAAQ,CAACM,WAAY,CAC5B4D,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,EAAE,CACPgD,IAAI,CAAE,GAAG,CACT,YAAY,CAAE,uBAChB,CAAE,CACFQ,UAAU,CAAC,eAAe,CAC1BlC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CACpB,CAAC,EACC,CAAC,CACF,CAAC,cAEPnD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,0BAA0B,CACnCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,mCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,gBAAgB,CACvBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,gBAAgB,CACzBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,gBAER,EAAY,CAAC,cACbxD,IAAA,CAACV,MAAM,EACL0C,KAAK,CAAExB,QAAQ,CAACO,SAAU,CAC1B2D,QAAQ,CAAE7B,kBAAkB,CAAC,WAAW,CAAE,CAC1C,kBAAgB,kBAAkB,CAClC8B,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,EAAG,CACTC,KAAK,MACLlD,GAAG,CAAE,CAAE,CACPC,GAAG,CAAE,GAAI,CACTsB,EAAE,CAAE,CACFC,EAAE,CAAE,CAAC,CACL2B,EAAE,CAAE,CAAC,CACLR,KAAK,CAAE,gBAAgB,CACvB,oBAAoB,CAAE,CACpBe,WAAW,CAAE,gBACf,CAAC,CACD,oBAAoB,CAAE,CACpBC,UAAU,CAAE,kDACd,CACF,CAAE,CACH,CAAC,cACFtF,IAAA,CAACd,SAAS,EACR6F,MAAM,CAAC,OAAO,CACdhD,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACO,SAAU,CAC1B2D,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbC,IAAI,CAAC,OAAO,CACZC,SAAS,MACTC,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,GAAG,CACRgD,IAAI,CAAE,EAAE,CACR,YAAY,CAAE,qBAChB,CAAE,CACFQ,UAAU,CAAC,gBAAgB,CAC3BlC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CACpB,CAAC,EACC,CAAC,CACF,CAAC,cAEPnD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,yBAAyB,CAClCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,kCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,cAAc,CACrBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,cAAc,CACvBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,aAER,EAAY,CAAC,cACbtD,KAAA,CAACV,WAAW,EAAC0F,SAAS,MAAChC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAAK,QAAA,eACxCxD,IAAA,CAACP,UAAU,EAAC+F,EAAE,CAAC,kBAAkB,CAAAhC,QAAA,CAAC,mBAAiB,CAAY,CAAC,cAChExD,IAAA,CAACN,MAAM,EACL+F,OAAO,CAAC,kBAAkB,CAC1B1D,IAAI,CAAC,YAAY,CACjBC,KAAK,CAAExB,QAAQ,CAACQ,UAAW,CAC3B0D,QAAQ,CAAE7C,YAAa,CACvB6D,KAAK,CAAC,mBAAmB,CACzBC,QAAQ,MACRzC,EAAE,CAAE,CAAEc,YAAY,CAAE,CAAE,CAAE,CAAAR,QAAA,CAEvBnD,OAAO,CAACW,UAAU,CAAC4E,GAAG,CAAEC,MAAM,eAC7B7F,IAAA,CAACb,QAAQ,EAAc6C,KAAK,CAAE6D,MAAO,CAAArC,QAAA,CAClCqC,MAAM,EADMA,MAEL,CACX,CAAC,CACI,CAAC,EACE,CAAC,EACX,CAAC,CACF,CAAC,cAEP7F,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,yBAAyB,CAClCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,kCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,SAAS,CAChBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,SAAS,CAClBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,oBAED,CAAK,CAAC,YAER,EAAY,CAAC,cACbtD,KAAA,CAACV,WAAW,EAAC0F,SAAS,MAAChC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAAK,QAAA,eACxCxD,IAAA,CAACP,UAAU,EAAC+F,EAAE,CAAC,iBAAiB,CAAAhC,QAAA,CAAC,kBAAgB,CAAY,CAAC,cAC9DxD,IAAA,CAACN,MAAM,EACL+F,OAAO,CAAC,iBAAiB,CACzB1D,IAAI,CAAC,WAAW,CAChBC,KAAK,CAAExB,QAAQ,CAACS,SAAU,CAC1ByD,QAAQ,CAAE7C,YAAa,CACvB6D,KAAK,CAAC,kBAAkB,CACxBC,QAAQ,MACRzC,EAAE,CAAE,CAAEc,YAAY,CAAE,CAAE,CAAE,CAAAR,QAAA,CAEvBnD,OAAO,CAACY,SAAS,CAAC2E,GAAG,CAAEC,MAAM,eAC5B7F,IAAA,CAACb,QAAQ,EAAc6C,KAAK,CAAE6D,MAAO,CAAArC,QAAA,CAClCqC,MAAM,EADMA,MAEL,CACX,CAAC,CACI,CAAC,EACE,CAAC,EACX,CAAC,CACF,CAAC,cAEP7F,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,0BAA0B,CACnCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,mCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,SAAS,CAChBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,SAAS,CAClBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,qBAER,EAAY,CAAC,cACbxD,IAAA,CAACd,SAAS,EACRgG,SAAS,MACTQ,KAAK,CAAC,oBAAoB,CAC1B3D,IAAI,CAAC,oBAAoB,CACzBC,KAAK,CAAExB,QAAQ,CAACU,kBAAmB,CACnCwD,QAAQ,CAAE7C,YAAa,CACvBmD,IAAI,CAAC,QAAQ,CACbW,QAAQ,MACRR,UAAU,CAAE,CACVxD,GAAG,CAAE,CAAC,CACNC,GAAG,CAAE,KAAK,CACVgD,IAAI,CAAE,GAAG,CACT,YAAY,CAAE,yCAChB,CAAE,CACFQ,UAAU,CAAC,+BAA4B,CACvClC,EAAE,CAAE,CACFC,EAAE,CAAE,MAAM,CACV,0BAA0B,CAAE,CAC1Ba,YAAY,CAAE,CAChB,CACF,CAAE,CACH,CAAC,EACC,CAAC,CACF,CAAC,cAEPhE,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,yBAAyB,CAClCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,kCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,SAAS,CAChBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,SAAS,CAClBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,oBAED,CAAK,CAAC,yBAER,EAAY,CAAC,cACbtD,KAAA,CAACV,WAAW,EAAC0F,SAAS,MAAChC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAAK,QAAA,eACxCxD,IAAA,CAACP,UAAU,EAAC+F,EAAE,CAAC,sBAAsB,CAAAhC,QAAA,CAAC,uBAAqB,CAAY,CAAC,cACxEtD,KAAA,CAACR,MAAM,EACL+F,OAAO,CAAC,sBAAsB,CAC9B1D,IAAI,CAAC,gBAAgB,CACrBC,KAAK,CAAExB,QAAQ,CAACW,cAAe,CAC/BuD,QAAQ,CAAE7C,YAAa,CACvB6D,KAAK,CAAC,uBAAuB,CAC7BxC,EAAE,CAAE,CAAEc,YAAY,CAAE,CAAE,CAAE,CAAAR,QAAA,eAExBxD,IAAA,CAACb,QAAQ,EAAC6C,KAAK,CAAC,KAAK,CAAAwB,QAAA,CAAC,KAAG,CAAU,CAAC,cACpCxD,IAAA,CAACb,QAAQ,EAAC6C,KAAK,CAAC,IAAI,CAAAwB,QAAA,CAAC,IAAE,CAAU,CAAC,EAC5B,CAAC,EACE,CAAC,EACX,CAAC,CACF,CAAC,cAEPxD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACM,EAAE,CAAE,CAAE,CAAAH,QAAA,cAC9BtD,KAAA,CAACjB,GAAG,EAACiE,EAAE,CAAE,CACPa,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,yBAAyB,CAClCC,MAAM,CAAE,MAAM,CACdN,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvB0B,MAAM,CAAE,kCACV,CAAE,CAAA/B,QAAA,eACAtD,KAAA,CAACX,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBC,YAAY,MACZlB,EAAE,CAAE,CACFmB,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,YAAY,CACnBV,OAAO,CAAE,MAAM,CACfW,UAAU,CAAE,QAAQ,CACpBO,EAAE,CAAE,CACN,CAAE,CAAAtB,QAAA,eAEFxD,IAAA,CAACf,GAAG,EACFgE,SAAS,CAAC,MAAM,CAChBC,EAAE,CAAE,CACFsB,EAAE,CAAE,CAAC,CACLZ,OAAO,CAAE,aAAa,CACtBK,OAAO,CAAE,YAAY,CACrBK,KAAK,CAAE,OAAO,CACdP,CAAC,CAAE,GAAG,CACNC,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,UACZ,CAAE,CAAAjB,QAAA,CACH,cAED,CAAK,CAAC,oBAER,EAAY,CAAC,cACbtD,KAAA,CAACV,WAAW,EAAC0F,SAAS,MAAChC,EAAE,CAAE,CAAEC,EAAE,CAAE,MAAO,CAAE,CAAAK,QAAA,eACxCxD,IAAA,CAACP,UAAU,EAAC+F,EAAE,CAAC,yBAAyB,CAAAhC,QAAA,CAAC,gBAAc,CAAY,CAAC,cACpEtD,KAAA,CAACR,MAAM,EACL+F,OAAO,CAAC,yBAAyB,CACjC1D,IAAI,CAAC,mBAAmB,CACxBC,KAAK,CAAExB,QAAQ,CAACY,iBAAkB,CAClCsD,QAAQ,CAAE7C,YAAa,CACvB6D,KAAK,CAAC,gBAAgB,CACtBxC,EAAE,CAAE,CAAEc,YAAY,CAAE,CAAE,CAAE,CAAAR,QAAA,eAExBxD,IAAA,CAACb,QAAQ,EAAC6C,KAAK,CAAC,KAAK,CAAAwB,QAAA,CAAC,KAAG,CAAU,CAAC,cACpCxD,IAAA,CAACb,QAAQ,EAAC6C,KAAK,CAAC,IAAI,CAAAwB,QAAA,CAAC,IAAE,CAAU,CAAC,EAC5B,CAAC,EACE,CAAC,EACX,CAAC,CACF,CAAC,cAEPxD,IAAA,CAACX,IAAI,EAACyE,IAAI,MAACV,EAAE,CAAE,EAAG,CAAAI,QAAA,cAChBxD,IAAA,CAACf,GAAG,EAACiE,EAAE,CAAE,CACPC,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEM,EAAE,CAAE,CAAE,CAAC,CAC3BmB,EAAE,CAAE,CAAE1B,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBC,QAAQ,CAAE,UAAU,CACpBM,OAAO,CAAE,MAAM,CACfkC,cAAc,CAAE,QAAQ,CACxB/B,CAAC,CAAE,CAAEX,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBW,YAAY,CAAE,CAAC,CACfC,OAAO,CAAE,0BAA0B,CACnCsB,MAAM,CAAE,mCACV,CAAE,CAAA/B,QAAA,cACAxD,IAAA,CAACZ,MAAM,EACL4F,IAAI,CAAC,QAAQ,CACbb,OAAO,CAAC,WAAW,CACnBG,KAAK,CAAC,SAAS,CACfW,IAAI,CAAC,OAAO,CACZc,OAAO,CAAExF,OAAO,cAAGP,IAAA,CAACL,gBAAgB,EAACsF,IAAI,CAAE,EAAG,CAACX,KAAK,CAAC,SAAS,CAAE,CAAC,cAAGtE,IAAA,CAACF,QAAQ,GAAE,CAAE,CACjFkG,QAAQ,CAAEzF,OAAO,EAAI,CAACC,QAAQ,CAACQ,UAAU,EAAI,CAACR,QAAQ,CAACS,SAAU,CACjEiC,EAAE,CAAE,CACF+C,EAAE,CAAE,CAAE7C,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,GAAI,CAAC,CACtB6C,EAAE,CAAE,CAAE9C,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEM,EAAE,CAAE,EAAG,CAAC,CAC5BK,YAAY,CAAE,CAAC,CACfS,QAAQ,CAAE,CAAErB,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACxCgB,UAAU,CAAE,GAAG,CACf8B,SAAS,CAAE,qCAAqC,CAChD7C,QAAQ,CAAE,UAAU,CACpB8C,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,kDAAkD,CAC9De,QAAQ,CAAE,CAAEjD,EAAE,CAAE,OAAO,CAAEC,EAAE,CAAE,OAAQ,CAAC,CACtC,WAAW,CAAE,CACXiD,OAAO,CAAE,IAAI,CACbhD,QAAQ,CAAE,UAAU,CACpBiD,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,MAAM,CACbvC,MAAM,CAAE,MAAM,CACdoB,UAAU,CAAE,4EAA4E,CACxFoB,OAAO,CAAE,CAAC,CACVC,UAAU,CAAE,mBACd,CAAC,CACD,SAAS,CAAE,CACTC,SAAS,CAAE,kBAAkB,CAC7BT,SAAS,CAAE,qCAAqC,CAChD,WAAW,CAAE,CACXO,OAAO,CAAE,CACX,CACF,CAAC,CACD,UAAU,CAAE,CACVE,SAAS,CAAE,iBAAiB,CAC5BT,SAAS,CAAE,oCACb,CAAC,CACD,YAAY,CAAE,CACZb,UAAU,CAAE,4CAA4C,CACxDa,SAAS,CAAE,+BAA+B,CAC1CS,SAAS,CAAE,MACb,CAAC,CACDD,UAAU,CAAE,kDACd,CAAE,CAAAnD,QAAA,CAEDjD,OAAO,CAAG,mBAAmB,CAAG,oBAAoB,CAC/C,CAAC,CACN,CAAC,CACF,CAAC,EACH,CAAC,cAGPP,IAAA,CAACH,QAAQ,EACP0B,IAAI,CAAEF,eAAe,CAACE,IAAK,CAC3BsF,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAElE,gBAAiB,CAC1BmE,YAAY,CAAE,CAAEC,QAAQ,CAAE,KAAK,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAzD,QAAA,cAExDxD,IAAA,CAACJ,KAAK,EACJkH,OAAO,CAAElE,gBAAiB,CAC1BnB,QAAQ,CAAEJ,eAAe,CAACI,QAAS,CACnC0C,OAAO,CAAC,QAAQ,CAChBjB,EAAE,CAAE,CACFuD,KAAK,CAAE,MAAM,CACbpC,UAAU,CAAE,GAAG,CACf,kBAAkB,CAAE,CAClBI,QAAQ,CAAE,QACZ,CACF,CAAE,CAAAjB,QAAA,CAEDnC,eAAe,CAACG,OAAO,CACnB,CAAC,CACA,CAAC,EACR,CAAC,CAEV,CAAC,CAED,cAAe,CAAArB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}