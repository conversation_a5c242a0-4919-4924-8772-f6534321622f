{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Downloads/Flood/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useRef}from'react';import{Box,Typography,Paper,Slider,Grid,Button,CircularProgress,Chip,useTheme,IconButton,Tooltip,Divider}from'@mui/material';import{useSpring,animated}from'react-spring';import{Line}from'react-chartjs-2';import{Chart as ChartJS,CategoryScale,LinearScale,PointElement,LineElement,Title,Tooltip as ChartTooltip,Legend,Filler}from'chart.js';import CalendarTodayIcon from'@mui/icons-material/CalendarToday';import AccessTimeIcon from'@mui/icons-material/AccessTime';import RefreshIcon from'@mui/icons-material/Refresh';import InfoIcon from'@mui/icons-material/Info';import WaterDropIcon from'@mui/icons-material/WaterDrop';import ThunderstormIcon from'@mui/icons-material/Thunderstorm';import LocationOnIcon from'@mui/icons-material/LocationOn';// Import the LocationSelector component\nimport LocationSelector from'./LocationSelector';// Register ChartJS components\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";ChartJS.register(CategoryScale,LinearScale,PointElement,LineElement,Title,ChartTooltip,Legend,Filler);const TimelineRiskPredictor=_ref=>{let{formData,onForecastGenerated}=_ref;const theme=useTheme();const[loading,setLoading]=useState(false);const[forecastData,setForecastData]=useState(null);const[timeRange,setTimeRange]=useState(48);// Default 48 hours\nconst[selectedTimeIndex,setSelectedTimeIndex]=useState(null);const initialForecastGenerated=useRef(false);const[selectedLocation,setSelectedLocation]=useState({name:'Delhi',state:'Delhi',coordinates:[77.2090,28.6139],userCoordinates:null// Will be populated when user shares their location\n});// Animation for the component\nconst fadeIn=useSpring({from:{opacity:0,transform:'translateY(20px)'},to:{opacity:1,transform:'translateY(0)'},config:{tension:280,friction:20},delay:200});// Handle location selection\nconst handleLocationSelect=useCallback(location=>{setSelectedLocation(location);// Reset selected time index when location changes\nsetSelectedTimeIndex(null);// If we already have forecast data, show a message to the user\nif(forecastData){// Clear the forecast data to avoid confusion\nsetForecastData(null);// Reset the initial forecast flag so we don't automatically regenerate\ninitialForecastGenerated.current=true;}},[forecastData]);// Fetch real forecast data from the API\nconst generateForecast=useCallback(()=>{// Check if formData is available and has the required properties\nif(!formData||!formData.rainfall||!formData.water_level||!formData.discharge){console.log(\"Form data is incomplete, cannot generate forecast\");return;}setLoading(true);// Call the weather forecast API\nfetch('/api/weather-forecast',{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify(_objectSpread(_objectSpread(_objectSpread({},formData),{},{hours:timeRange,// Use the selected location\nlocation:\"\".concat(selectedLocation.name,\",\").concat(selectedLocation.state,\",India\")},selectedLocation.userCoordinates&&{latitude:selectedLocation.userCoordinates[1],longitude:selectedLocation.userCoordinates[0]}),!selectedLocation.userCoordinates&&{latitude:selectedLocation.coordinates[1],longitude:selectedLocation.coordinates[0]}))}).then(response=>{if(!response.ok){throw new Error('Network response was not ok');}return response.json();}).then(data=>{var _data$summary,_data$summary2;// Set the forecast data from the API response\nsetForecastData({timestamps:data.timestamps,rainfallData:data.rainfallData,waterLevelData:data.waterLevelData,dischargeData:data.dischargeData,riskScoreData:data.riskScoreData,temperatureData:data.temperatureData,humidityData:data.humidityData,location:(_data$summary=data.summary)===null||_data$summary===void 0?void 0:_data$summary.location,country:(_data$summary2=data.summary)===null||_data$summary2===void 0?void 0:_data$summary2.country});// Notify parent component\nif(onForecastGenerated){onForecastGenerated({maxRiskScore:data.summary.maxRiskScore,maxRiskTime:data.summary.maxRiskTime,riskTrend:data.summary.riskTrend,location:data.summary.location,country:data.summary.country});}setLoading(false);}).catch(error=>{console.error('Error fetching forecast data:',error);setLoading(false);});},[formData,timeRange,selectedLocation,onForecastGenerated]);// Handle time range change\nconst handleTimeRangeChange=(_,newValue)=>{setTimeRange(newValue);};// Handle chart click to select a specific time\nconst handleChartClick=(_,elements)=>{if(elements.length>0){setSelectedTimeIndex(elements[0].index);}};// Prepare chart data\nconst chartData=forecastData?{labels:forecastData.timestamps,datasets:[{label:'Flood Risk Score',data:forecastData.riskScoreData,borderColor:'rgba(58, 134, 255, 0.8)',borderWidth:3,backgroundColor:ctx=>{const gradient=ctx.chart.ctx.createLinearGradient(0,0,0,400);gradient.addColorStop(0,'rgba(76, 201, 240, 0.6)');gradient.addColorStop(1,'rgba(58, 134, 255, 0.05)');return gradient;},fill:true,tension:0.4,pointRadius:ctx=>ctx.dataIndex===selectedTimeIndex?8:4,pointBackgroundColor:ctx=>{const value=ctx.raw;if(value>70)return theme.palette.error.main;if(value>40)return theme.palette.warning.main;return theme.palette.success.main;},pointBorderColor:'#fff',pointBorderWidth:2,pointHoverRadius:8,pointHoverBackgroundColor:ctx=>{const value=ctx.raw;if(value>70)return theme.palette.error.dark;if(value>40)return theme.palette.warning.dark;return theme.palette.success.dark;},pointHoverBorderColor:'#fff',pointHoverBorderWidth:3},// Add rainfall data as a secondary dataset\n{label:'Rainfall (mm)',data:forecastData.rainfallData,borderColor:'rgba(255, 89, 94, 0.7)',borderWidth:2,backgroundColor:'rgba(255, 89, 94, 0.1)',borderDash:[5,5],fill:true,tension:0.4,pointRadius:0,yAxisID:'y1'},// Add temperature data if available\n...(forecastData.temperatureData?[{label:'Temperature (°C)',data:forecastData.temperatureData,borderColor:'rgba(255, 159, 28, 0.7)',borderWidth:2,backgroundColor:'rgba(255, 159, 28, 0.0)',tension:0.4,pointRadius:2,pointBackgroundColor:'rgba(255, 159, 28, 0.7)',pointBorderColor:'rgba(255, 255, 255, 0.8)',pointBorderWidth:1,pointHoverRadius:5,pointHoverBackgroundColor:'rgba(255, 159, 28, 0.9)',pointHoverBorderColor:'#fff',pointHoverBorderWidth:2,yAxisID:'y2',hidden:false// Show by default for better visibility\n}]:[])]}:null;// Chart options\nconst chartOptions={responsive:true,maintainAspectRatio:false,interaction:{mode:'index',intersect:false},animations:{tension:{duration:1000,easing:'easeInOutCubic',from:0.8,to:0.4,loop:false}},plugins:{legend:{position:'top',labels:{usePointStyle:true,padding:15,font:{size:12,weight:'bold'}}},tooltip:{backgroundColor:'rgba(255, 255, 255, 0.9)',titleColor:'#333',bodyColor:'#333',titleFont:{size:14,weight:'bold'},bodyFont:{size:13},padding:12,borderColor:'rgba(58, 134, 255, 0.3)',borderWidth:1,displayColors:true,boxWidth:8,boxHeight:8,boxPadding:4,usePointStyle:true,callbacks:{label:function(context){const value=context.raw;const datasetLabel=context.dataset.label;if(datasetLabel==='Flood Risk Score'){let riskLevel='Low';let emoji='✅';if(value>70){riskLevel='High';emoji='⚠️';}else if(value>40){riskLevel='Medium';emoji='⚠️';}return\"\".concat(emoji,\" Risk Score: \").concat(value.toFixed(1),\" (\").concat(riskLevel,\")\");}else if(datasetLabel==='Rainfall (mm)'){return\"\\u2614 Rainfall: \".concat(value.toFixed(1),\" mm\");}else if(datasetLabel==='Temperature (°C)'){return\"\\uD83C\\uDF21\\uFE0F Temperature: \".concat(value.toFixed(1),\" \\xB0C\");}else if(datasetLabel==='Humidity (%)'){return\"\\uD83D\\uDCA7 Humidity: \".concat(value.toFixed(0),\"%\");}else{return\"\".concat(datasetLabel,\": \").concat(value.toFixed(1));}}}}},scales:{y:{beginAtZero:true,max:100,title:{display:true,text:'Risk Score',font:{size:14,weight:'bold'},color:'rgba(58, 134, 255, 0.8)'},grid:{color:'rgba(0, 0, 0, 0.05)',borderDash:[5,5]},ticks:{font:{size:12},color:'rgba(0, 0, 0, 0.6)'}},y1:{beginAtZero:true,position:'right',max:Math.max(...((forecastData===null||forecastData===void 0?void 0:forecastData.rainfallData)||[0]))*1.2,title:{display:true,text:'Rainfall (mm)',font:{size:14,weight:'bold'},color:'rgba(255, 89, 94, 0.7)'},grid:{display:false},ticks:{font:{size:12},color:'rgba(255, 89, 94, 0.7)'}},y2:{beginAtZero:false,position:'right',min:Math.min(...((forecastData===null||forecastData===void 0?void 0:forecastData.temperatureData)||[20]))-5,max:Math.max(...((forecastData===null||forecastData===void 0?void 0:forecastData.temperatureData)||[30]))+5,title:{display:true,text:'Temperature (°C)',font:{size:14,weight:'bold'},color:'rgba(255, 159, 28, 0.7)'},grid:{display:false},ticks:{font:{size:12},color:'rgba(255, 159, 28, 0.7)'},display:true// Always show the temperature axis\n},x:{title:{display:true,text:'Time',font:{size:14,weight:'bold'}},ticks:{maxRotation:45,minRotation:45,font:{size:11},color:'rgba(0, 0, 0, 0.6)'},grid:{color:'rgba(0, 0, 0, 0.05)'}}},onClick:handleChartClick};// Generate forecast only when the component mounts with valid form data\nuseEffect(()=>{// Only generate forecast if we have valid form data and haven't generated one yet\nif(formData&&formData.rainfall&&formData.water_level&&formData.discharge&&!initialForecastGenerated.current){initialForecastGenerated.current=true;generateForecast();}},[formData,generateForecast]);// Track location changes but don't automatically regenerate forecast\nuseEffect(()=>{// When location changes, we'll just update the UI to show the new location\n// but we won't automatically regenerate the forecast to avoid infinite loops\n// The user can click the \"Generate Forecast\" button to get data for the new location\nif(initialForecastGenerated.current){console.log(\"Location changed to \".concat(selectedLocation.name,\", \").concat(selectedLocation.state));}},[selectedLocation]);return/*#__PURE__*/_jsx(animated.div,{style:fadeIn,children:/*#__PURE__*/_jsxs(Box,{sx:{p:{xs:4,sm:5,md:6},position:'relative'},children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:{xs:4,sm:5},pb:3,borderBottom:'2px solid rgba(58, 134, 255, 0.1)',position:'relative'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-20,right:-20,width:120,height:120,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2,position:'relative',zIndex:1},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:{xs:2,sm:3},p:{xs:1.5,sm:2},borderRadius:3,background:'linear-gradient(135deg, rgba(58, 134, 255, 0.15) 0%, rgba(76, 201, 240, 0.15) 100%)',display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 12px rgba(58, 134, 255, 0.2)'},children:/*#__PURE__*/_jsx(AccessTimeIcon,{sx:{fontSize:{xs:28,sm:32},color:'primary.main'}})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h3\",sx:{fontWeight:800,color:theme.palette.primary.dark,letterSpacing:'-0.8px',mb:0.5},children:[\"Temporal Flood Risk Prediction\",/*#__PURE__*/_jsx(Tooltip,{title:\"This feature predicts how flood risk will change over time based on current conditions and weather forecasts\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",sx:{ml:1,mb:1},children:/*#__PURE__*/_jsx(InfoIcon,{fontSize:\"small\"})})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{color:'text.secondary',fontWeight:500},children:\"Real-time Weather-Based Risk Forecasting\"})]})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{fontSize:{xs:'1rem',sm:'1.1rem'},lineHeight:1.7,color:'text.secondary',maxWidth:'900px',position:'relative',zIndex:1},children:\"Analyze how flood risk evolves over the next hours and days based on your input parameters, real-time weather data, and advanced hydrological modeling.\"})]}),/*#__PURE__*/_jsx(LocationSelector,{onLocationSelect:handleLocationSelect,initialLocation:selectedLocation}),/*#__PURE__*/_jsx(Box,{sx:{mb:{xs:4,sm:5},p:{xs:3,sm:4},borderRadius:3,background:'linear-gradient(135deg, rgba(58, 134, 255, 0.03) 0%, rgba(255,255,255,0.8) 100%)',border:'1px solid rgba(58, 134, 255, 0.1)'},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:{xs:4,sm:5},children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,sm:8,children:[/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:600,color:'primary.dark',mb:1},children:\"Forecast Time Range (hours)\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:2},children:\"Select the prediction timeframe for risk analysis\"})]}),/*#__PURE__*/_jsx(Slider,{value:timeRange,onChange:handleTimeRangeChange,\"aria-labelledby\":\"time-range-slider\",valueLabelDisplay:\"auto\",step:12,marks:[{value:12,label:'12h'},{value:24,label:'24h'},{value:48,label:'48h'},{value:72,label:'72h'}],min:12,max:72,sx:{color:'primary.main',height:8,'& .MuiSlider-thumb':{width:24,height:24,boxShadow:'0 4px 12px rgba(58, 134, 255, 0.3)'},'& .MuiSlider-track':{height:8,borderRadius:4,background:'linear-gradient(90deg, #3A86FF 0%, #4CC9F0 100%)'},'& .MuiSlider-rail':{height:8,borderRadius:4,opacity:0.3},'& .MuiSlider-mark':{backgroundColor:'primary.main',height:12,width:3,borderRadius:2},'& .MuiSlider-markLabel':{fontWeight:600,color:'primary.dark'}}})]}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:4,sx:{display:'flex',alignItems:'center',justifyContent:{xs:'center',sm:'flex-end'}},children:/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:generateForecast,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(RefreshIcon,{}),sx:{py:{xs:1.5,sm:2},px:{xs:4,sm:5},borderRadius:3,fontSize:{xs:'1rem',sm:'1.1rem'},fontWeight:600,boxShadow:'0 8px 20px rgba(58, 134, 255, 0.3)',background:'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)','&:hover':{transform:'translateY(-2px)',boxShadow:'0 12px 28px rgba(58, 134, 255, 0.4)'},'&:disabled':{background:'linear-gradient(45deg, #ccc 30%, #ddd 90%)',transform:'none'},transition:'all 0.3s ease'},children:forecastData?'Refresh Forecast':'Generate Forecast'})})]})}),loading?/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',alignItems:'center',height:300},children:/*#__PURE__*/_jsx(CircularProgress,{})}):forecastData?/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsxs(Box,{sx:{height:350,mb:3,p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(240,245,255,0.9) 100%)',boxShadow:'inset 0 0 15px rgba(58, 134, 255, 0.1)',border:'1px solid rgba(58, 134, 255, 0.2)',position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-20,right:-20,width:100,height:100,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:-30,left:-30,width:150,height:150,borderRadius:'50%',background:'radial-gradient(circle, rgba(255,89,94,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{mb:2,fontWeight:600,color:theme.palette.primary.dark,textAlign:'center',textShadow:'0px 1px 2px rgba(0,0,0,0.05)'},children:\"Temporal Flood Risk Analysis\"}),/*#__PURE__*/_jsx(Line,{data:chartData,options:chartOptions})]}),selectedTimeIndex!==null&&/*#__PURE__*/_jsx(Box,{sx:{mt:2,mb:3},children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:0,overflow:'hidden',borderRadius:3,boxShadow:'0 8px 20px rgba(0, 0, 0, 0.08)'},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:2,background:'linear-gradient(90deg, rgba(58,134,255,0.9) 0%, rgba(76,201,240,0.9) 100%)',color:'white',position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-20,right:-20,width:100,height:100,borderRadius:'50%',background:'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:600,textShadow:'0 1px 2px rgba(0,0,0,0.1)'},children:[/*#__PURE__*/_jsx(CalendarTodayIcon,{fontSize:\"small\",sx:{mr:1,verticalAlign:'middle'}}),forecastData.timestamps[selectedTimeIndex]]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:0.5,opacity:0.9},children:\"Detailed forecast information\"})]}),/*#__PURE__*/_jsx(Box,{sx:{p:{xs:3,sm:4}},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:{xs:3,sm:4},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:\"linear-gradient(135deg, \".concat(forecastData.riskScoreData[selectedTimeIndex]>70?'rgba(255,89,94,0.1)':forecastData.riskScoreData[selectedTimeIndex]>40?'rgba(251,133,0,0.1)':'rgba(6,214,160,0.1)',\" 0%, rgba(255,255,255,0.7) 100%)\"),border:\"1px solid \".concat(forecastData.riskScoreData[selectedTimeIndex]>70?'rgba(255,89,94,0.3)':forecastData.riskScoreData[selectedTimeIndex]>40?'rgba(251,133,0,0.3)':'rgba(6,214,160,0.3)'),height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Risk Score\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:forecastData.riskScoreData[selectedTimeIndex]>70?theme.palette.error.main:forecastData.riskScoreData[selectedTimeIndex]>40?theme.palette.warning.main:theme.palette.success.main},children:forecastData.riskScoreData[selectedTimeIndex].toFixed(1)}),/*#__PURE__*/_jsx(Chip,{label:forecastData.riskScoreData[selectedTimeIndex]>70?'High Risk':forecastData.riskScoreData[selectedTimeIndex]>40?'Medium Risk':'Low Risk',size:\"small\",color:forecastData.riskScoreData[selectedTimeIndex]>70?'error':forecastData.riskScoreData[selectedTimeIndex]>40?'warning':'success',sx:{mt:1,fontWeight:'bold',boxShadow:'0 2px 5px rgba(0,0,0,0.1)'}})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(255,89,94,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(255,89,94,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:[/*#__PURE__*/_jsx(ThunderstormIcon,{fontSize:\"small\",sx:{mr:0.5,verticalAlign:'middle'}}),\"Rainfall\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(255,89,94,0.8)'},children:forecastData.rainfallData[selectedTimeIndex].toFixed(1)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"millimeters\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(76,201,240,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(76,201,240,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:[/*#__PURE__*/_jsx(WaterDropIcon,{fontSize:\"small\",sx:{mr:0.5,verticalAlign:'middle'}}),\"Water Level\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(76,201,240,0.8)'},children:forecastData.waterLevelData[selectedTimeIndex].toFixed(2)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"meters\"})]})}),forecastData.temperatureData&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(255,159,28,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(255,159,28,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Temperature\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(255,159,28,0.8)'},children:forecastData.temperatureData[selectedTimeIndex].toFixed(1)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"\\xB0C\"})]})}),forecastData.humidityData&&/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(111,134,214,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(111,134,214,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Humidity\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(111,134,214,0.8)'},children:forecastData.humidityData[selectedTimeIndex].toFixed(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"%\"})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,background:'linear-gradient(135deg, rgba(58,134,255,0.1) 0%, rgba(255,255,255,0.7) 100%)',border:'1px solid rgba(58,134,255,0.3)',height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',textAlign:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mb:1,fontWeight:500},children:\"Discharge\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:'bold',color:'rgba(58,134,255,0.8)'},children:forecastData.dischargeData[selectedTimeIndex].toFixed(0)}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",sx:{mt:0.5},children:\"m\\xB3/s\"})]})})]})})]})}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3,p:2,borderRadius:2,backgroundColor:'rgba(76, 201, 240, 0.1)',border:'1px dashed rgba(58, 134, 255, 0.3)',display:'flex',alignItems:'flex-start'},children:[/*#__PURE__*/_jsx(InfoIcon,{fontSize:\"small\",sx:{mr:1.5,mt:0.3,color:theme.palette.primary.main}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{color:theme.palette.primary.dark,fontWeight:500,mb:0.5},children:[\"Interactive Forecast Visualization for \",forecastData.location||selectedLocation.name,\", \",selectedLocation.state]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"text.secondary\",children:[\"This forecast is based on real-time weather data and hydrological modeling for your selected location.\",/*#__PURE__*/_jsx(\"strong\",{children:\" Click on any point in the chart\"}),\" to see detailed predictions for that specific time. The blue line shows flood risk score, while the red dashed line shows rainfall intensity.\",forecastData.temperatureData&&' Temperature data is also available (toggle in chart legend).']})]})]})]}):/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',justifyContent:'center',alignItems:'center',height:350,background:'linear-gradient(135deg, rgba(240,245,255,0.8) 0%, rgba(230,240,255,0.8) 100%)',borderRadius:3,border:'1px dashed rgba(58, 134, 255, 0.3)',position:'relative',overflow:'hidden',p:3},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-30,right:-30,width:150,height:150,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:-30,left:-30,width:150,height:150,borderRadius:'50%',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)'}}),/*#__PURE__*/_jsx(AccessTimeIcon,{sx:{fontSize:80,color:'rgba(58, 134, 255, 0.3)',mb:3,filter:'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",color:\"primary.dark\",align:\"center\",sx:{mb:1,fontWeight:600},children:\"Temporal Flood Risk Prediction\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",align:\"center\",sx:{mb:3,maxWidth:450},children:\"Generate a forecast to visualize how flood risk may change over the next hours and days based on current conditions.\"}),/*#__PURE__*/_jsx(Button,{variant:\"contained\",color:\"primary\",onClick:generateForecast,disabled:loading,startIcon:loading?/*#__PURE__*/_jsx(CircularProgress,{size:20,color:\"inherit\"}):/*#__PURE__*/_jsx(RefreshIcon,{}),sx:{px:3,py:1.2,borderRadius:8,boxShadow:'0 4px 14px rgba(58, 134, 255, 0.3)','&:hover':{boxShadow:'0 6px 20px rgba(58, 134, 255, 0.4)',transform:'translateY(-2px)'},transition:'all 0.3s ease'},children:\"Generate Forecast\"})]})]})});};export default TimelineRiskPredictor;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Box", "Typography", "Paper", "Slide<PERSON>", "Grid", "<PERSON><PERSON>", "CircularProgress", "Chip", "useTheme", "IconButton", "<PERSON><PERSON><PERSON>", "Divider", "useSpring", "animated", "Line", "Chart", "ChartJS", "CategoryScale", "LinearScale", "PointElement", "LineElement", "Title", "ChartTooltip", "Legend", "Filler", "CalendarTodayIcon", "AccessTimeIcon", "RefreshIcon", "InfoIcon", "WaterDropIcon", "ThunderstormIcon", "LocationOnIcon", "LocationSelector", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "register", "TimelineRiskPredictor", "_ref", "formData", "onForecastGenerated", "theme", "loading", "setLoading", "forecastData", "setForecastData", "timeRange", "setTimeRange", "selectedTimeIndex", "setSelectedTimeIndex", "initialForecastGenerated", "selectedLocation", "setSelectedLocation", "name", "state", "coordinates", "userCoordinates", "fadeIn", "from", "opacity", "transform", "to", "config", "tension", "friction", "delay", "handleLocationSelect", "location", "current", "generateForecast", "rainfall", "water_level", "discharge", "console", "log", "fetch", "method", "headers", "body", "JSON", "stringify", "_objectSpread", "hours", "concat", "latitude", "longitude", "then", "response", "ok", "Error", "json", "data", "_data$summary", "_data$summary2", "timestamps", "rainfallData", "waterLevelData", "dischargeData", "riskScoreData", "temperatureData", "humidityData", "summary", "country", "maxRiskScore", "maxRiskTime", "riskTrend", "catch", "error", "handleTimeRangeChange", "_", "newValue", "handleChartClick", "elements", "length", "index", "chartData", "labels", "datasets", "label", "borderColor", "borderWidth", "backgroundColor", "ctx", "gradient", "chart", "createLinearGradient", "addColorStop", "fill", "pointRadius", "dataIndex", "pointBackgroundColor", "value", "raw", "palette", "main", "warning", "success", "pointBorderColor", "pointBorderWidth", "pointHoverRadius", "pointHoverBackgroundColor", "dark", "pointHoverBorderColor", "pointHoverBorderWidth", "borderDash", "yAxisID", "hidden", "chartOptions", "responsive", "maintainAspectRatio", "interaction", "mode", "intersect", "animations", "duration", "easing", "loop", "plugins", "legend", "position", "usePointStyle", "padding", "font", "size", "weight", "tooltip", "titleColor", "bodyColor", "titleFont", "bodyFont", "displayColors", "boxWidth", "boxHeight", "boxPadding", "callbacks", "context", "datasetLabel", "dataset", "riskLevel", "emoji", "toFixed", "scales", "y", "beginAtZero", "max", "title", "display", "text", "color", "grid", "ticks", "y1", "Math", "y2", "min", "x", "maxRotation", "minRotation", "onClick", "div", "style", "children", "sx", "p", "xs", "sm", "md", "mb", "pb", "borderBottom", "top", "right", "width", "height", "borderRadius", "background", "zIndex", "alignItems", "mr", "justifyContent", "boxShadow", "fontSize", "variant", "fontWeight", "primary", "letterSpacing", "ml", "lineHeight", "max<PERSON><PERSON><PERSON>", "onLocationSelect", "initialLocation", "border", "container", "spacing", "item", "onChange", "valueLabelDisplay", "step", "marks", "disabled", "startIcon", "py", "px", "transition", "overflow", "bottom", "left", "textAlign", "textShadow", "options", "mt", "elevation", "verticalAlign", "flexDirection", "filter", "align"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/TimelineRiskPredictor.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Slider,\n  Grid,\n  Button,\n  CircularProgress,\n  Chip,\n  useTheme,\n  IconButton,\n  Tooltip,\n  Divider\n} from '@mui/material';\nimport { useSpring, animated } from 'react-spring';\nimport { Line } from 'react-chartjs-2';\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip as ChartTooltip,\n  Legend,\n  Filler\n} from 'chart.js';\nimport CalendarTodayIcon from '@mui/icons-material/CalendarToday';\nimport AccessTimeIcon from '@mui/icons-material/AccessTime';\nimport RefreshIcon from '@mui/icons-material/Refresh';\nimport InfoIcon from '@mui/icons-material/Info';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\n\n// Import the LocationSelector component\nimport LocationSelector from './LocationSelector';\n\n// Register ChartJS components\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  ChartTooltip,\n  Legend,\n  Filler\n);\n\nconst TimelineRiskPredictor = ({ formData, onForecastGenerated }) => {\n  const theme = useTheme();\n  const [loading, setLoading] = useState(false);\n  const [forecastData, setForecastData] = useState(null);\n  const [timeRange, setTimeRange] = useState(48); // Default 48 hours\n  const [selectedTimeIndex, setSelectedTimeIndex] = useState(null);\n  const initialForecastGenerated = useRef(false);\n  const [selectedLocation, setSelectedLocation] = useState({\n    name: 'Delhi',\n    state: 'Delhi',\n    coordinates: [77.2090, 28.6139],\n    userCoordinates: null // Will be populated when user shares their location\n  });\n\n  // Animation for the component\n  const fadeIn = useSpring({\n    from: { opacity: 0, transform: 'translateY(20px)' },\n    to: { opacity: 1, transform: 'translateY(0)' },\n    config: { tension: 280, friction: 20 },\n    delay: 200\n  });\n\n  // Handle location selection\n  const handleLocationSelect = useCallback((location) => {\n    setSelectedLocation(location);\n    // Reset selected time index when location changes\n    setSelectedTimeIndex(null);\n\n    // If we already have forecast data, show a message to the user\n    if (forecastData) {\n      // Clear the forecast data to avoid confusion\n      setForecastData(null);\n      // Reset the initial forecast flag so we don't automatically regenerate\n      initialForecastGenerated.current = true;\n    }\n  }, [forecastData]);\n\n  // Fetch real forecast data from the API\n  const generateForecast = useCallback(() => {\n    // Check if formData is available and has the required properties\n    if (!formData || !formData.rainfall || !formData.water_level || !formData.discharge) {\n      console.log(\"Form data is incomplete, cannot generate forecast\");\n      return;\n    }\n\n    setLoading(true);\n\n    // Call the weather forecast API\n    fetch('/api/weather-forecast', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({\n        ...formData,\n        hours: timeRange,\n        // Use the selected location\n        location: `${selectedLocation.name},${selectedLocation.state},India`,\n        // If we have precise coordinates from geolocation, use them\n        ...(selectedLocation.userCoordinates && {\n          latitude: selectedLocation.userCoordinates[1],\n          longitude: selectedLocation.userCoordinates[0]\n        }),\n        // Otherwise use the city coordinates\n        ...(!selectedLocation.userCoordinates && {\n          latitude: selectedLocation.coordinates[1],\n          longitude: selectedLocation.coordinates[0]\n        })\n      }),\n    })\n      .then(response => {\n        if (!response.ok) {\n          throw new Error('Network response was not ok');\n        }\n        return response.json();\n      })\n      .then(data => {\n        // Set the forecast data from the API response\n        setForecastData({\n          timestamps: data.timestamps,\n          rainfallData: data.rainfallData,\n          waterLevelData: data.waterLevelData,\n          dischargeData: data.dischargeData,\n          riskScoreData: data.riskScoreData,\n          temperatureData: data.temperatureData,\n          humidityData: data.humidityData,\n          location: data.summary?.location,\n          country: data.summary?.country\n        });\n\n        // Notify parent component\n        if (onForecastGenerated) {\n          onForecastGenerated({\n            maxRiskScore: data.summary.maxRiskScore,\n            maxRiskTime: data.summary.maxRiskTime,\n            riskTrend: data.summary.riskTrend,\n            location: data.summary.location,\n            country: data.summary.country\n          });\n        }\n\n        setLoading(false);\n      })\n      .catch(error => {\n        console.error('Error fetching forecast data:', error);\n        setLoading(false);\n      });\n  }, [formData, timeRange, selectedLocation, onForecastGenerated]);\n\n  // Handle time range change\n  const handleTimeRangeChange = (_, newValue) => {\n    setTimeRange(newValue);\n  };\n\n  // Handle chart click to select a specific time\n  const handleChartClick = (_, elements) => {\n    if (elements.length > 0) {\n      setSelectedTimeIndex(elements[0].index);\n    }\n  };\n\n  // Prepare chart data\n  const chartData = forecastData ? {\n    labels: forecastData.timestamps,\n    datasets: [\n      {\n        label: 'Flood Risk Score',\n        data: forecastData.riskScoreData,\n        borderColor: 'rgba(58, 134, 255, 0.8)',\n        borderWidth: 3,\n        backgroundColor: (ctx) => {\n          const gradient = ctx.chart.ctx.createLinearGradient(0, 0, 0, 400);\n          gradient.addColorStop(0, 'rgba(76, 201, 240, 0.6)');\n          gradient.addColorStop(1, 'rgba(58, 134, 255, 0.05)');\n          return gradient;\n        },\n        fill: true,\n        tension: 0.4,\n        pointRadius: (ctx) => ctx.dataIndex === selectedTimeIndex ? 8 : 4,\n        pointBackgroundColor: (ctx) => {\n          const value = ctx.raw;\n          if (value > 70) return theme.palette.error.main;\n          if (value > 40) return theme.palette.warning.main;\n          return theme.palette.success.main;\n        },\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointHoverRadius: 8,\n        pointHoverBackgroundColor: (ctx) => {\n          const value = ctx.raw;\n          if (value > 70) return theme.palette.error.dark;\n          if (value > 40) return theme.palette.warning.dark;\n          return theme.palette.success.dark;\n        },\n        pointHoverBorderColor: '#fff',\n        pointHoverBorderWidth: 3,\n      },\n      // Add rainfall data as a secondary dataset\n      {\n        label: 'Rainfall (mm)',\n        data: forecastData.rainfallData,\n        borderColor: 'rgba(255, 89, 94, 0.7)',\n        borderWidth: 2,\n        backgroundColor: 'rgba(255, 89, 94, 0.1)',\n        borderDash: [5, 5],\n        fill: true,\n        tension: 0.4,\n        pointRadius: 0,\n        yAxisID: 'y1',\n      },\n      // Add temperature data if available\n      ...(forecastData.temperatureData ? [{\n        label: 'Temperature (°C)',\n        data: forecastData.temperatureData,\n        borderColor: 'rgba(255, 159, 28, 0.7)',\n        borderWidth: 2,\n        backgroundColor: 'rgba(255, 159, 28, 0.0)',\n        tension: 0.4,\n        pointRadius: 2,\n        pointBackgroundColor: 'rgba(255, 159, 28, 0.7)',\n        pointBorderColor: 'rgba(255, 255, 255, 0.8)',\n        pointBorderWidth: 1,\n        pointHoverRadius: 5,\n        pointHoverBackgroundColor: 'rgba(255, 159, 28, 0.9)',\n        pointHoverBorderColor: '#fff',\n        pointHoverBorderWidth: 2,\n        yAxisID: 'y2',\n        hidden: false, // Show by default for better visibility\n      }] : [])\n    ]\n  } : null;\n\n  // Chart options\n  const chartOptions = {\n    responsive: true,\n    maintainAspectRatio: false,\n    interaction: {\n      mode: 'index',\n      intersect: false,\n    },\n    animations: {\n      tension: {\n        duration: 1000,\n        easing: 'easeInOutCubic',\n        from: 0.8,\n        to: 0.4,\n        loop: false\n      }\n    },\n    plugins: {\n      legend: {\n        position: 'top',\n        labels: {\n          usePointStyle: true,\n          padding: 15,\n          font: {\n            size: 12,\n            weight: 'bold'\n          }\n        }\n      },\n      tooltip: {\n        backgroundColor: 'rgba(255, 255, 255, 0.9)',\n        titleColor: '#333',\n        bodyColor: '#333',\n        titleFont: {\n          size: 14,\n          weight: 'bold'\n        },\n        bodyFont: {\n          size: 13\n        },\n        padding: 12,\n        borderColor: 'rgba(58, 134, 255, 0.3)',\n        borderWidth: 1,\n        displayColors: true,\n        boxWidth: 8,\n        boxHeight: 8,\n        boxPadding: 4,\n        usePointStyle: true,\n        callbacks: {\n          label: function(context) {\n            const value = context.raw;\n            const datasetLabel = context.dataset.label;\n\n            if (datasetLabel === 'Flood Risk Score') {\n              let riskLevel = 'Low';\n              let emoji = '✅';\n\n              if (value > 70) {\n                riskLevel = 'High';\n                emoji = '⚠️';\n              } else if (value > 40) {\n                riskLevel = 'Medium';\n                emoji = '⚠️';\n              }\n\n              return `${emoji} Risk Score: ${value.toFixed(1)} (${riskLevel})`;\n            } else if (datasetLabel === 'Rainfall (mm)') {\n              return `☔ Rainfall: ${value.toFixed(1)} mm`;\n            } else if (datasetLabel === 'Temperature (°C)') {\n              return `🌡️ Temperature: ${value.toFixed(1)} °C`;\n            } else if (datasetLabel === 'Humidity (%)') {\n              return `💧 Humidity: ${value.toFixed(0)}%`;\n            } else {\n              return `${datasetLabel}: ${value.toFixed(1)}`;\n            }\n          }\n        }\n      }\n    },\n    scales: {\n      y: {\n        beginAtZero: true,\n        max: 100,\n        title: {\n          display: true,\n          text: 'Risk Score',\n          font: {\n            size: 14,\n            weight: 'bold'\n          },\n          color: 'rgba(58, 134, 255, 0.8)'\n        },\n        grid: {\n          color: 'rgba(0, 0, 0, 0.05)',\n          borderDash: [5, 5]\n        },\n        ticks: {\n          font: {\n            size: 12\n          },\n          color: 'rgba(0, 0, 0, 0.6)'\n        }\n      },\n      y1: {\n        beginAtZero: true,\n        position: 'right',\n        max: Math.max(...forecastData?.rainfallData || [0]) * 1.2,\n        title: {\n          display: true,\n          text: 'Rainfall (mm)',\n          font: {\n            size: 14,\n            weight: 'bold'\n          },\n          color: 'rgba(255, 89, 94, 0.7)'\n        },\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            size: 12\n          },\n          color: 'rgba(255, 89, 94, 0.7)'\n        }\n      },\n      y2: {\n        beginAtZero: false,\n        position: 'right',\n        min: Math.min(...(forecastData?.temperatureData || [20])) - 5,\n        max: Math.max(...(forecastData?.temperatureData || [30])) + 5,\n        title: {\n          display: true,\n          text: 'Temperature (°C)',\n          font: {\n            size: 14,\n            weight: 'bold'\n          },\n          color: 'rgba(255, 159, 28, 0.7)'\n        },\n        grid: {\n          display: false\n        },\n        ticks: {\n          font: {\n            size: 12\n          },\n          color: 'rgba(255, 159, 28, 0.7)'\n        },\n        display: true, // Always show the temperature axis\n      },\n      x: {\n        title: {\n          display: true,\n          text: 'Time',\n          font: {\n            size: 14,\n            weight: 'bold'\n          }\n        },\n        ticks: {\n          maxRotation: 45,\n          minRotation: 45,\n          font: {\n            size: 11\n          },\n          color: 'rgba(0, 0, 0, 0.6)'\n        },\n        grid: {\n          color: 'rgba(0, 0, 0, 0.05)'\n        }\n      }\n    },\n    onClick: handleChartClick\n  };\n\n  // Generate forecast only when the component mounts with valid form data\n  useEffect(() => {\n    // Only generate forecast if we have valid form data and haven't generated one yet\n    if (formData &&\n        formData.rainfall &&\n        formData.water_level &&\n        formData.discharge &&\n        !initialForecastGenerated.current) {\n      initialForecastGenerated.current = true;\n      generateForecast();\n    }\n  }, [formData, generateForecast]);\n\n  // Track location changes but don't automatically regenerate forecast\n  useEffect(() => {\n    // When location changes, we'll just update the UI to show the new location\n    // but we won't automatically regenerate the forecast to avoid infinite loops\n    // The user can click the \"Generate Forecast\" button to get data for the new location\n    if (initialForecastGenerated.current) {\n      console.log(`Location changed to ${selectedLocation.name}, ${selectedLocation.state}`);\n    }\n  }, [selectedLocation]);\n\n  return (\n    <animated.div style={fadeIn}>\n      <Box sx={{ p: { xs: 4, sm: 5, md: 6 }, position: 'relative' }}>\n        {/* Enhanced header section */}\n        <Box sx={{\n          mb: { xs: 4, sm: 5 },\n          pb: 3,\n          borderBottom: '2px solid rgba(58, 134, 255, 0.1)',\n          position: 'relative'\n        }}>\n          {/* Decorative background element */}\n          <Box\n            sx={{\n              position: 'absolute',\n              top: -20,\n              right: -20,\n              width: 120,\n              height: 120,\n              borderRadius: '50%',\n              background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n              zIndex: 0\n            }}\n          />\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, position: 'relative', zIndex: 1 }}>\n            <Box\n              sx={{\n                mr: { xs: 2, sm: 3 },\n                p: { xs: 1.5, sm: 2 },\n                borderRadius: 3,\n                background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.15) 0%, rgba(76, 201, 240, 0.15) 100%)',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                boxShadow: '0 4px 12px rgba(58, 134, 255, 0.2)'\n              }}\n            >\n              <AccessTimeIcon sx={{ fontSize: { xs: 28, sm: 32 }, color: 'primary.main' }} />\n            </Box>\n            <Box>\n              <Typography\n                variant=\"h3\"\n                sx={{\n                  fontWeight: 800,\n                  color: theme.palette.primary.dark,\n                  letterSpacing: '-0.8px',\n                  mb: 0.5\n                }}\n              >\n                Temporal Flood Risk Prediction\n                <Tooltip title=\"This feature predicts how flood risk will change over time based on current conditions and weather forecasts\">\n                  <IconButton size=\"small\" sx={{ ml: 1, mb: 1 }}>\n                    <InfoIcon fontSize=\"small\" />\n                  </IconButton>\n                </Tooltip>\n              </Typography>\n              <Typography\n                variant=\"subtitle1\"\n                sx={{\n                  color: 'text.secondary',\n                  fontWeight: 500\n                }}\n              >\n                Real-time Weather-Based Risk Forecasting\n              </Typography>\n            </Box>\n          </Box>\n\n          <Typography\n            variant=\"body1\"\n            sx={{\n              fontSize: { xs: '1rem', sm: '1.1rem' },\n              lineHeight: 1.7,\n              color: 'text.secondary',\n              maxWidth: '900px',\n              position: 'relative',\n              zIndex: 1\n            }}\n          >\n            Analyze how flood risk evolves over the next hours and days based on your input parameters, real-time weather data, and advanced hydrological modeling.\n          </Typography>\n        </Box>\n\n        {/* Location Selector */}\n        <LocationSelector\n          onLocationSelect={handleLocationSelect}\n          initialLocation={selectedLocation}\n        />\n\n        <Box sx={{\n          mb: { xs: 4, sm: 5 },\n          p: { xs: 3, sm: 4 },\n          borderRadius: 3,\n          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.03) 0%, rgba(255,255,255,0.8) 100%)',\n          border: '1px solid rgba(58, 134, 255, 0.1)'\n        }}>\n          <Grid container spacing={{ xs: 4, sm: 5 }}>\n            <Grid item xs={12} sm={8}>\n              <Box sx={{ mb: 2 }}>\n                <Typography\n                  variant=\"h6\"\n                  sx={{\n                    fontWeight: 600,\n                    color: 'primary.dark',\n                    mb: 1\n                  }}\n                >\n                  Forecast Time Range (hours)\n                </Typography>\n                <Typography\n                  variant=\"body2\"\n                  color=\"text.secondary\"\n                  sx={{ mb: 2 }}\n                >\n                  Select the prediction timeframe for risk analysis\n                </Typography>\n              </Box>\n              <Slider\n                value={timeRange}\n                onChange={handleTimeRangeChange}\n                aria-labelledby=\"time-range-slider\"\n                valueLabelDisplay=\"auto\"\n                step={12}\n                marks={[\n                  { value: 12, label: '12h' },\n                  { value: 24, label: '24h' },\n                  { value: 48, label: '48h' },\n                  { value: 72, label: '72h' }\n                ]}\n                min={12}\n                max={72}\n                sx={{\n                  color: 'primary.main',\n                  height: 8,\n                  '& .MuiSlider-thumb': {\n                    width: 24,\n                    height: 24,\n                    boxShadow: '0 4px 12px rgba(58, 134, 255, 0.3)'\n                  },\n                  '& .MuiSlider-track': {\n                    height: 8,\n                    borderRadius: 4,\n                    background: 'linear-gradient(90deg, #3A86FF 0%, #4CC9F0 100%)'\n                  },\n                  '& .MuiSlider-rail': {\n                    height: 8,\n                    borderRadius: 4,\n                    opacity: 0.3\n                  },\n                  '& .MuiSlider-mark': {\n                    backgroundColor: 'primary.main',\n                    height: 12,\n                    width: 3,\n                    borderRadius: 2\n                  },\n                  '& .MuiSlider-markLabel': {\n                    fontWeight: 600,\n                    color: 'primary.dark'\n                  }\n                }}\n              />\n            </Grid>\n            <Grid item xs={12} sm={4} sx={{\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: { xs: 'center', sm: 'flex-end' }\n            }}>\n              <Button\n                variant=\"contained\"\n                color=\"primary\"\n                onClick={generateForecast}\n                disabled={loading}\n                startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <RefreshIcon />}\n                sx={{\n                  py: { xs: 1.5, sm: 2 },\n                  px: { xs: 4, sm: 5 },\n                  borderRadius: 3,\n                  fontSize: { xs: '1rem', sm: '1.1rem' },\n                  fontWeight: 600,\n                  boxShadow: '0 8px 20px rgba(58, 134, 255, 0.3)',\n                  background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',\n                  '&:hover': {\n                    transform: 'translateY(-2px)',\n                    boxShadow: '0 12px 28px rgba(58, 134, 255, 0.4)'\n                  },\n                  '&:disabled': {\n                    background: 'linear-gradient(45deg, #ccc 30%, #ddd 90%)',\n                    transform: 'none'\n                  },\n                  transition: 'all 0.3s ease'\n                }}\n              >\n                {forecastData ? 'Refresh Forecast' : 'Generate Forecast'}\n              </Button>\n            </Grid>\n          </Grid>\n        </Box>\n\n        {loading ? (\n          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>\n            <CircularProgress />\n          </Box>\n        ) : forecastData ? (\n          <>\n            <Box\n              sx={{\n                height: 350,\n                mb: 3,\n                p: 2,\n                borderRadius: 2,\n                background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(240,245,255,0.9) 100%)',\n                boxShadow: 'inset 0 0 15px rgba(58, 134, 255, 0.1)',\n                border: '1px solid rgba(58, 134, 255, 0.2)',\n                position: 'relative',\n                overflow: 'hidden'\n              }}\n            >\n              {/* Decorative elements */}\n              <Box\n                sx={{\n                  position: 'absolute',\n                  top: -20,\n                  right: -20,\n                  width: 100,\n                  height: 100,\n                  borderRadius: '50%',\n                  background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                }}\n              />\n              <Box\n                sx={{\n                  position: 'absolute',\n                  bottom: -30,\n                  left: -30,\n                  width: 150,\n                  height: 150,\n                  borderRadius: '50%',\n                  background: 'radial-gradient(circle, rgba(255,89,94,0.1) 0%, rgba(0,0,0,0) 70%)',\n                }}\n              />\n\n              {/* Chart title */}\n              <Typography\n                variant=\"h6\"\n                sx={{\n                  mb: 2,\n                  fontWeight: 600,\n                  color: theme.palette.primary.dark,\n                  textAlign: 'center',\n                  textShadow: '0px 1px 2px rgba(0,0,0,0.05)'\n                }}\n              >\n                Temporal Flood Risk Analysis\n              </Typography>\n\n              {/* The chart */}\n              <Line data={chartData} options={chartOptions} />\n            </Box>\n\n            {selectedTimeIndex !== null && (\n              <Box sx={{ mt: 2, mb: 3 }}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: 0,\n                    overflow: 'hidden',\n                    borderRadius: 3,\n                    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.08)'\n                  }}\n                >\n                  {/* Header with timestamp */}\n                  <Box\n                    sx={{\n                      p: 2,\n                      background: 'linear-gradient(90deg, rgba(58,134,255,0.9) 0%, rgba(76,201,240,0.9) 100%)',\n                      color: 'white',\n                      position: 'relative',\n                      overflow: 'hidden'\n                    }}\n                  >\n                    {/* Decorative water ripple effect */}\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        top: -20,\n                        right: -20,\n                        width: 100,\n                        height: 100,\n                        borderRadius: '50%',\n                        background: 'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)',\n                      }}\n                    />\n\n                    <Typography variant=\"h6\" sx={{ fontWeight: 600, textShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>\n                      <CalendarTodayIcon fontSize=\"small\" sx={{ mr: 1, verticalAlign: 'middle' }} />\n                      {forecastData.timestamps[selectedTimeIndex]}\n                    </Typography>\n                    <Typography variant=\"body2\" sx={{ mt: 0.5, opacity: 0.9 }}>\n                      Detailed forecast information\n                    </Typography>\n                  </Box>\n\n                  {/* Content with metrics */}\n                  <Box sx={{ p: { xs: 3, sm: 4 } }}>\n                    <Grid container spacing={{ xs: 3, sm: 4 }}>\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: `linear-gradient(135deg, ${\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'rgba(255,89,94,0.1)' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'rgba(251,133,0,0.1)' :\n                              'rgba(6,214,160,0.1)'\n                            } 0%, rgba(255,255,255,0.7) 100%)`,\n                            border: `1px solid ${\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'rgba(255,89,94,0.3)' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'rgba(251,133,0,0.3)' :\n                              'rgba(6,214,160,0.3)'\n                            }`,\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            Risk Score\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{\n                            fontWeight: 'bold',\n                            color: forecastData.riskScoreData[selectedTimeIndex] > 70 ? theme.palette.error.main :\n                                  forecastData.riskScoreData[selectedTimeIndex] > 40 ? theme.palette.warning.main :\n                                  theme.palette.success.main\n                          }}>\n                            {forecastData.riskScoreData[selectedTimeIndex].toFixed(1)}\n                          </Typography>\n                          <Chip\n                            label={\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'High Risk' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'Medium Risk' : 'Low Risk'\n                            }\n                            size=\"small\"\n                            color={\n                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'error' :\n                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'warning' : 'success'\n                            }\n                            sx={{\n                              mt: 1,\n                              fontWeight: 'bold',\n                              boxShadow: '0 2px 5px rgba(0,0,0,0.1)'\n                            }}\n                          />\n                        </Box>\n                      </Grid>\n\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: 'linear-gradient(135deg, rgba(255,89,94,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                            border: '1px solid rgba(255,89,94,0.3)',\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            <ThunderstormIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                            Rainfall\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(255,89,94,0.8)' }}>\n                            {forecastData.rainfallData[selectedTimeIndex].toFixed(1)}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            millimeters\n                          </Typography>\n                        </Box>\n                      </Grid>\n\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: 'linear-gradient(135deg, rgba(76,201,240,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                            border: '1px solid rgba(76,201,240,0.3)',\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            <WaterDropIcon fontSize=\"small\" sx={{ mr: 0.5, verticalAlign: 'middle' }} />\n                            Water Level\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(76,201,240,0.8)' }}>\n                            {forecastData.waterLevelData[selectedTimeIndex].toFixed(2)}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            meters\n                          </Typography>\n                        </Box>\n                      </Grid>\n\n                      {forecastData.temperatureData && (\n                        <Grid item xs={12} sm={6} md={3}>\n                          <Box\n                            sx={{\n                              p: 2,\n                              borderRadius: 2,\n                              background: 'linear-gradient(135deg, rgba(255,159,28,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                              border: '1px solid rgba(255,159,28,0.3)',\n                              height: '100%',\n                              display: 'flex',\n                              flexDirection: 'column',\n                              justifyContent: 'center',\n                              alignItems: 'center',\n                              textAlign: 'center'\n                            }}\n                          >\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                              Temperature\n                            </Typography>\n                            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(255,159,28,0.8)' }}>\n                              {forecastData.temperatureData[selectedTimeIndex].toFixed(1)}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                              °C\n                            </Typography>\n                          </Box>\n                        </Grid>\n                      )}\n\n                      {forecastData.humidityData && (\n                        <Grid item xs={12} sm={6} md={3}>\n                          <Box\n                            sx={{\n                              p: 2,\n                              borderRadius: 2,\n                              background: 'linear-gradient(135deg, rgba(111,134,214,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                              border: '1px solid rgba(111,134,214,0.3)',\n                              height: '100%',\n                              display: 'flex',\n                              flexDirection: 'column',\n                              justifyContent: 'center',\n                              alignItems: 'center',\n                              textAlign: 'center'\n                            }}\n                          >\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                              Humidity\n                            </Typography>\n                            <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(111,134,214,0.8)' }}>\n                              {forecastData.humidityData[selectedTimeIndex].toFixed(0)}\n                            </Typography>\n                            <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                              %\n                            </Typography>\n                          </Box>\n                        </Grid>\n                      )}\n\n                      <Grid item xs={12} sm={6} md={3}>\n                        <Box\n                          sx={{\n                            p: 2,\n                            borderRadius: 2,\n                            background: 'linear-gradient(135deg, rgba(58,134,255,0.1) 0%, rgba(255,255,255,0.7) 100%)',\n                            border: '1px solid rgba(58,134,255,0.3)',\n                            height: '100%',\n                            display: 'flex',\n                            flexDirection: 'column',\n                            justifyContent: 'center',\n                            alignItems: 'center',\n                            textAlign: 'center'\n                          }}\n                        >\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mb: 1, fontWeight: 500 }}>\n                            Discharge\n                          </Typography>\n                          <Typography variant=\"h4\" sx={{ fontWeight: 'bold', color: 'rgba(58,134,255,0.8)' }}>\n                            {forecastData.dischargeData[selectedTimeIndex].toFixed(0)}\n                          </Typography>\n                          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 0.5 }}>\n                            m³/s\n                          </Typography>\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </Box>\n                </Paper>\n              </Box>\n            )}\n\n            <Box\n              sx={{\n                mt: 3,\n                p: 2,\n                borderRadius: 2,\n                backgroundColor: 'rgba(76, 201, 240, 0.1)',\n                border: '1px dashed rgba(58, 134, 255, 0.3)',\n                display: 'flex',\n                alignItems: 'flex-start'\n              }}\n            >\n              <InfoIcon\n                fontSize=\"small\"\n                sx={{\n                  mr: 1.5,\n                  mt: 0.3,\n                  color: theme.palette.primary.main\n                }}\n              />\n              <Box>\n                <Typography variant=\"body2\" sx={{ color: theme.palette.primary.dark, fontWeight: 500, mb: 0.5 }}>\n                  Interactive Forecast Visualization for {forecastData.location || selectedLocation.name}, {selectedLocation.state}\n                </Typography>\n                <Typography variant=\"body2\" color=\"text.secondary\">\n                  This forecast is based on real-time weather data and hydrological modeling for your selected location.\n                  <strong> Click on any point in the chart</strong> to see detailed predictions for that specific time.\n                  The blue line shows flood risk score, while the red dashed line shows rainfall intensity.\n                  {forecastData.temperatureData && ' Temperature data is also available (toggle in chart legend).'}\n                </Typography>\n              </Box>\n            </Box>\n          </>\n        ) : (\n          <Box sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            justifyContent: 'center',\n            alignItems: 'center',\n            height: 350,\n            background: 'linear-gradient(135deg, rgba(240,245,255,0.8) 0%, rgba(230,240,255,0.8) 100%)',\n            borderRadius: 3,\n            border: '1px dashed rgba(58, 134, 255, 0.3)',\n            position: 'relative',\n            overflow: 'hidden',\n            p: 3\n          }}>\n            {/* Decorative elements */}\n            <Box\n              sx={{\n                position: 'absolute',\n                top: -30,\n                right: -30,\n                width: 150,\n                height: 150,\n                borderRadius: '50%',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                bottom: -30,\n                left: -30,\n                width: 150,\n                height: 150,\n                borderRadius: '50%',\n                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n              }}\n            />\n\n            <AccessTimeIcon sx={{\n              fontSize: 80,\n              color: 'rgba(58, 134, 255, 0.3)',\n              mb: 3,\n              filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'\n            }} />\n\n            <Typography variant=\"h6\" color=\"primary.dark\" align=\"center\" sx={{ mb: 1, fontWeight: 600 }}>\n              Temporal Flood Risk Prediction\n            </Typography>\n\n            <Typography variant=\"body1\" color=\"text.secondary\" align=\"center\" sx={{ mb: 3, maxWidth: 450 }}>\n              Generate a forecast to visualize how flood risk may change over the next hours and days based on current conditions.\n            </Typography>\n\n            <Button\n              variant=\"contained\"\n              color=\"primary\"\n              onClick={generateForecast}\n              disabled={loading}\n              startIcon={loading ? <CircularProgress size={20} color=\"inherit\" /> : <RefreshIcon />}\n              sx={{\n                px: 3,\n                py: 1.2,\n                borderRadius: 8,\n                boxShadow: '0 4px 14px rgba(58, 134, 255, 0.3)',\n                '&:hover': {\n                  boxShadow: '0 6px 20px rgba(58, 134, 255, 0.4)',\n                  transform: 'translateY(-2px)'\n                },\n                transition: 'all 0.3s ease'\n              }}\n            >\n              Generate Forecast\n            </Button>\n          </Box>\n        )}\n      </Box>\n    </animated.div>\n  );\n};\n\nexport default TimelineRiskPredictor;\n"], "mappings": "kIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACvE,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,IAAI,CACJC,MAAM,CACNC,gBAAgB,CAChBC,IAAI,CACJC,QAAQ,CACRC,UAAU,CACVC,OAAO,CACPC,OAAO,KACF,eAAe,CACtB,OAASC,SAAS,CAAEC,QAAQ,KAAQ,cAAc,CAClD,OAASC,IAAI,KAAQ,iBAAiB,CACtC,OACEC,KAAK,GAAI,CAAAC,OAAO,CAChBC,aAAa,CACbC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,KAAK,CACLX,OAAO,GAAI,CAAAY,YAAY,CACvBC,MAAM,CACNC,MAAM,KACD,UAAU,CACjB,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,gBAAgB,KAAM,kCAAkC,CAC/D,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAE3D;AACA,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CAEjD;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACAtB,OAAO,CAACuB,QAAQ,CACdtB,aAAa,CACbC,WAAW,CACXC,YAAY,CACZC,WAAW,CACXC,KAAK,CACLC,YAAY,CACZC,MAAM,CACNC,MACF,CAAC,CAED,KAAM,CAAAgB,qBAAqB,CAAGC,IAAA,EAAuC,IAAtC,CAAEC,QAAQ,CAAEC,mBAAoB,CAAC,CAAAF,IAAA,CAC9D,KAAM,CAAAG,KAAK,CAAGpC,QAAQ,CAAC,CAAC,CACxB,KAAM,CAACqC,OAAO,CAAEC,UAAU,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACmD,YAAY,CAAEC,eAAe,CAAC,CAAGpD,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACqD,SAAS,CAAEC,YAAY,CAAC,CAAGtD,QAAQ,CAAC,EAAE,CAAC,CAAE;AAChD,KAAM,CAACuD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGxD,QAAQ,CAAC,IAAI,CAAC,CAChE,KAAM,CAAAyD,wBAAwB,CAAGtD,MAAM,CAAC,KAAK,CAAC,CAC9C,KAAM,CAACuD,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG3D,QAAQ,CAAC,CACvD4D,IAAI,CAAE,OAAO,CACbC,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,CAAC,OAAO,CAAE,OAAO,CAAC,CAC/BC,eAAe,CAAE,IAAK;AACxB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,MAAM,CAAGhD,SAAS,CAAC,CACvBiD,IAAI,CAAE,CAAEC,OAAO,CAAE,CAAC,CAAEC,SAAS,CAAE,kBAAmB,CAAC,CACnDC,EAAE,CAAE,CAAEF,OAAO,CAAE,CAAC,CAAEC,SAAS,CAAE,eAAgB,CAAC,CAC9CE,MAAM,CAAE,CAAEC,OAAO,CAAE,GAAG,CAAEC,QAAQ,CAAE,EAAG,CAAC,CACtCC,KAAK,CAAE,GACT,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,oBAAoB,CAAGvE,WAAW,CAAEwE,QAAQ,EAAK,CACrDf,mBAAmB,CAACe,QAAQ,CAAC,CAC7B;AACAlB,oBAAoB,CAAC,IAAI,CAAC,CAE1B;AACA,GAAIL,YAAY,CAAE,CAChB;AACAC,eAAe,CAAC,IAAI,CAAC,CACrB;AACAK,wBAAwB,CAACkB,OAAO,CAAG,IAAI,CACzC,CACF,CAAC,CAAE,CAACxB,YAAY,CAAC,CAAC,CAElB;AACA,KAAM,CAAAyB,gBAAgB,CAAG1E,WAAW,CAAC,IAAM,CACzC;AACA,GAAI,CAAC4C,QAAQ,EAAI,CAACA,QAAQ,CAAC+B,QAAQ,EAAI,CAAC/B,QAAQ,CAACgC,WAAW,EAAI,CAAChC,QAAQ,CAACiC,SAAS,CAAE,CACnFC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC,CAChE,OACF,CAEA/B,UAAU,CAAC,IAAI,CAAC,CAEhB;AACAgC,KAAK,CAAC,uBAAuB,CAAE,CAC7BC,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,IACf1C,QAAQ,MACX2C,KAAK,CAAEpC,SAAS,CAChB;AACAqB,QAAQ,IAAAgB,MAAA,CAAKhC,gBAAgB,CAACE,IAAI,MAAA8B,MAAA,CAAIhC,gBAAgB,CAACG,KAAK,UAAQ,EAEhEH,gBAAgB,CAACK,eAAe,EAAI,CACtC4B,QAAQ,CAAEjC,gBAAgB,CAACK,eAAe,CAAC,CAAC,CAAC,CAC7C6B,SAAS,CAAElC,gBAAgB,CAACK,eAAe,CAAC,CAAC,CAC/C,CAAC,EAEG,CAACL,gBAAgB,CAACK,eAAe,EAAI,CACvC4B,QAAQ,CAAEjC,gBAAgB,CAACI,WAAW,CAAC,CAAC,CAAC,CACzC8B,SAAS,CAAElC,gBAAgB,CAACI,WAAW,CAAC,CAAC,CAC3C,CAAC,CACF,CACH,CAAC,CAAC,CACC+B,IAAI,CAACC,QAAQ,EAAI,CAChB,GAAI,CAACA,QAAQ,CAACC,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,CAAC,6BAA6B,CAAC,CAChD,CACA,MAAO,CAAAF,QAAQ,CAACG,IAAI,CAAC,CAAC,CACxB,CAAC,CAAC,CACDJ,IAAI,CAACK,IAAI,EAAI,KAAAC,aAAA,CAAAC,cAAA,CACZ;AACAhD,eAAe,CAAC,CACdiD,UAAU,CAAEH,IAAI,CAACG,UAAU,CAC3BC,YAAY,CAAEJ,IAAI,CAACI,YAAY,CAC/BC,cAAc,CAAEL,IAAI,CAACK,cAAc,CACnCC,aAAa,CAAEN,IAAI,CAACM,aAAa,CACjCC,aAAa,CAAEP,IAAI,CAACO,aAAa,CACjCC,eAAe,CAAER,IAAI,CAACQ,eAAe,CACrCC,YAAY,CAAET,IAAI,CAACS,YAAY,CAC/BjC,QAAQ,EAAAyB,aAAA,CAAED,IAAI,CAACU,OAAO,UAAAT,aAAA,iBAAZA,aAAA,CAAczB,QAAQ,CAChCmC,OAAO,EAAAT,cAAA,CAAEF,IAAI,CAACU,OAAO,UAAAR,cAAA,iBAAZA,cAAA,CAAcS,OACzB,CAAC,CAAC,CAEF;AACA,GAAI9D,mBAAmB,CAAE,CACvBA,mBAAmB,CAAC,CAClB+D,YAAY,CAAEZ,IAAI,CAACU,OAAO,CAACE,YAAY,CACvCC,WAAW,CAAEb,IAAI,CAACU,OAAO,CAACG,WAAW,CACrCC,SAAS,CAAEd,IAAI,CAACU,OAAO,CAACI,SAAS,CACjCtC,QAAQ,CAAEwB,IAAI,CAACU,OAAO,CAAClC,QAAQ,CAC/BmC,OAAO,CAAEX,IAAI,CAACU,OAAO,CAACC,OACxB,CAAC,CAAC,CACJ,CAEA3D,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACD+D,KAAK,CAACC,KAAK,EAAI,CACdlC,OAAO,CAACkC,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrDhE,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAC,CACN,CAAC,CAAE,CAACJ,QAAQ,CAAEO,SAAS,CAAEK,gBAAgB,CAAEX,mBAAmB,CAAC,CAAC,CAEhE;AACA,KAAM,CAAAoE,qBAAqB,CAAGA,CAACC,CAAC,CAAEC,QAAQ,GAAK,CAC7C/D,YAAY,CAAC+D,QAAQ,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGA,CAACF,CAAC,CAAEG,QAAQ,GAAK,CACxC,GAAIA,QAAQ,CAACC,MAAM,CAAG,CAAC,CAAE,CACvBhE,oBAAoB,CAAC+D,QAAQ,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,CACzC,CACF,CAAC,CAED;AACA,KAAM,CAAAC,SAAS,CAAGvE,YAAY,CAAG,CAC/BwE,MAAM,CAAExE,YAAY,CAACkD,UAAU,CAC/BuB,QAAQ,CAAE,CACR,CACEC,KAAK,CAAE,kBAAkB,CACzB3B,IAAI,CAAE/C,YAAY,CAACsD,aAAa,CAChCqB,WAAW,CAAE,yBAAyB,CACtCC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAGC,GAAG,EAAK,CACxB,KAAM,CAAAC,QAAQ,CAAGD,GAAG,CAACE,KAAK,CAACF,GAAG,CAACG,oBAAoB,CAAC,CAAC,CAAE,CAAC,CAAE,CAAC,CAAE,GAAG,CAAC,CACjEF,QAAQ,CAACG,YAAY,CAAC,CAAC,CAAE,yBAAyB,CAAC,CACnDH,QAAQ,CAACG,YAAY,CAAC,CAAC,CAAE,0BAA0B,CAAC,CACpD,MAAO,CAAAH,QAAQ,CACjB,CAAC,CACDI,IAAI,CAAE,IAAI,CACVhE,OAAO,CAAE,GAAG,CACZiE,WAAW,CAAGN,GAAG,EAAKA,GAAG,CAACO,SAAS,GAAKjF,iBAAiB,CAAG,CAAC,CAAG,CAAC,CACjEkF,oBAAoB,CAAGR,GAAG,EAAK,CAC7B,KAAM,CAAAS,KAAK,CAAGT,GAAG,CAACU,GAAG,CACrB,GAAID,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAAC1B,KAAK,CAAC2B,IAAI,CAC/C,GAAIH,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAACE,OAAO,CAACD,IAAI,CACjD,MAAO,CAAA7F,KAAK,CAAC4F,OAAO,CAACG,OAAO,CAACF,IAAI,CACnC,CAAC,CACDG,gBAAgB,CAAE,MAAM,CACxBC,gBAAgB,CAAE,CAAC,CACnBC,gBAAgB,CAAE,CAAC,CACnBC,yBAAyB,CAAGlB,GAAG,EAAK,CAClC,KAAM,CAAAS,KAAK,CAAGT,GAAG,CAACU,GAAG,CACrB,GAAID,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAAC1B,KAAK,CAACkC,IAAI,CAC/C,GAAIV,KAAK,CAAG,EAAE,CAAE,MAAO,CAAA1F,KAAK,CAAC4F,OAAO,CAACE,OAAO,CAACM,IAAI,CACjD,MAAO,CAAApG,KAAK,CAAC4F,OAAO,CAACG,OAAO,CAACK,IAAI,CACnC,CAAC,CACDC,qBAAqB,CAAE,MAAM,CAC7BC,qBAAqB,CAAE,CACzB,CAAC,CACD;AACA,CACEzB,KAAK,CAAE,eAAe,CACtB3B,IAAI,CAAE/C,YAAY,CAACmD,YAAY,CAC/BwB,WAAW,CAAE,wBAAwB,CACrCC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAE,wBAAwB,CACzCuB,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CAAC,CAClBjB,IAAI,CAAE,IAAI,CACVhE,OAAO,CAAE,GAAG,CACZiE,WAAW,CAAE,CAAC,CACdiB,OAAO,CAAE,IACX,CAAC,CACD;AACA,IAAIrG,YAAY,CAACuD,eAAe,CAAG,CAAC,CAClCmB,KAAK,CAAE,kBAAkB,CACzB3B,IAAI,CAAE/C,YAAY,CAACuD,eAAe,CAClCoB,WAAW,CAAE,yBAAyB,CACtCC,WAAW,CAAE,CAAC,CACdC,eAAe,CAAE,yBAAyB,CAC1C1D,OAAO,CAAE,GAAG,CACZiE,WAAW,CAAE,CAAC,CACdE,oBAAoB,CAAE,yBAAyB,CAC/CO,gBAAgB,CAAE,0BAA0B,CAC5CC,gBAAgB,CAAE,CAAC,CACnBC,gBAAgB,CAAE,CAAC,CACnBC,yBAAyB,CAAE,yBAAyB,CACpDE,qBAAqB,CAAE,MAAM,CAC7BC,qBAAqB,CAAE,CAAC,CACxBE,OAAO,CAAE,IAAI,CACbC,MAAM,CAAE,KAAO;AACjB,CAAC,CAAC,CAAG,EAAE,CAAC,CAEZ,CAAC,CAAG,IAAI,CAER;AACA,KAAM,CAAAC,YAAY,CAAG,CACnBC,UAAU,CAAE,IAAI,CAChBC,mBAAmB,CAAE,KAAK,CAC1BC,WAAW,CAAE,CACXC,IAAI,CAAE,OAAO,CACbC,SAAS,CAAE,KACb,CAAC,CACDC,UAAU,CAAE,CACV1F,OAAO,CAAE,CACP2F,QAAQ,CAAE,IAAI,CACdC,MAAM,CAAE,gBAAgB,CACxBjG,IAAI,CAAE,GAAG,CACTG,EAAE,CAAE,GAAG,CACP+F,IAAI,CAAE,KACR,CACF,CAAC,CACDC,OAAO,CAAE,CACPC,MAAM,CAAE,CACNC,QAAQ,CAAE,KAAK,CACf3C,MAAM,CAAE,CACN4C,aAAa,CAAE,IAAI,CACnBC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACP5C,eAAe,CAAE,0BAA0B,CAC3C6C,UAAU,CAAE,MAAM,CAClBC,SAAS,CAAE,MAAM,CACjBC,SAAS,CAAE,CACTL,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDK,QAAQ,CAAE,CACRN,IAAI,CAAE,EACR,CAAC,CACDF,OAAO,CAAE,EAAE,CACX1C,WAAW,CAAE,yBAAyB,CACtCC,WAAW,CAAE,CAAC,CACdkD,aAAa,CAAE,IAAI,CACnBC,QAAQ,CAAE,CAAC,CACXC,SAAS,CAAE,CAAC,CACZC,UAAU,CAAE,CAAC,CACbb,aAAa,CAAE,IAAI,CACnBc,SAAS,CAAE,CACTxD,KAAK,CAAE,QAAAA,CAASyD,OAAO,CAAE,CACvB,KAAM,CAAA5C,KAAK,CAAG4C,OAAO,CAAC3C,GAAG,CACzB,KAAM,CAAA4C,YAAY,CAAGD,OAAO,CAACE,OAAO,CAAC3D,KAAK,CAE1C,GAAI0D,YAAY,GAAK,kBAAkB,CAAE,CACvC,GAAI,CAAAE,SAAS,CAAG,KAAK,CACrB,GAAI,CAAAC,KAAK,CAAG,GAAG,CAEf,GAAIhD,KAAK,CAAG,EAAE,CAAE,CACd+C,SAAS,CAAG,MAAM,CAClBC,KAAK,CAAG,IAAI,CACd,CAAC,IAAM,IAAIhD,KAAK,CAAG,EAAE,CAAE,CACrB+C,SAAS,CAAG,QAAQ,CACpBC,KAAK,CAAG,IAAI,CACd,CAEA,SAAAhG,MAAA,CAAUgG,KAAK,kBAAAhG,MAAA,CAAgBgD,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,OAAAjG,MAAA,CAAK+F,SAAS,MAC/D,CAAC,IAAM,IAAIF,YAAY,GAAK,eAAe,CAAE,CAC3C,0BAAA7F,MAAA,CAAsBgD,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,QACxC,CAAC,IAAM,IAAIJ,YAAY,GAAK,kBAAkB,CAAE,CAC9C,yCAAA7F,MAAA,CAA2BgD,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,WAC7C,CAAC,IAAM,IAAIJ,YAAY,GAAK,cAAc,CAAE,CAC1C,gCAAA7F,MAAA,CAAuBgD,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,MACzC,CAAC,IAAM,CACL,SAAAjG,MAAA,CAAU6F,YAAY,OAAA7F,MAAA,CAAKgD,KAAK,CAACiD,OAAO,CAAC,CAAC,CAAC,EAC7C,CACF,CACF,CACF,CACF,CAAC,CACDC,MAAM,CAAE,CACNC,CAAC,CAAE,CACDC,WAAW,CAAE,IAAI,CACjBC,GAAG,CAAE,GAAG,CACRC,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,YAAY,CAClBzB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDwB,KAAK,CAAE,yBACT,CAAC,CACDC,IAAI,CAAE,CACJD,KAAK,CAAE,qBAAqB,CAC5B5C,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CACnB,CAAC,CACD8C,KAAK,CAAE,CACL5B,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDyB,KAAK,CAAE,oBACT,CACF,CAAC,CACDG,EAAE,CAAE,CACFR,WAAW,CAAE,IAAI,CACjBxB,QAAQ,CAAE,OAAO,CACjByB,GAAG,CAAEQ,IAAI,CAACR,GAAG,CAAC,IAAG,CAAA5I,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEmD,YAAY,GAAI,CAAC,CAAC,CAAC,EAAC,CAAG,GAAG,CACzD0F,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,eAAe,CACrBzB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDwB,KAAK,CAAE,wBACT,CAAC,CACDC,IAAI,CAAE,CACJH,OAAO,CAAE,KACX,CAAC,CACDI,KAAK,CAAE,CACL5B,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDyB,KAAK,CAAE,wBACT,CACF,CAAC,CACDK,EAAE,CAAE,CACFV,WAAW,CAAE,KAAK,CAClBxB,QAAQ,CAAE,OAAO,CACjBmC,GAAG,CAAEF,IAAI,CAACE,GAAG,CAAC,IAAI,CAAAtJ,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEuD,eAAe,GAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAG,CAAC,CAC7DqF,GAAG,CAAEQ,IAAI,CAACR,GAAG,CAAC,IAAI,CAAA5I,YAAY,SAAZA,YAAY,iBAAZA,YAAY,CAAEuD,eAAe,GAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAG,CAAC,CAC7DsF,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,kBAAkB,CACxBzB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CAAC,CACDwB,KAAK,CAAE,yBACT,CAAC,CACDC,IAAI,CAAE,CACJH,OAAO,CAAE,KACX,CAAC,CACDI,KAAK,CAAE,CACL5B,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDyB,KAAK,CAAE,yBACT,CAAC,CACDF,OAAO,CAAE,IAAM;AACjB,CAAC,CACDS,CAAC,CAAE,CACDV,KAAK,CAAE,CACLC,OAAO,CAAE,IAAI,CACbC,IAAI,CAAE,MAAM,CACZzB,IAAI,CAAE,CACJC,IAAI,CAAE,EAAE,CACRC,MAAM,CAAE,MACV,CACF,CAAC,CACD0B,KAAK,CAAE,CACLM,WAAW,CAAE,EAAE,CACfC,WAAW,CAAE,EAAE,CACfnC,IAAI,CAAE,CACJC,IAAI,CAAE,EACR,CAAC,CACDyB,KAAK,CAAE,oBACT,CAAC,CACDC,IAAI,CAAE,CACJD,KAAK,CAAE,qBACT,CACF,CACF,CAAC,CACDU,OAAO,CAAEvF,gBACX,CAAC,CAED;AACArH,SAAS,CAAC,IAAM,CACd;AACA,GAAI6C,QAAQ,EACRA,QAAQ,CAAC+B,QAAQ,EACjB/B,QAAQ,CAACgC,WAAW,EACpBhC,QAAQ,CAACiC,SAAS,EAClB,CAACtB,wBAAwB,CAACkB,OAAO,CAAE,CACrClB,wBAAwB,CAACkB,OAAO,CAAG,IAAI,CACvCC,gBAAgB,CAAC,CAAC,CACpB,CACF,CAAC,CAAE,CAAC9B,QAAQ,CAAE8B,gBAAgB,CAAC,CAAC,CAEhC;AACA3E,SAAS,CAAC,IAAM,CACd;AACA;AACA;AACA,GAAIwD,wBAAwB,CAACkB,OAAO,CAAE,CACpCK,OAAO,CAACC,GAAG,wBAAAS,MAAA,CAAwBhC,gBAAgB,CAACE,IAAI,OAAA8B,MAAA,CAAKhC,gBAAgB,CAACG,KAAK,CAAE,CAAC,CACxF,CACF,CAAC,CAAE,CAACH,gBAAgB,CAAC,CAAC,CAEtB,mBACEpB,IAAA,CAACrB,QAAQ,CAAC6L,GAAG,EAACC,KAAK,CAAE/I,MAAO,CAAAgJ,QAAA,cAC1BxK,KAAA,CAACpC,GAAG,EAAC6M,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAAE/C,QAAQ,CAAE,UAAW,CAAE,CAAA0C,QAAA,eAE5DxK,KAAA,CAACpC,GAAG,EAAC6M,EAAE,CAAE,CACPK,EAAE,CAAE,CAAEH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBG,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,mCAAmC,CACjDlD,QAAQ,CAAE,UACZ,CAAE,CAAA0C,QAAA,eAEA1K,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACF3C,QAAQ,CAAE,UAAU,CACpBmD,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,sEAAsE,CAClFC,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cAEFvL,KAAA,CAACpC,GAAG,EAAC6M,EAAE,CAAE,CAAEhB,OAAO,CAAE,MAAM,CAAE+B,UAAU,CAAE,QAAQ,CAAEV,EAAE,CAAE,CAAC,CAAEhD,QAAQ,CAAE,UAAU,CAAEyD,MAAM,CAAE,CAAE,CAAE,CAAAf,QAAA,eACzF1K,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACFgB,EAAE,CAAE,CAAEd,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBF,CAAC,CAAE,CAAEC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBS,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,qFAAqF,CACjG7B,OAAO,CAAE,MAAM,CACf+B,UAAU,CAAE,QAAQ,CACpBE,cAAc,CAAE,QAAQ,CACxBC,SAAS,CAAE,oCACb,CAAE,CAAAnB,QAAA,cAEF1K,IAAA,CAACR,cAAc,EAACmL,EAAE,CAAE,CAAEmB,QAAQ,CAAE,CAAEjB,EAAE,CAAE,EAAE,CAAEC,EAAE,CAAE,EAAG,CAAC,CAAEjB,KAAK,CAAE,cAAe,CAAE,CAAE,CAAC,CAC5E,CAAC,cACN3J,KAAA,CAACpC,GAAG,EAAA4M,QAAA,eACFxK,KAAA,CAACnC,UAAU,EACTgO,OAAO,CAAC,IAAI,CACZpB,EAAE,CAAE,CACFqB,UAAU,CAAE,GAAG,CACfnC,KAAK,CAAEnJ,KAAK,CAAC4F,OAAO,CAAC2F,OAAO,CAACnF,IAAI,CACjCoF,aAAa,CAAE,QAAQ,CACvBlB,EAAE,CAAE,GACN,CAAE,CAAAN,QAAA,EACH,gCAEC,cAAA1K,IAAA,CAACxB,OAAO,EAACkL,KAAK,CAAC,8GAA8G,CAAAgB,QAAA,cAC3H1K,IAAA,CAACzB,UAAU,EAAC6J,IAAI,CAAC,OAAO,CAACuC,EAAE,CAAE,CAAEwB,EAAE,CAAE,CAAC,CAAEnB,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,cAC5C1K,IAAA,CAACN,QAAQ,EAACoM,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,CACN,CAAC,EACA,CAAC,cACb9L,IAAA,CAACjC,UAAU,EACTgO,OAAO,CAAC,WAAW,CACnBpB,EAAE,CAAE,CACFd,KAAK,CAAE,gBAAgB,CACvBmC,UAAU,CAAE,GACd,CAAE,CAAAtB,QAAA,CACH,0CAED,CAAY,CAAC,EACV,CAAC,EACH,CAAC,cAEN1K,IAAA,CAACjC,UAAU,EACTgO,OAAO,CAAC,OAAO,CACfpB,EAAE,CAAE,CACFmB,QAAQ,CAAE,CAAEjB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtCsB,UAAU,CAAE,GAAG,CACfvC,KAAK,CAAE,gBAAgB,CACvBwC,QAAQ,CAAE,OAAO,CACjBrE,QAAQ,CAAE,UAAU,CACpByD,MAAM,CAAE,CACV,CAAE,CAAAf,QAAA,CACH,yJAED,CAAY,CAAC,EACV,CAAC,cAGN1K,IAAA,CAACF,gBAAgB,EACfwM,gBAAgB,CAAEnK,oBAAqB,CACvCoK,eAAe,CAAEnL,gBAAiB,CACnC,CAAC,cAEFpB,IAAA,CAAClC,GAAG,EAAC6M,EAAE,CAAE,CACPK,EAAE,CAAE,CAAEH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBF,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBS,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,kFAAkF,CAC9FgB,MAAM,CAAE,mCACV,CAAE,CAAA9B,QAAA,cACAxK,KAAA,CAAChC,IAAI,EAACuO,SAAS,MAACC,OAAO,CAAE,CAAE7B,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACxCxK,KAAA,CAAChC,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAJ,QAAA,eACvBxK,KAAA,CAACpC,GAAG,EAAC6M,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,eACjB1K,IAAA,CAACjC,UAAU,EACTgO,OAAO,CAAC,IAAI,CACZpB,EAAE,CAAE,CACFqB,UAAU,CAAE,GAAG,CACfnC,KAAK,CAAE,cAAc,CACrBmB,EAAE,CAAE,CACN,CAAE,CAAAN,QAAA,CACH,6BAED,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EACTgO,OAAO,CAAC,OAAO,CACflC,KAAK,CAAC,gBAAgB,CACtBc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,CACf,mDAED,CAAY,CAAC,EACV,CAAC,cACN1K,IAAA,CAAC/B,MAAM,EACLmI,KAAK,CAAErF,SAAU,CACjB6L,QAAQ,CAAE/H,qBAAsB,CAChC,kBAAgB,mBAAmB,CACnCgI,iBAAiB,CAAC,MAAM,CACxBC,IAAI,CAAE,EAAG,CACTC,KAAK,CAAE,CACL,CAAE3G,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CAAEa,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CAAEa,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CAAEa,KAAK,CAAE,EAAE,CAAEb,KAAK,CAAE,KAAM,CAAC,CAC3B,CACF4E,GAAG,CAAE,EAAG,CACRV,GAAG,CAAE,EAAG,CACRkB,EAAE,CAAE,CACFd,KAAK,CAAE,cAAc,CACrByB,MAAM,CAAE,CAAC,CACT,oBAAoB,CAAE,CACpBD,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,EAAE,CACVO,SAAS,CAAE,oCACb,CAAC,CACD,oBAAoB,CAAE,CACpBP,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,kDACd,CAAC,CACD,mBAAmB,CAAE,CACnBF,MAAM,CAAE,CAAC,CACTC,YAAY,CAAE,CAAC,CACf3J,OAAO,CAAE,GACX,CAAC,CACD,mBAAmB,CAAE,CACnB8D,eAAe,CAAE,cAAc,CAC/B4F,MAAM,CAAE,EAAE,CACVD,KAAK,CAAE,CAAC,CACRE,YAAY,CAAE,CAChB,CAAC,CACD,wBAAwB,CAAE,CACxBS,UAAU,CAAE,GAAG,CACfnC,KAAK,CAAE,cACT,CACF,CAAE,CACH,CAAC,EACE,CAAC,cACP7J,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACH,EAAE,CAAE,CAC5BhB,OAAO,CAAE,MAAM,CACf+B,UAAU,CAAE,QAAQ,CACpBE,cAAc,CAAE,CAAEf,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,UAAW,CACjD,CAAE,CAAAJ,QAAA,cACA1K,IAAA,CAAC7B,MAAM,EACL4N,OAAO,CAAC,WAAW,CACnBlC,KAAK,CAAC,SAAS,CACfU,OAAO,CAAEjI,gBAAiB,CAC1B0K,QAAQ,CAAErM,OAAQ,CAClBsM,SAAS,CAAEtM,OAAO,cAAGX,IAAA,CAAC5B,gBAAgB,EAACgK,IAAI,CAAE,EAAG,CAACyB,KAAK,CAAC,SAAS,CAAE,CAAC,cAAG7J,IAAA,CAACP,WAAW,GAAE,CAAE,CACtFkL,EAAE,CAAE,CACFuC,EAAE,CAAE,CAAErC,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACtBqC,EAAE,CAAE,CAAEtC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBS,YAAY,CAAE,CAAC,CACfO,QAAQ,CAAE,CAAEjB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtCkB,UAAU,CAAE,GAAG,CACfH,SAAS,CAAE,oCAAoC,CAC/CL,UAAU,CAAE,kDAAkD,CAC9D,SAAS,CAAE,CACT3J,SAAS,CAAE,kBAAkB,CAC7BgK,SAAS,CAAE,qCACb,CAAC,CACD,YAAY,CAAE,CACZL,UAAU,CAAE,4CAA4C,CACxD3J,SAAS,CAAE,MACb,CAAC,CACDuL,UAAU,CAAE,eACd,CAAE,CAAA1C,QAAA,CAED7J,YAAY,CAAG,kBAAkB,CAAG,mBAAmB,CAClD,CAAC,CACL,CAAC,EACH,CAAC,CACJ,CAAC,CAELF,OAAO,cACNX,IAAA,CAAClC,GAAG,EAAC6M,EAAE,CAAE,CAAEhB,OAAO,CAAE,MAAM,CAAEiC,cAAc,CAAE,QAAQ,CAAEF,UAAU,CAAE,QAAQ,CAAEJ,MAAM,CAAE,GAAI,CAAE,CAAAZ,QAAA,cACxF1K,IAAA,CAAC5B,gBAAgB,GAAE,CAAC,CACjB,CAAC,CACJyC,YAAY,cACdX,KAAA,CAAAE,SAAA,EAAAsK,QAAA,eACExK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFW,MAAM,CAAE,GAAG,CACXN,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,+EAA+E,CAC3FK,SAAS,CAAE,wCAAwC,CACnDW,MAAM,CAAE,mCAAmC,CAC3CxE,QAAQ,CAAE,UAAU,CACpBqF,QAAQ,CAAE,QACZ,CAAE,CAAA3C,QAAA,eAGF1K,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACF3C,QAAQ,CAAE,UAAU,CACpBmD,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,qEACd,CAAE,CACH,CAAC,cACFxL,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACF3C,QAAQ,CAAE,UAAU,CACpBsF,MAAM,CAAE,CAAC,EAAE,CACXC,IAAI,CAAE,CAAC,EAAE,CACTlC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,oEACd,CAAE,CACH,CAAC,cAGFxL,IAAA,CAACjC,UAAU,EACTgO,OAAO,CAAC,IAAI,CACZpB,EAAE,CAAE,CACFK,EAAE,CAAE,CAAC,CACLgB,UAAU,CAAE,GAAG,CACfnC,KAAK,CAAEnJ,KAAK,CAAC4F,OAAO,CAAC2F,OAAO,CAACnF,IAAI,CACjC0G,SAAS,CAAE,QAAQ,CACnBC,UAAU,CAAE,8BACd,CAAE,CAAA/C,QAAA,CACH,8BAED,CAAY,CAAC,cAGb1K,IAAA,CAACpB,IAAI,EAACgF,IAAI,CAAEwB,SAAU,CAACsI,OAAO,CAAEtG,YAAa,CAAE,CAAC,EAC7C,CAAC,CAELnG,iBAAiB,GAAK,IAAI,eACzBjB,IAAA,CAAClC,GAAG,EAAC6M,EAAE,CAAE,CAAEgD,EAAE,CAAE,CAAC,CAAE3C,EAAE,CAAE,CAAE,CAAE,CAAAN,QAAA,cACxBxK,KAAA,CAAClC,KAAK,EACJ4P,SAAS,CAAE,CAAE,CACbjD,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJyC,QAAQ,CAAE,QAAQ,CAClB9B,YAAY,CAAE,CAAC,CACfM,SAAS,CAAE,gCACb,CAAE,CAAAnB,QAAA,eAGFxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJY,UAAU,CAAE,4EAA4E,CACxF3B,KAAK,CAAE,OAAO,CACd7B,QAAQ,CAAE,UAAU,CACpBqF,QAAQ,CAAE,QACZ,CAAE,CAAA3C,QAAA,eAGF1K,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACF3C,QAAQ,CAAE,UAAU,CACpBmD,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,4EACd,CAAE,CACH,CAAC,cAEFtL,KAAA,CAACnC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAAEqB,UAAU,CAAE,GAAG,CAAEyB,UAAU,CAAE,2BAA4B,CAAE,CAAA/C,QAAA,eACxF1K,IAAA,CAACT,iBAAiB,EAACuM,QAAQ,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAC,CAAEkC,aAAa,CAAE,QAAS,CAAE,CAAE,CAAC,CAC7EhN,YAAY,CAACkD,UAAU,CAAC9C,iBAAiB,CAAC,EACjC,CAAC,cACbjB,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAACpB,EAAE,CAAE,CAAEgD,EAAE,CAAE,GAAG,CAAE/L,OAAO,CAAE,GAAI,CAAE,CAAA8I,QAAA,CAAC,+BAE3D,CAAY,CAAC,EACV,CAAC,cAGN1K,IAAA,CAAClC,GAAG,EAAC6M,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,cAC/BxK,KAAA,CAAChC,IAAI,EAACuO,SAAS,MAACC,OAAO,CAAE,CAAE7B,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACxC1K,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,4BAAApI,MAAA,CACRvC,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1EJ,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1E,qBAAqB,oCACW,CAClCuL,MAAM,cAAApJ,MAAA,CACJvC,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1EJ,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,qBAAqB,CAC1E,qBAAqB,CACrB,CACFqK,MAAM,CAAE,MAAM,CACd3B,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpB8B,SAAS,CAAE,QACb,CAAE,CAAA9C,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,CAAC,YAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAC3BqB,UAAU,CAAE,MAAM,CAClBnC,KAAK,CAAEhJ,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAGP,KAAK,CAAC4F,OAAO,CAAC1B,KAAK,CAAC2B,IAAI,CAC9E1F,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAGP,KAAK,CAAC4F,OAAO,CAACE,OAAO,CAACD,IAAI,CAC/E7F,KAAK,CAAC4F,OAAO,CAACG,OAAO,CAACF,IAC9B,CAAE,CAAAmE,QAAA,CACC7J,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAC/C,CAAC,cACbrJ,IAAA,CAAC3B,IAAI,EACHkH,KAAK,CACH1E,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,WAAW,CAChEJ,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,aAAa,CAAG,UACtE,CACDmH,IAAI,CAAC,OAAO,CACZyB,KAAK,CACHhJ,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,OAAO,CAC5DJ,YAAY,CAACsD,aAAa,CAAClD,iBAAiB,CAAC,CAAG,EAAE,CAAG,SAAS,CAAG,SAClE,CACD0J,EAAE,CAAE,CACFgD,EAAE,CAAE,CAAC,CACL3B,UAAU,CAAE,MAAM,CAClBH,SAAS,CAAE,2BACb,CAAE,CACH,CAAC,EACC,CAAC,CACF,CAAC,cAEP7L,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,6EAA6E,CACzFgB,MAAM,CAAE,+BAA+B,CACvClB,MAAM,CAAE,MAAM,CACd3B,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpB8B,SAAS,CAAE,QACb,CAAE,CAAA9C,QAAA,eAEFxK,KAAA,CAACnC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,eAChF1K,IAAA,CAACJ,gBAAgB,EAACkM,QAAQ,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEgB,EAAE,CAAE,GAAG,CAAEkC,aAAa,CAAE,QAAS,CAAE,CAAE,CAAC,WAEjF,EAAY,CAAC,cACb7N,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAAEqB,UAAU,CAAE,MAAM,CAAEnC,KAAK,CAAE,qBAAsB,CAAE,CAAAa,QAAA,CAC/E7J,YAAY,CAACmD,YAAY,CAAC/C,iBAAiB,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAC9C,CAAC,cACbrJ,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEgD,EAAE,CAAE,GAAI,CAAE,CAAAjD,QAAA,CAAC,aAEpE,CAAY,CAAC,EACV,CAAC,CACF,CAAC,cAEP1K,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,8EAA8E,CAC1FgB,MAAM,CAAE,gCAAgC,CACxClB,MAAM,CAAE,MAAM,CACd3B,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpB8B,SAAS,CAAE,QACb,CAAE,CAAA9C,QAAA,eAEFxK,KAAA,CAACnC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,eAChF1K,IAAA,CAACL,aAAa,EAACmM,QAAQ,CAAC,OAAO,CAACnB,EAAE,CAAE,CAAEgB,EAAE,CAAE,GAAG,CAAEkC,aAAa,CAAE,QAAS,CAAE,CAAE,CAAC,cAE9E,EAAY,CAAC,cACb7N,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAAEqB,UAAU,CAAE,MAAM,CAAEnC,KAAK,CAAE,sBAAuB,CAAE,CAAAa,QAAA,CAChF7J,YAAY,CAACoD,cAAc,CAAChD,iBAAiB,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAChD,CAAC,cACbrJ,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEgD,EAAE,CAAE,GAAI,CAAE,CAAAjD,QAAA,CAAC,QAEpE,CAAY,CAAC,EACV,CAAC,CACF,CAAC,CAEN7J,YAAY,CAACuD,eAAe,eAC3BpE,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,8EAA8E,CAC1FgB,MAAM,CAAE,gCAAgC,CACxClB,MAAM,CAAE,MAAM,CACd3B,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpB8B,SAAS,CAAE,QACb,CAAE,CAAA9C,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,CAAC,aAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAAEqB,UAAU,CAAE,MAAM,CAAEnC,KAAK,CAAE,sBAAuB,CAAE,CAAAa,QAAA,CAChF7J,YAAY,CAACuD,eAAe,CAACnD,iBAAiB,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CACjD,CAAC,cACbrJ,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEgD,EAAE,CAAE,GAAI,CAAE,CAAAjD,QAAA,CAAC,OAEpE,CAAY,CAAC,EACV,CAAC,CACF,CACP,CAEA7J,YAAY,CAACwD,YAAY,eACxBrE,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,+EAA+E,CAC3FgB,MAAM,CAAE,iCAAiC,CACzClB,MAAM,CAAE,MAAM,CACd3B,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpB8B,SAAS,CAAE,QACb,CAAE,CAAA9C,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,CAAC,UAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAAEqB,UAAU,CAAE,MAAM,CAAEnC,KAAK,CAAE,uBAAwB,CAAE,CAAAa,QAAA,CACjF7J,YAAY,CAACwD,YAAY,CAACpD,iBAAiB,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAC9C,CAAC,cACbrJ,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEgD,EAAE,CAAE,GAAI,CAAE,CAAAjD,QAAA,CAAC,GAEpE,CAAY,CAAC,EACV,CAAC,CACF,CACP,cAED1K,IAAA,CAAC9B,IAAI,EAACyO,IAAI,MAAC9B,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cAC9BxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACfC,UAAU,CAAE,8EAA8E,CAC1FgB,MAAM,CAAE,gCAAgC,CACxClB,MAAM,CAAE,MAAM,CACd3B,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpB8B,SAAS,CAAE,QACb,CAAE,CAAA9C,QAAA,eAEF1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,CAAC,WAEnF,CAAY,CAAC,cACb1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAACpB,EAAE,CAAE,CAAEqB,UAAU,CAAE,MAAM,CAAEnC,KAAK,CAAE,sBAAuB,CAAE,CAAAa,QAAA,CAChF7J,YAAY,CAACqD,aAAa,CAACjD,iBAAiB,CAAC,CAACoI,OAAO,CAAC,CAAC,CAAC,CAC/C,CAAC,cACbrJ,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACc,EAAE,CAAE,CAAEgD,EAAE,CAAE,GAAI,CAAE,CAAAjD,QAAA,CAAC,SAEpE,CAAY,CAAC,EACV,CAAC,CACF,CAAC,EACH,CAAC,CACJ,CAAC,EACD,CAAC,CACL,CACN,cAEDxK,KAAA,CAACpC,GAAG,EACF6M,EAAE,CAAE,CACFgD,EAAE,CAAE,CAAC,CACL/C,CAAC,CAAE,CAAC,CACJW,YAAY,CAAE,CAAC,CACf7F,eAAe,CAAE,yBAAyB,CAC1C8G,MAAM,CAAE,oCAAoC,CAC5C7C,OAAO,CAAE,MAAM,CACf+B,UAAU,CAAE,YACd,CAAE,CAAAhB,QAAA,eAEF1K,IAAA,CAACN,QAAQ,EACPoM,QAAQ,CAAC,OAAO,CAChBnB,EAAE,CAAE,CACFgB,EAAE,CAAE,GAAG,CACPgC,EAAE,CAAE,GAAG,CACP9D,KAAK,CAAEnJ,KAAK,CAAC4F,OAAO,CAAC2F,OAAO,CAAC1F,IAC/B,CAAE,CACH,CAAC,cACFrG,KAAA,CAACpC,GAAG,EAAA4M,QAAA,eACFxK,KAAA,CAACnC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAACpB,EAAE,CAAE,CAAEd,KAAK,CAAEnJ,KAAK,CAAC4F,OAAO,CAAC2F,OAAO,CAACnF,IAAI,CAAEkF,UAAU,CAAE,GAAG,CAAEhB,EAAE,CAAE,GAAI,CAAE,CAAAN,QAAA,EAAC,yCACxD,CAAC7J,YAAY,CAACuB,QAAQ,EAAIhB,gBAAgB,CAACE,IAAI,CAAC,IAAE,CAACF,gBAAgB,CAACG,KAAK,EACtG,CAAC,cACbrB,KAAA,CAACnC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAAAa,QAAA,EAAC,wGAEjD,cAAA1K,IAAA,WAAA0K,QAAA,CAAQ,kCAAgC,CAAQ,CAAC,iJAEjD,CAAC7J,YAAY,CAACuD,eAAe,EAAI,+DAA+D,EACtF,CAAC,EACV,CAAC,EACH,CAAC,EACN,CAAC,cAEHlE,KAAA,CAACpC,GAAG,EAAC6M,EAAE,CAAE,CACPhB,OAAO,CAAE,MAAM,CACfmE,aAAa,CAAE,QAAQ,CACvBlC,cAAc,CAAE,QAAQ,CACxBF,UAAU,CAAE,QAAQ,CACpBJ,MAAM,CAAE,GAAG,CACXE,UAAU,CAAE,+EAA+E,CAC3FD,YAAY,CAAE,CAAC,CACfiB,MAAM,CAAE,oCAAoC,CAC5CxE,QAAQ,CAAE,UAAU,CACpBqF,QAAQ,CAAE,QAAQ,CAClBzC,CAAC,CAAE,CACL,CAAE,CAAAF,QAAA,eAEA1K,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACF3C,QAAQ,CAAE,UAAU,CACpBmD,GAAG,CAAE,CAAC,EAAE,CACRC,KAAK,CAAE,CAAC,EAAE,CACVC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,qEACd,CAAE,CACH,CAAC,cACFxL,IAAA,CAAClC,GAAG,EACF6M,EAAE,CAAE,CACF3C,QAAQ,CAAE,UAAU,CACpBsF,MAAM,CAAE,CAAC,EAAE,CACXC,IAAI,CAAE,CAAC,EAAE,CACTlC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,KAAK,CACnBC,UAAU,CAAE,qEACd,CAAE,CACH,CAAC,cAEFxL,IAAA,CAACR,cAAc,EAACmL,EAAE,CAAE,CAClBmB,QAAQ,CAAE,EAAE,CACZjC,KAAK,CAAE,yBAAyB,CAChCmB,EAAE,CAAE,CAAC,CACL+C,MAAM,CAAE,2CACV,CAAE,CAAE,CAAC,cAEL/N,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,IAAI,CAAClC,KAAK,CAAC,cAAc,CAACmE,KAAK,CAAC,QAAQ,CAACrD,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEgB,UAAU,CAAE,GAAI,CAAE,CAAAtB,QAAA,CAAC,gCAE7F,CAAY,CAAC,cAEb1K,IAAA,CAACjC,UAAU,EAACgO,OAAO,CAAC,OAAO,CAAClC,KAAK,CAAC,gBAAgB,CAACmE,KAAK,CAAC,QAAQ,CAACrD,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAC,CAAEqB,QAAQ,CAAE,GAAI,CAAE,CAAA3B,QAAA,CAAC,sHAEhG,CAAY,CAAC,cAEb1K,IAAA,CAAC7B,MAAM,EACL4N,OAAO,CAAC,WAAW,CACnBlC,KAAK,CAAC,SAAS,CACfU,OAAO,CAAEjI,gBAAiB,CAC1B0K,QAAQ,CAAErM,OAAQ,CAClBsM,SAAS,CAAEtM,OAAO,cAAGX,IAAA,CAAC5B,gBAAgB,EAACgK,IAAI,CAAE,EAAG,CAACyB,KAAK,CAAC,SAAS,CAAE,CAAC,cAAG7J,IAAA,CAACP,WAAW,GAAE,CAAE,CACtFkL,EAAE,CAAE,CACFwC,EAAE,CAAE,CAAC,CACLD,EAAE,CAAE,GAAG,CACP3B,YAAY,CAAE,CAAC,CACfM,SAAS,CAAE,oCAAoC,CAC/C,SAAS,CAAE,CACTA,SAAS,CAAE,oCAAoC,CAC/ChK,SAAS,CAAE,kBACb,CAAC,CACDuL,UAAU,CAAE,eACd,CAAE,CAAA1C,QAAA,CACH,mBAED,CAAQ,CAAC,EACN,CACN,EACE,CAAC,CACM,CAAC,CAEnB,CAAC,CAED,cAAe,CAAApK,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}