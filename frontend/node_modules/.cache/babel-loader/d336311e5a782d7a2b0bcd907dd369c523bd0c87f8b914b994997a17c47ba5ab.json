{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Downloads/Flood/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect}from'react';import{ThemeProvider,createTheme}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';import{Container,Box,Paper,Typography,Grid,Alert,Chip}from'@mui/material';import Header from'./components/Header';import FloodMap from'./components/FloodMap';import PredictionForm from'./components/PredictionForm';import ResultDisplay from'./components/ResultDisplay';import RiskFactorsChart from'./components/RiskFactorsChart';import TimelineRiskPredictor from'./components/TimelineRiskPredictor';import VoiceEmergencyAssistant from'./components/VoiceEmergencyAssistant';import CommunityReports from'./components/CommunityReports';import{SlideUp,ScaleIn}from'./components/animations/AnimatedComponents';import LoadingAnimation from'./components/animations/LoadingAnimation';import axios from'axios';import{neuromorphicStyles,getNeuromorphicPalette,getNeuromorphicShadow,getPressedEffect}from'./theme/neuromorphicUtils';// Create a theme with neuromorphic design\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const theme=createTheme({palette:_objectSpread(_objectSpread({mode:'light'},getNeuromorphicPalette('#3A86FF')),{},{info:{main:'#4CC9F0',// Light blue\nlight:'#e6f7fc',dark:'#3AA1C0',contrastText:'#ffffff'},divider:'rgba(0, 0, 0, 0.08)'}),typography:{fontFamily:'\"Poppins\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',h1:{fontSize:'clamp(2.5rem, 5vw, 3.5rem)',fontWeight:800,letterSpacing:'-0.01em',lineHeight:1.2},h2:{fontSize:'clamp(2rem, 4vw, 2.75rem)',fontWeight:700,letterSpacing:'-0.01em',lineHeight:1.2},h3:{fontSize:'clamp(1.5rem, 3vw, 2rem)',fontWeight:600,letterSpacing:'-0.01em',lineHeight:1.3},h4:{fontSize:'clamp(1.25rem, 2.5vw, 1.75rem)',fontWeight:600,letterSpacing:'-0.01em',lineHeight:1.4},h5:{fontSize:'clamp(1.1rem, 2vw, 1.5rem)',fontWeight:500,letterSpacing:'-0.01em',lineHeight:1.4},h6:{fontSize:'clamp(1rem, 1.5vw, 1.25rem)',fontWeight:500,letterSpacing:'-0.01em',lineHeight:1.5},body1:{fontSize:'clamp(0.875rem, 1.5vw, 1rem)',lineHeight:1.6},body2:{fontSize:'clamp(0.8125rem, 1.25vw, 0.875rem)',lineHeight:1.6},button:{fontWeight:600,textTransform:'none',letterSpacing:'0.02em',fontSize:'clamp(0.875rem, 1.5vw, 1rem)'},subtitle1:{fontSize:'clamp(0.9375rem, 1.75vw, 1.125rem)',fontWeight:500,lineHeight:1.5},subtitle2:{fontSize:'clamp(0.8125rem, 1.5vw, 0.9375rem)',fontWeight:500,lineHeight:1.5}},shape:{borderRadius:12},shadows:['none','0px 2px 4px rgba(0, 0, 0, 0.03), 0px 1px 2px rgba(0, 0, 0, 0.06)','0px 4px 6px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.06)','0px 6px 8px rgba(0, 0, 0, 0.04), 0px 3px 6px rgba(0, 0, 0, 0.06)','0px 8px 12px rgba(0, 0, 0, 0.05), 0px 4px 8px rgba(0, 0, 0, 0.06)','0px 10px 15px rgba(0, 0, 0, 0.05), 0px 6px 10px rgba(0, 0, 0, 0.06)','0px 12px 18px rgba(0, 0, 0, 0.05), 0px 7px 12px rgba(0, 0, 0, 0.06)','0px 14px 21px rgba(0, 0, 0, 0.05), 0px 8px 14px rgba(0, 0, 0, 0.06)','0px 16px 24px rgba(0, 0, 0, 0.05), 0px 9px 16px rgba(0, 0, 0, 0.06)','0px 18px 27px rgba(0, 0, 0, 0.05), 0px 10px 18px rgba(0, 0, 0, 0.06)','0px 20px 30px rgba(0, 0, 0, 0.05), 0px 11px 20px rgba(0, 0, 0, 0.06)','0px 22px 33px rgba(0, 0, 0, 0.05), 0px 12px 22px rgba(0, 0, 0, 0.06)','0px 24px 36px rgba(0, 0, 0, 0.05), 0px 13px 24px rgba(0, 0, 0, 0.06)','0px 26px 39px rgba(0, 0, 0, 0.05), 0px 14px 26px rgba(0, 0, 0, 0.06)','0px 28px 42px rgba(0, 0, 0, 0.05), 0px 15px 28px rgba(0, 0, 0, 0.06)','0px 30px 45px rgba(0, 0, 0, 0.05), 0px 16px 30px rgba(0, 0, 0, 0.06)','0px 32px 48px rgba(0, 0, 0, 0.05), 0px 17px 32px rgba(0, 0, 0, 0.06)','0px 34px 51px rgba(0, 0, 0, 0.05), 0px 18px 34px rgba(0, 0, 0, 0.06)','0px 36px 54px rgba(0, 0, 0, 0.05), 0px 19px 36px rgba(0, 0, 0, 0.06)','0px 38px 57px rgba(0, 0, 0, 0.05), 0px 20px 38px rgba(0, 0, 0, 0.06)','0px 40px 60px rgba(0, 0, 0, 0.05), 0px 21px 40px rgba(0, 0, 0, 0.06)','0px 42px 63px rgba(0, 0, 0, 0.05), 0px 22px 42px rgba(0, 0, 0, 0.06)','0px 44px 66px rgba(0, 0, 0, 0.05), 0px 23px 44px rgba(0, 0, 0, 0.06)','0px 46px 69px rgba(0, 0, 0, 0.05), 0px 24px 46px rgba(0, 0, 0, 0.06)'],components:{MuiContainer:{styleOverrides:{root:{paddingLeft:{xs:16,sm:24,md:32},paddingRight:{xs:16,sm:24,md:32},maxWidth:{xs:'100%',sm:'100%',md:'100%',lg:'1280px',xl:'1920px'}}}},MuiGrid:{styleOverrides:{container:{marginTop:{xs:2,sm:3,md:4},marginBottom:{xs:2,sm:3,md:4}},item:{paddingTop:{xs:1,sm:1.5,md:2},paddingBottom:{xs:1,sm:1.5,md:2}}}},MuiPaper:{styleOverrides:{root:{backgroundColor:'#f0f4f8',borderRadius:16,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",transition:'all 0.3s ease-in-out',padding:{xs:2,sm:3,md:4},border:'none','&:hover':{boxShadow:\"\\n              8px 8px 16px rgba(174, 174, 192, 0.35),\\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\\n            \"}}}},MuiButton:{styleOverrides:{root:{backgroundColor:'#f0f4f8',borderRadius:12,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",transition:'all 0.2s ease',border:'none',padding:{xs:'10px 20px',sm:'12px 24px',md:'14px 28px'},fontSize:{xs:'0.875rem',sm:'0.9375rem',md:'1rem'},'&:hover':{boxShadow:\"\\n              8px 8px 16px rgba(174, 174, 192, 0.35),\\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\\n            \",backgroundColor:'#f0f4f8'},'&:active':{boxShadow:\"\\n              inset 4px 4px 8px rgba(174, 174, 192, 0.3),\\n              inset -4px -4px 8px rgba(255, 255, 255, 0.5)\\n            \",backgroundColor:'#f0f4f8'}},containedPrimary:{color:'#ffffff',backgroundColor:'#3A86FF',boxShadow:\"\\n            6px 6px 12px rgba(58, 134, 255, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",'&:hover':{backgroundColor:'#2a6bc9'},'&:active':{boxShadow:\"\\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\\n            \"}},containedSecondary:{color:'#ffffff',backgroundColor:'#FF595E',boxShadow:\"\\n            6px 6px 12px rgba(255, 89, 94, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",'&:hover':{backgroundColor:'#d04649'},'&:active':{boxShadow:\"\\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\\n            \"}},outlined:{backgroundColor:'transparent',boxShadow:'none',border:'2px solid #f0f4f8','&:hover':{boxShadow:\"\\n              4px 4px 8px rgba(174, 174, 192, 0.2),\\n              -4px -4px 8px rgba(255, 255, 255, 0.3)\\n            \"},'&:active':{boxShadow:\"\\n              inset 2px 2px 4px rgba(174, 174, 192, 0.2),\\n              inset -2px -2px 4px rgba(255, 255, 255, 0.3)\\n            \"}}}},MuiTextField:{styleOverrides:{root:{transition:'all 0.3s ease',marginBottom:{xs:2,sm:2.5,md:3},'& .MuiOutlinedInput-root':{backgroundColor:'#f0f4f8',borderRadius:12,boxShadow:\"\\n              inset 2px 2px 5px rgba(174, 174, 192, 0.2),\\n              inset -2px -2px 5px rgba(255, 255, 255, 0.7)\\n            \",border:'none','& fieldset':{border:'none'},'&:hover fieldset':{border:'none'},'&.Mui-focused':{boxShadow:\"\\n                inset 4px 4px 8px rgba(174, 174, 192, 0.3),\\n                inset -4px -4px 8px rgba(255, 255, 255, 0.5)\\n              \",'& fieldset':{border:'none'}}},'& .MuiInputLabel-root':{fontSize:{xs:'0.875rem',sm:'0.9375rem',md:'1rem'},color:'#1a202c',fontWeight:500,'&.Mui-focused':{color:'#3A86FF',fontWeight:600}},'& .MuiInputBase-input':{padding:'16px 14px',color:'#1a202c',fontWeight:500}}}},MuiSlider:{styleOverrides:{root:{height:10,'& .MuiSlider-track':{border:'none',boxShadow:'inset 1px 1px 2px rgba(174, 174, 192, 0.3)',backgroundColor:'#3A86FF'},'& .MuiSlider-rail':{boxShadow:\"\\n              inset 2px 2px 4px rgba(174, 174, 192, 0.3),\\n              inset -2px -2px 4px rgba(255, 255, 255, 0.5)\\n            \",backgroundColor:'#f0f4f8',opacity:1},'& .MuiSlider-thumb':{height:24,width:24,backgroundColor:'#f0f4f8',boxShadow:\"\\n              6px 6px 12px rgba(174, 174, 192, 0.3),\\n              -6px -6px 12px rgba(255, 255, 255, 0.5)\\n            \",'&:focus, &:hover, &.Mui-active, &.Mui-focusVisible':{boxShadow:\"\\n                8px 8px 16px rgba(174, 174, 192, 0.35),\\n                -8px -8px 16px rgba(255, 255, 255, 0.6)\\n              \"}}}}},MuiCard:{styleOverrides:{root:{backgroundColor:'#f0f4f8',borderRadius:16,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",transition:'all 0.3s ease-in-out',border:'none',overflow:'hidden','&:hover':{boxShadow:\"\\n              8px 8px 16px rgba(174, 174, 192, 0.35),\\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\\n            \"}}}},MuiCardContent:{styleOverrides:{root:{padding:{xs:'20px',sm:'24px',md:'28px'},'&:last-child':{paddingBottom:{xs:'20px',sm:'24px',md:'28px'}}}}},MuiAlert:{styleOverrides:{root:{borderRadius:16,boxShadow:\"\\n            6px 6px 12px rgba(174, 174, 192, 0.3),\\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\\n          \",fontSize:{xs:'0.875rem',sm:'0.9375rem',md:'1rem'},fontWeight:500},standardSuccess:{backgroundColor:'rgba(6, 214, 160, 0.1)',color:'#05ab80','& .MuiAlert-icon':{color:'#06d6a0'}},standardError:{backgroundColor:'rgba(255, 89, 94, 0.1)',color:'#d04649','& .MuiAlert-icon':{color:'#ff595e'}},standardWarning:{backgroundColor:'rgba(255, 159, 28, 0.1)',color:'#d18016','& .MuiAlert-icon':{color:'#ff9f1c'}},standardInfo:{backgroundColor:'rgba(76, 201, 240, 0.1)',color:'#3aa8cc','& .MuiAlert-icon':{color:'#4cc9f0'}}}},MuiChip:{styleOverrides:{root:{borderRadius:12,backgroundColor:'#f0f4f8',boxShadow:\"\\n            2px 2px 4px rgba(174, 174, 192, 0.3),\\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\\n          \",fontSize:{xs:'0.75rem',sm:'0.8125rem',md:'0.875rem'},color:'#1a202c',fontWeight:500,'&:hover':{boxShadow:\"\\n              3px 3px 6px rgba(174, 174, 192, 0.35),\\n              -3px -3px 6px rgba(255, 255, 255, 0.6)\\n            \"}},colorPrimary:{backgroundColor:'#3A86FF',color:'#ffffff'},colorSecondary:{backgroundColor:'#FF595E',color:'#ffffff'},colorSuccess:{backgroundColor:'#06d6a0',color:'#ffffff'},colorError:{backgroundColor:'#ff595e',color:'#ffffff'},colorWarning:{backgroundColor:'#ff9f1c',color:'#ffffff'},colorInfo:{backgroundColor:'#4cc9f0',color:'#ffffff'}}},MuiTypography:{styleOverrides:{root:{marginBottom:{xs:1,sm:1.5,md:2},color:'#1a202c'},h1:{color:'#1a202c',fontWeight:800},h2:{color:'#1a202c',fontWeight:700},h3:{color:'#1a202c',fontWeight:700},h4:{color:'#1a202c',fontWeight:600},h5:{color:'#1a202c',fontWeight:600},h6:{color:'#1a202c',fontWeight:600},subtitle1:{color:'#2d3748',fontWeight:500},subtitle2:{color:'#2d3748',fontWeight:500},body1:{color:'#2d3748'},body2:{color:'#4a5568'}}},MuiSwitch:{styleOverrides:{root:{width:56,height:32,padding:0},switchBase:{padding:4,'&.Mui-checked':{transform:'translateX(24px)','& + .MuiSwitch-track':{opacity:1,backgroundColor:'#e6eef8'}}},thumb:{width:24,height:24,backgroundColor:'#f0f4f8',boxShadow:\"\\n            2px 2px 4px rgba(174, 174, 192, 0.3),\\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\\n          \"},track:{opacity:1,borderRadius:16,backgroundColor:'#e6eef8',boxShadow:\"\\n            inset 2px 2px 4px rgba(174, 174, 192, 0.3),\\n            inset -2px -2px 4px rgba(255, 255, 255, 0.5)\\n          \"}}}}});function App(){const[mapData,setMapData]=useState([]);const[options,setOptions]=useState({land_cover:[],soil_type:[]});const[prediction,setPrediction]=useState(null);const[loading,setLoading]=useState(false);const[initialLoading,setInitialLoading]=useState(true);const[showPredictionResult,setShowPredictionResult]=useState(false);const[forecastSummary,setForecastSummary]=useState(null);const[showForecastAlert,setShowForecastAlert]=useState(false);useEffect(()=>{// Fetch map data and options when component mounts\nconst fetchData=async()=>{setInitialLoading(true);let loadingTimer;try{const[mapResponse,optionsResponse]=await Promise.all([axios.get('/api/map-data'),axios.get('/api/options')]);setMapData(mapResponse.data);setOptions(optionsResponse.data);}catch(error){console.error('Error fetching data:',error);}finally{// Add a slight delay to make the loading animation visible\n// but ensure it gets cleared if component unmounts\nloadingTimer=setTimeout(()=>{setInitialLoading(false);},1500);}// Cleanup function to ensure loading state is reset if component unmounts\nreturn()=>{if(loadingTimer)clearTimeout(loadingTimer);setInitialLoading(false);};};fetchData();},[]);const handleSubmit=async formData=>{setLoading(true);setShowPredictionResult(false);setShowForecastAlert(false);try{// Add a slight delay to make the loading animation visible\nawait new Promise(resolve=>setTimeout(resolve,1200));const response=await axios.post('/api/predict',formData);setPrediction(response.data);// Add a slight delay before showing the result for better animation\nsetTimeout(()=>{setShowPredictionResult(true);},300);}catch(error){console.error('Error making prediction:',error);}finally{setLoading(false);}};const handleForecastGenerated=summary=>{setForecastSummary(summary);setShowForecastAlert(true);// Hide the alert after 10 seconds\nsetTimeout(()=>{setShowForecastAlert(false);},10000);};return/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(VoiceEmergencyAssistant,{}),initialLoading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',minHeight:'80vh',background:'#e6eef8'},children:[/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',width:200,height:200},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,width:'100%',height:'100%',borderRadius:'50%',border:'4px solid transparent',borderTopColor:theme.palette.primary.main,animation:'spin 1.5s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:15,left:15,width:'calc(100% - 30px)',height:'calc(100% - 30px)',borderRadius:'50%',border:'4px solid transparent',borderTopColor:theme.palette.secondary.main,animation:'spin 2s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:30,left:30,width:'calc(100% - 60px)',height:'calc(100% - 60px)',borderRadius:'50%',border:'4px solid transparent',borderTopColor:theme.palette.info.main,animation:'spin 2.5s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}})]}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{mt:4,fontWeight:600,background:'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',backgroundClip:'text',textFillColor:'transparent',animation:'pulse 2s infinite','@keyframes pulse':{'0%':{opacity:0.6},'50%':{opacity:1},'100%':{opacity:0.6}}},children:\"Loading Flood Prediction System...\"})]}):/*#__PURE__*/_jsx(Container,{maxWidth:\"xl\",sx:{mt:{xs:2,sm:3,md:4},mb:{xs:4,sm:6,md:8},px:{xs:2,sm:3,md:4},py:{xs:3,sm:4,md:5},overflow:'hidden',backgroundColor:'#e6eef8',borderRadius:'24px',boxShadow:\"\\n              inset 1px 1px 2px rgba(255, 255, 255, 0.5),\\n              inset -1px -1px 2px rgba(174, 174, 192, 0.3)\\n            \"},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:{xs:3,sm:4,md:5},sx:{'& .MuiGrid-item':{display:'flex',flexDirection:'column'}},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,md:4},mb:3,backgroundColor:'#f0f4f8',position:'relative',overflow:'hidden',borderRadius:2,transition:'all 0.3s ease-in-out',boxShadow:\"\\n                    6px 6px 12px rgba(174, 174, 192, 0.3),\\n                    -6px -6px 12px rgba(255, 255, 255, 0.5)\\n                  \",'&:hover':{boxShadow:\"\\n                      8px 8px 16px rgba(174, 174, 192, 0.35),\\n                      -8px -8px 16px rgba(255, 255, 255, 0.6)\\n                    \"}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'180px',height:'180px',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,left:0,width:'120px',height:'120px',background:'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 100% 0 0',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1.5,borderRadius:'50%',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 12px rgba(76, 201, 240, 0.15)'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"span\",children:\"\\uD83C\\uDF0A\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h2\",component:\"h1\",sx:{background:'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',backgroundClip:'text',textFillColor:'transparent',fontWeight:800,letterSpacing:'-0.5px'},children:\"Flood Risk Prediction System\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,sx:{fontSize:'1.1rem',maxWidth:'90%',color:'text.secondary',lineHeight:1.6,mb:2},children:\"This interactive tool helps predict flood risk based on various environmental and geographical factors. For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2,flexWrap:'wrap',mt:3},children:[/*#__PURE__*/_jsx(Chip,{label:\"Real-time Weather Data\",color:\"primary\",size:\"medium\",icon:/*#__PURE__*/_jsx(\"span\",{children:\"\\u26C8\\uFE0F\"}),sx:{fontWeight:500,px:1}}),/*#__PURE__*/_jsx(Chip,{label:\"Historical Flood Analysis\",color:\"info\",size:\"medium\",icon:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCA\"}),sx:{fontWeight:500,px:1}}),/*#__PURE__*/_jsx(Chip,{label:\"Indian Cities Database\",color:\"success\",size:\"medium\",icon:/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFD9\\uFE0F\"}),sx:{fontWeight:500,px:1}})]})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,md:4},height:'100%',position:'relative',overflow:'hidden',borderRadius:2,transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,width:'100px',height:'100px',background:'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 100% 0',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,right:0,width:'80px',height:'80px',background:'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'100% 0 0 0',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDCDD\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.main,letterSpacing:'-0.5px'},children:\"Predict Flood Risk\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mb:3,color:'text.secondary',maxWidth:'95%'},children:\"Enter location and environmental factors to get a precise flood risk assessment. For Indian cities, we provide enhanced accuracy using historical data.\"}),/*#__PURE__*/_jsx(PredictionForm,{options:options,onSubmit:handleSubmit,loading:loading})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,lg:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,md:4},height:'100%',position:'relative',overflow:'hidden',borderRadius:2,transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'100px',height:'100px',background:'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:0,left:0,width:'80px',height:'80px',background:'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 100% 0 0',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'relative',zIndex:1},children:loading?/*#__PURE__*/_jsx(LoadingAnimation,{theme:theme}):prediction&&showPredictionResult?/*#__PURE__*/_jsxs(Box,{sx:{opacity:showPredictionResult?1:0,transform:showPredictionResult?'translateY(0)':'translateY(20px)',transition:'all 0.5s ease-in-out'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDCCA\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.main,letterSpacing:'-0.5px'},children:\"Risk Assessment\"})]}),/*#__PURE__*/_jsx(ResultDisplay,{prediction:prediction})]}):/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',flexDirection:'column',alignItems:'center',justifyContent:'center',py:8,opacity:loading?0:1,transition:'opacity 0.3s ease-in-out'},children:[/*#__PURE__*/_jsx(Box,{sx:{width:100,height:100,borderRadius:'50%',display:'flex',alignItems:'center',justifyContent:'center',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',boxShadow:'0 8px 32px rgba(76, 201, 240, 0.12)',mb:3,animation:'float 3s ease-in-out infinite','@keyframes float':{'0%':{transform:'translateY(0px)'},'50%':{transform:'translateY(-10px)'},'100%':{transform:'translateY(0px)'}}},children:/*#__PURE__*/_jsx(Typography,{variant:\"h2\",component:\"span\",children:\"\\uD83D\\uDCCA\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h5\",sx:{fontWeight:600,color:theme.palette.primary.main,textAlign:'center',mb:1},children:\"Results will appear here\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"text.secondary\",sx:{textAlign:'center',maxWidth:'80%',mx:'auto'},children:\"Fill out the form on the left to generate a detailed flood risk assessment\"})]})})]})}),prediction&&prediction.risk_assessment&&showPredictionResult&&/*#__PURE__*/_jsx(SlideUp,{delay:200,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:4,sx:{p:{xs:4,sm:5,md:6},background:'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',borderRadius:4,position:'relative',overflow:'hidden',transition:'all 0.3s ease-in-out',border:'1px solid rgba(58, 134, 255, 0.08)','&:hover':{boxShadow:'0 12px 32px rgba(58, 134, 255, 0.15)',transform:'translateY(-2px)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:-30,right:-30,width:'200px',height:'200px',background:'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',borderRadius:'50%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',bottom:-40,left:-40,width:'160px',height:'160px',background:'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'50%',zIndex:0}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:'50%',right:'10%',width:'80px',height:'80px',background:'radial-gradient(circle, rgba(255,159,28,0.06) 0%, rgba(0,0,0,0) 70%)',borderRadius:'50%',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:{xs:4,sm:5},pb:2,borderBottom:'2px solid rgba(58, 134, 255, 0.1)'},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:{xs:2,sm:3},p:{xs:1.5,sm:2},borderRadius:3,background:'linear-gradient(135deg, rgba(255, 89, 94, 0.15) 0%, rgba(255, 159, 28, 0.15) 100%)',display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 4px 12px rgba(255, 89, 94, 0.2)'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h4\",component:\"span\",children:\"\\uD83D\\uDCC8\"})}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h3\",sx:{fontWeight:800,color:theme.palette.primary.dark,letterSpacing:'-0.8px',mb:0.5},children:\"Risk Factor Analysis\"}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{color:'text.secondary',fontWeight:500},children:\"Detailed Environmental Impact Assessment\"})]})]}),/*#__PURE__*/_jsx(Box,{sx:{mb:{xs:4,sm:5}},children:/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{fontSize:{xs:'1rem',sm:'1.1rem'},lineHeight:1.7,color:'text.secondary',maxWidth:'900px'},children:[\"This comprehensive analysis shows how different environmental and geographical factors contribute to the overall flood risk assessment.\",prediction.accurate_data&&\" For Indian cities, this includes historical flood data and real-time weather conditions from our enhanced database.\"]})}),/*#__PURE__*/_jsx(Box,{sx:{p:{xs:2,sm:3},borderRadius:3,background:'rgba(58, 134, 255, 0.02)',border:'1px solid rgba(58, 134, 255, 0.1)'},children:/*#__PURE__*/_jsx(RiskFactorsChart,{riskAssessment:prediction.risk_assessment})})]})]})})}),showForecastAlert&&forecastSummary&&/*#__PURE__*/_jsx(ScaleIn,{delay:100,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Alert,{severity:forecastSummary.maxRiskScore>70?\"error\":forecastSummary.maxRiskScore>40?\"warning\":\"info\",variant:\"filled\",sx:{mb:3,borderRadius:2,boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)','& .MuiAlert-icon':{fontSize:'1.8rem'},p:2,animation:'pulse 2s infinite','@keyframes pulse':{'0%':{boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)'},'50%':{boxShadow:'0 8px 36px rgba(0, 0, 0, 0.25)'},'100%':{boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)'}}},children:/*#__PURE__*/_jsx(Box,{sx:{display:'flex',alignItems:'flex-start'},children:/*#__PURE__*/_jsxs(Box,{sx:{flexGrow:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",sx:{fontWeight:'bold',mb:0.5},children:forecastSummary.maxRiskScore>70?\"⚠️ High flood risk detected in the forecast!\":forecastSummary.maxRiskScore>40?\"⚠️ Medium flood risk detected in the forecast\":\"ℹ️ Flood risk forecast generated\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",sx:{fontWeight:500},children:[\"Location: \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.location||'Selected area'})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body1\",children:[\"Peak risk score of \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.maxRiskScore.toFixed(1)}),\" expected around \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.maxRiskTime}),\". Risk trend is \",/*#__PURE__*/_jsx(\"strong\",{children:forecastSummary.riskTrend}),\".\"]}),forecastSummary.maxRiskScore>60&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:1,fontStyle:'italic'},children:\"Please monitor local weather updates and follow emergency guidelines.\"})]})})})})}),prediction&&showPredictionResult&&/*#__PURE__*/_jsx(SlideUp,{delay:300,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Box,{sx:{p:{xs:1,sm:2},borderRadius:4,background:'linear-gradient(135deg, rgba(58, 134, 255, 0.02) 0%, rgba(76, 201, 240, 0.02) 100%)',border:'1px solid rgba(58, 134, 255, 0.08)'},children:/*#__PURE__*/_jsx(Paper,{elevation:4,sx:{borderRadius:4,overflow:'hidden',transition:'all 0.3s ease-in-out',background:'linear-gradient(135deg, #ffffff 0%, #f8faff 100%)','&:hover':{boxShadow:'0 12px 32px rgba(58, 134, 255, 0.15)',transform:'translateY(-2px)'}},children:/*#__PURE__*/_jsx(TimelineRiskPredictor,{formData:prediction.input_data,onForecastGenerated:handleForecastGenerated})})})})}),/*#__PURE__*/_jsx(SlideUp,{delay:200,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,md:4},background:'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',borderRadius:2,position:'relative',overflow:'hidden',transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'150px',height:'150px',background:'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDDFA\\uFE0F\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.dark,letterSpacing:'-0.5px'},children:\"Flood Risk Map\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,sx:{mb:3,color:'text.secondary',maxWidth:'800px'},children:\"This interactive map shows areas with predicted flood risk based on our analysis. Green markers indicate low risk areas, while red markers indicate high risk zones. Click on markers to see detailed information.\"}),/*#__PURE__*/_jsx(Box,{sx:{borderRadius:2,overflow:'hidden',boxShadow:'0 4px 20px rgba(0, 0, 0, 0.08)',height:'500px'},children:/*#__PURE__*/_jsx(FloodMap,{mapData:mapData})})]})]})})}),/*#__PURE__*/_jsx(SlideUp,{delay:300,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:3,md:4},background:'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',borderRadius:2,position:'relative',overflow:'hidden',transition:'all 0.3s ease-in-out','&:hover':{boxShadow:'0 8px 24px rgba(58, 134, 255, 0.12)'}},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'150px',height:'150px',background:'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:2,p:1,borderRadius:'12px',background:'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',display:'flex',alignItems:'center',justifyContent:'center'},children:/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"span\",children:\"\\uD83D\\uDC65\"})}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",sx:{fontWeight:700,color:theme.palette.primary.dark,letterSpacing:'-0.5px'},children:\"Community Reports\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,sx:{mb:3,color:'text.secondary',maxWidth:'800px'},children:\"View and submit real-time flood reports from community members. These reports help validate our predictions and provide valuable on-the-ground information during flood events.\"}),/*#__PURE__*/_jsx(CommunityReports,{})]})]})})})]})})]});}export default App;", "map": {"version": 3, "names": ["useState", "useEffect", "ThemeProvider", "createTheme", "CssBaseline", "Container", "Box", "Paper", "Typography", "Grid", "<PERSON><PERSON>", "Chip", "Header", "FloodMap", "PredictionForm", "ResultDisplay", "RiskFactorsChart", "TimelineRiskPredictor", "VoiceEmergencyAssistant", "CommunityReports", "SlideUp", "ScaleIn", "LoadingAnimation", "axios", "neuromorphicStyles", "getNeuromorphicPalette", "getNeuromorphicShadow", "getPressedEffect", "jsx", "_jsx", "jsxs", "_jsxs", "theme", "palette", "_objectSpread", "mode", "info", "main", "light", "dark", "contrastText", "divider", "typography", "fontFamily", "h1", "fontSize", "fontWeight", "letterSpacing", "lineHeight", "h2", "h3", "h4", "h5", "h6", "body1", "body2", "button", "textTransform", "subtitle1", "subtitle2", "shape", "borderRadius", "shadows", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "styleOverrides", "root", "paddingLeft", "xs", "sm", "md", "paddingRight", "max<PERSON><PERSON><PERSON>", "lg", "xl", "MuiGrid", "container", "marginTop", "marginBottom", "item", "paddingTop", "paddingBottom", "MuiPaper", "backgroundColor", "boxShadow", "transition", "padding", "border", "MuiB<PERSON>on", "containedPrimary", "color", "containedSecondary", "outlined", "MuiTextField", "<PERSON>i<PERSON><PERSON><PERSON>", "height", "opacity", "width", "MuiCard", "overflow", "MuiCardContent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "standardSuccess", "standardError", "standardWarning", "standardInfo", "MuiChip", "colorPrimary", "colorSecondary", "colorSuccess", "colorError", "colorWarning", "colorInfo", "MuiTypography", "MuiSwitch", "switchBase", "transform", "thumb", "track", "App", "mapData", "setMapData", "options", "setOptions", "land_cover", "soil_type", "prediction", "setPrediction", "loading", "setLoading", "initialLoading", "setInitialLoading", "showPredictionResult", "setShowPredictionResult", "forecastSummary", "setForecastSummary", "showForecastAlert", "setShowForecastAlert", "fetchData", "loadingTimer", "mapResponse", "optionsResponse", "Promise", "all", "get", "data", "error", "console", "setTimeout", "clearTimeout", "handleSubmit", "formData", "resolve", "response", "post", "handleForecastGenerated", "summary", "children", "sx", "display", "flexDirection", "alignItems", "justifyContent", "minHeight", "background", "position", "top", "left", "borderTopColor", "primary", "animation", "secondary", "variant", "mt", "backgroundClip", "textFillColor", "mb", "px", "py", "spacing", "elevation", "p", "right", "zIndex", "bottom", "mr", "component", "paragraph", "gap", "flexWrap", "label", "size", "icon", "onSubmit", "textAlign", "mx", "risk_assessment", "delay", "pb", "borderBottom", "accurate_data", "riskAssessment", "severity", "maxRiskScore", "flexGrow", "location", "toFixed", "maxRiskTime", "riskTrend", "fontStyle", "input_data", "onForecastGenerated"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/App.js"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport { Container, Box, Paper, Typography, Grid, Alert, Chip } from '@mui/material';\nimport Header from './components/Header';\nimport FloodMap from './components/FloodMap';\nimport PredictionForm from './components/PredictionForm';\nimport ResultDisplay from './components/ResultDisplay';\nimport RiskFactorsChart from './components/RiskFactorsChart';\nimport TimelineRiskPredictor from './components/TimelineRiskPredictor';\nimport VoiceEmergencyAssistant from './components/VoiceEmergencyAssistant';\nimport CommunityReports from './components/CommunityReports';\nimport { SlideUp, ScaleIn } from './components/animations/AnimatedComponents';\nimport LoadingAnimation from './components/animations/LoadingAnimation';\nimport axios from 'axios';\nimport { neuromorphicStyles, getNeuromorphicPalette, getNeuromorphicShadow, getPressedEffect } from './theme/neuromorphicUtils';\n\n// Create a theme with neuromorphic design\nconst theme = createTheme({\n  palette: {\n    mode: 'light',\n    ...getNeuromorphicPalette('#3A86FF'),\n    info: {\n      main: '#4CC9F0', // Light blue\n      light: '#e6f7fc',\n      dark: '#3AA1C0',\n      contrastText: '#ffffff',\n    },\n    divider: 'rgba(0, 0, 0, 0.08)',\n  },\n  typography: {\n    fontFamily: '\"Poppins\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontSize: 'clamp(2.5rem, 5vw, 3.5rem)',\n      fontWeight: 800,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.2,\n    },\n    h2: {\n      fontSize: 'clamp(2rem, 4vw, 2.75rem)',\n      fontWeight: 700,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.2,\n    },\n    h3: {\n      fontSize: 'clamp(1.5rem, 3vw, 2rem)',\n      fontWeight: 600,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.3,\n    },\n    h4: {\n      fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)',\n      fontWeight: 600,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.4,\n    },\n    h5: {\n      fontSize: 'clamp(1.1rem, 2vw, 1.5rem)',\n      fontWeight: 500,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.4,\n    },\n    h6: {\n      fontSize: 'clamp(1rem, 1.5vw, 1.25rem)',\n      fontWeight: 500,\n      letterSpacing: '-0.01em',\n      lineHeight: 1.5,\n    },\n    body1: {\n      fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',\n      lineHeight: 1.6,\n    },\n    body2: {\n      fontSize: 'clamp(0.8125rem, 1.25vw, 0.875rem)',\n      lineHeight: 1.6,\n    },\n    button: {\n      fontWeight: 600,\n      textTransform: 'none',\n      letterSpacing: '0.02em',\n      fontSize: 'clamp(0.875rem, 1.5vw, 1rem)',\n    },\n    subtitle1: {\n      fontSize: 'clamp(0.9375rem, 1.75vw, 1.125rem)',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n    subtitle2: {\n      fontSize: 'clamp(0.8125rem, 1.5vw, 0.9375rem)',\n      fontWeight: 500,\n      lineHeight: 1.5,\n    },\n  },\n  shape: {\n    borderRadius: 12,\n  },\n  shadows: [\n    'none',\n    '0px 2px 4px rgba(0, 0, 0, 0.03), 0px 1px 2px rgba(0, 0, 0, 0.06)',\n    '0px 4px 6px rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.06)',\n    '0px 6px 8px rgba(0, 0, 0, 0.04), 0px 3px 6px rgba(0, 0, 0, 0.06)',\n    '0px 8px 12px rgba(0, 0, 0, 0.05), 0px 4px 8px rgba(0, 0, 0, 0.06)',\n    '0px 10px 15px rgba(0, 0, 0, 0.05), 0px 6px 10px rgba(0, 0, 0, 0.06)',\n    '0px 12px 18px rgba(0, 0, 0, 0.05), 0px 7px 12px rgba(0, 0, 0, 0.06)',\n    '0px 14px 21px rgba(0, 0, 0, 0.05), 0px 8px 14px rgba(0, 0, 0, 0.06)',\n    '0px 16px 24px rgba(0, 0, 0, 0.05), 0px 9px 16px rgba(0, 0, 0, 0.06)',\n    '0px 18px 27px rgba(0, 0, 0, 0.05), 0px 10px 18px rgba(0, 0, 0, 0.06)',\n    '0px 20px 30px rgba(0, 0, 0, 0.05), 0px 11px 20px rgba(0, 0, 0, 0.06)',\n    '0px 22px 33px rgba(0, 0, 0, 0.05), 0px 12px 22px rgba(0, 0, 0, 0.06)',\n    '0px 24px 36px rgba(0, 0, 0, 0.05), 0px 13px 24px rgba(0, 0, 0, 0.06)',\n    '0px 26px 39px rgba(0, 0, 0, 0.05), 0px 14px 26px rgba(0, 0, 0, 0.06)',\n    '0px 28px 42px rgba(0, 0, 0, 0.05), 0px 15px 28px rgba(0, 0, 0, 0.06)',\n    '0px 30px 45px rgba(0, 0, 0, 0.05), 0px 16px 30px rgba(0, 0, 0, 0.06)',\n    '0px 32px 48px rgba(0, 0, 0, 0.05), 0px 17px 32px rgba(0, 0, 0, 0.06)',\n    '0px 34px 51px rgba(0, 0, 0, 0.05), 0px 18px 34px rgba(0, 0, 0, 0.06)',\n    '0px 36px 54px rgba(0, 0, 0, 0.05), 0px 19px 36px rgba(0, 0, 0, 0.06)',\n    '0px 38px 57px rgba(0, 0, 0, 0.05), 0px 20px 38px rgba(0, 0, 0, 0.06)',\n    '0px 40px 60px rgba(0, 0, 0, 0.05), 0px 21px 40px rgba(0, 0, 0, 0.06)',\n    '0px 42px 63px rgba(0, 0, 0, 0.05), 0px 22px 42px rgba(0, 0, 0, 0.06)',\n    '0px 44px 66px rgba(0, 0, 0, 0.05), 0px 23px 44px rgba(0, 0, 0, 0.06)',\n    '0px 46px 69px rgba(0, 0, 0, 0.05), 0px 24px 46px rgba(0, 0, 0, 0.06)',\n  ],\n  components: {\n    MuiContainer: {\n      styleOverrides: {\n        root: {\n          paddingLeft: {\n            xs: 16,\n            sm: 24,\n            md: 32,\n          },\n          paddingRight: {\n            xs: 16,\n            sm: 24,\n            md: 32,\n          },\n          maxWidth: {\n            xs: '100%',\n            sm: '100%',\n            md: '100%',\n            lg: '1280px',\n            xl: '1920px',\n          },\n        },\n      },\n    },\n    MuiGrid: {\n      styleOverrides: {\n        container: {\n          marginTop: {\n            xs: 2,\n            sm: 3,\n            md: 4,\n          },\n          marginBottom: {\n            xs: 2,\n            sm: 3,\n            md: 4,\n          },\n        },\n        item: {\n          paddingTop: {\n            xs: 1,\n            sm: 1.5,\n            md: 2,\n          },\n          paddingBottom: {\n            xs: 1,\n            sm: 1.5,\n            md: 2,\n          },\n        },\n      },\n    },\n    MuiPaper: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f0f4f8',\n          borderRadius: 16,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          transition: 'all 0.3s ease-in-out',\n          padding: {\n            xs: 2,\n            sm: 3,\n            md: 4,\n          },\n          border: 'none',\n          '&:hover': {\n            boxShadow: `\n              8px 8px 16px rgba(174, 174, 192, 0.35),\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\n            `,\n          },\n        },\n      },\n    },\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f0f4f8',\n          borderRadius: 12,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          transition: 'all 0.2s ease',\n          border: 'none',\n          padding: {\n            xs: '10px 20px',\n            sm: '12px 24px',\n            md: '14px 28px',\n          },\n          fontSize: {\n            xs: '0.875rem',\n            sm: '0.9375rem',\n            md: '1rem',\n          },\n          '&:hover': {\n            boxShadow: `\n              8px 8px 16px rgba(174, 174, 192, 0.35),\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\n            `,\n            backgroundColor: '#f0f4f8',\n          },\n          '&:active': {\n            boxShadow: `\n              inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n              inset -4px -4px 8px rgba(255, 255, 255, 0.5)\n            `,\n            backgroundColor: '#f0f4f8',\n          },\n        },\n        containedPrimary: {\n          color: '#ffffff',\n          backgroundColor: '#3A86FF',\n          boxShadow: `\n            6px 6px 12px rgba(58, 134, 255, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          '&:hover': {\n            backgroundColor: '#2a6bc9',\n          },\n          '&:active': {\n            boxShadow: `\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\n            `,\n          }\n        },\n        containedSecondary: {\n          color: '#ffffff',\n          backgroundColor: '#FF595E',\n          boxShadow: `\n            6px 6px 12px rgba(255, 89, 94, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          '&:hover': {\n            backgroundColor: '#d04649',\n          },\n          '&:active': {\n            boxShadow: `\n              inset 4px 4px 8px rgba(0, 0, 0, 0.2),\n              inset -4px -4px 8px rgba(255, 255, 255, 0.1)\n            `,\n          }\n        },\n        outlined: {\n          backgroundColor: 'transparent',\n          boxShadow: 'none',\n          border: '2px solid #f0f4f8',\n          '&:hover': {\n            boxShadow: `\n              4px 4px 8px rgba(174, 174, 192, 0.2),\n              -4px -4px 8px rgba(255, 255, 255, 0.3)\n            `,\n          },\n          '&:active': {\n            boxShadow: `\n              inset 2px 2px 4px rgba(174, 174, 192, 0.2),\n              inset -2px -2px 4px rgba(255, 255, 255, 0.3)\n            `,\n          },\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          transition: 'all 0.3s ease',\n          marginBottom: {\n            xs: 2,\n            sm: 2.5,\n            md: 3,\n          },\n          '& .MuiOutlinedInput-root': {\n            backgroundColor: '#f0f4f8',\n            borderRadius: 12,\n            boxShadow: `\n              inset 2px 2px 5px rgba(174, 174, 192, 0.2),\n              inset -2px -2px 5px rgba(255, 255, 255, 0.7)\n            `,\n            border: 'none',\n            '& fieldset': {\n              border: 'none',\n            },\n            '&:hover fieldset': {\n              border: 'none',\n            },\n            '&.Mui-focused': {\n              boxShadow: `\n                inset 4px 4px 8px rgba(174, 174, 192, 0.3),\n                inset -4px -4px 8px rgba(255, 255, 255, 0.5)\n              `,\n              '& fieldset': {\n                border: 'none',\n              },\n            },\n          },\n          '& .MuiInputLabel-root': {\n            fontSize: {\n              xs: '0.875rem',\n              sm: '0.9375rem',\n              md: '1rem',\n            },\n            color: '#1a202c',\n            fontWeight: 500,\n            '&.Mui-focused': {\n              color: '#3A86FF',\n              fontWeight: 600,\n            }\n          },\n          '& .MuiInputBase-input': {\n            padding: '16px 14px',\n            color: '#1a202c',\n            fontWeight: 500,\n          },\n        },\n      },\n    },\n    MuiSlider: {\n      styleOverrides: {\n        root: {\n          height: 10,\n          '& .MuiSlider-track': {\n            border: 'none',\n            boxShadow: 'inset 1px 1px 2px rgba(174, 174, 192, 0.3)',\n            backgroundColor: '#3A86FF',\n          },\n          '& .MuiSlider-rail': {\n            boxShadow: `\n              inset 2px 2px 4px rgba(174, 174, 192, 0.3),\n              inset -2px -2px 4px rgba(255, 255, 255, 0.5)\n            `,\n            backgroundColor: '#f0f4f8',\n            opacity: 1,\n          },\n          '& .MuiSlider-thumb': {\n            height: 24,\n            width: 24,\n            backgroundColor: '#f0f4f8',\n            boxShadow: `\n              6px 6px 12px rgba(174, 174, 192, 0.3),\n              -6px -6px 12px rgba(255, 255, 255, 0.5)\n            `,\n            '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {\n              boxShadow: `\n                8px 8px 16px rgba(174, 174, 192, 0.35),\n                -8px -8px 16px rgba(255, 255, 255, 0.6)\n              `,\n            },\n          },\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          backgroundColor: '#f0f4f8',\n          borderRadius: 16,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          transition: 'all 0.3s ease-in-out',\n          border: 'none',\n          overflow: 'hidden',\n          '&:hover': {\n            boxShadow: `\n              8px 8px 16px rgba(174, 174, 192, 0.35),\n              -8px -8px 16px rgba(255, 255, 255, 0.6)\n            `,\n          },\n        },\n      },\n    },\n    MuiCardContent: {\n      styleOverrides: {\n        root: {\n          padding: {\n            xs: '20px',\n            sm: '24px',\n            md: '28px',\n          },\n          '&:last-child': {\n            paddingBottom: {\n              xs: '20px',\n              sm: '24px',\n              md: '28px',\n            },\n          },\n        },\n      },\n    },\n    MuiAlert: {\n      styleOverrides: {\n        root: {\n          borderRadius: 16,\n          boxShadow: `\n            6px 6px 12px rgba(174, 174, 192, 0.3),\n            -6px -6px 12px rgba(255, 255, 255, 0.5)\n          `,\n          fontSize: {\n            xs: '0.875rem',\n            sm: '0.9375rem',\n            md: '1rem',\n          },\n          fontWeight: 500,\n        },\n        standardSuccess: {\n          backgroundColor: 'rgba(6, 214, 160, 0.1)',\n          color: '#05ab80',\n          '& .MuiAlert-icon': {\n            color: '#06d6a0',\n          },\n        },\n        standardError: {\n          backgroundColor: 'rgba(255, 89, 94, 0.1)',\n          color: '#d04649',\n          '& .MuiAlert-icon': {\n            color: '#ff595e',\n          },\n        },\n        standardWarning: {\n          backgroundColor: 'rgba(255, 159, 28, 0.1)',\n          color: '#d18016',\n          '& .MuiAlert-icon': {\n            color: '#ff9f1c',\n          },\n        },\n        standardInfo: {\n          backgroundColor: 'rgba(76, 201, 240, 0.1)',\n          color: '#3aa8cc',\n          '& .MuiAlert-icon': {\n            color: '#4cc9f0',\n          },\n        },\n      },\n    },\n    MuiChip: {\n      styleOverrides: {\n        root: {\n          borderRadius: 12,\n          backgroundColor: '#f0f4f8',\n          boxShadow: `\n            2px 2px 4px rgba(174, 174, 192, 0.3),\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\n          `,\n          fontSize: {\n            xs: '0.75rem',\n            sm: '0.8125rem',\n            md: '0.875rem',\n          },\n          color: '#1a202c',\n          fontWeight: 500,\n          '&:hover': {\n            boxShadow: `\n              3px 3px 6px rgba(174, 174, 192, 0.35),\n              -3px -3px 6px rgba(255, 255, 255, 0.6)\n            `,\n          },\n        },\n        colorPrimary: {\n          backgroundColor: '#3A86FF',\n          color: '#ffffff',\n        },\n        colorSecondary: {\n          backgroundColor: '#FF595E',\n          color: '#ffffff',\n        },\n        colorSuccess: {\n          backgroundColor: '#06d6a0',\n          color: '#ffffff',\n        },\n        colorError: {\n          backgroundColor: '#ff595e',\n          color: '#ffffff',\n        },\n        colorWarning: {\n          backgroundColor: '#ff9f1c',\n          color: '#ffffff',\n        },\n        colorInfo: {\n          backgroundColor: '#4cc9f0',\n          color: '#ffffff',\n        },\n      },\n    },\n    MuiTypography: {\n      styleOverrides: {\n        root: {\n          marginBottom: {\n            xs: 1,\n            sm: 1.5,\n            md: 2,\n          },\n          color: '#1a202c',\n        },\n        h1: {\n          color: '#1a202c',\n          fontWeight: 800,\n        },\n        h2: {\n          color: '#1a202c',\n          fontWeight: 700,\n        },\n        h3: {\n          color: '#1a202c',\n          fontWeight: 700,\n        },\n        h4: {\n          color: '#1a202c',\n          fontWeight: 600,\n        },\n        h5: {\n          color: '#1a202c',\n          fontWeight: 600,\n        },\n        h6: {\n          color: '#1a202c',\n          fontWeight: 600,\n        },\n        subtitle1: {\n          color: '#2d3748',\n          fontWeight: 500,\n        },\n        subtitle2: {\n          color: '#2d3748',\n          fontWeight: 500,\n        },\n        body1: {\n          color: '#2d3748',\n        },\n        body2: {\n          color: '#4a5568',\n        },\n      },\n    },\n    MuiSwitch: {\n      styleOverrides: {\n        root: {\n          width: 56,\n          height: 32,\n          padding: 0,\n        },\n        switchBase: {\n          padding: 4,\n          '&.Mui-checked': {\n            transform: 'translateX(24px)',\n            '& + .MuiSwitch-track': {\n              opacity: 1,\n              backgroundColor: '#e6eef8',\n            },\n          },\n        },\n        thumb: {\n          width: 24,\n          height: 24,\n          backgroundColor: '#f0f4f8',\n          boxShadow: `\n            2px 2px 4px rgba(174, 174, 192, 0.3),\n            -2px -2px 4px rgba(255, 255, 255, 0.5)\n          `,\n        },\n        track: {\n          opacity: 1,\n          borderRadius: 16,\n          backgroundColor: '#e6eef8',\n          boxShadow: `\n            inset 2px 2px 4px rgba(174, 174, 192, 0.3),\n            inset -2px -2px 4px rgba(255, 255, 255, 0.5)\n          `,\n        },\n      },\n    },\n  },\n});\n\nfunction App() {\n  const [mapData, setMapData] = useState([]);\n  const [options, setOptions] = useState({ land_cover: [], soil_type: [] });\n  const [prediction, setPrediction] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [initialLoading, setInitialLoading] = useState(true);\n  const [showPredictionResult, setShowPredictionResult] = useState(false);\n  const [forecastSummary, setForecastSummary] = useState(null);\n  const [showForecastAlert, setShowForecastAlert] = useState(false);\n\n  useEffect(() => {\n    // Fetch map data and options when component mounts\n    const fetchData = async () => {\n      setInitialLoading(true);\n      let loadingTimer;\n\n      try {\n        const [mapResponse, optionsResponse] = await Promise.all([\n          axios.get('/api/map-data'),\n          axios.get('/api/options')\n        ]);\n        setMapData(mapResponse.data);\n        setOptions(optionsResponse.data);\n      } catch (error) {\n        console.error('Error fetching data:', error);\n      } finally {\n        // Add a slight delay to make the loading animation visible\n        // but ensure it gets cleared if component unmounts\n        loadingTimer = setTimeout(() => {\n          setInitialLoading(false);\n        }, 1500);\n      }\n\n      // Cleanup function to ensure loading state is reset if component unmounts\n      return () => {\n        if (loadingTimer) clearTimeout(loadingTimer);\n        setInitialLoading(false);\n      };\n    };\n\n    fetchData();\n  }, []);\n\n  const handleSubmit = async (formData) => {\n    setLoading(true);\n    setShowPredictionResult(false);\n    setShowForecastAlert(false);\n\n    try {\n      // Add a slight delay to make the loading animation visible\n      await new Promise(resolve => setTimeout(resolve, 1200));\n\n      const response = await axios.post('/api/predict', formData);\n      setPrediction(response.data);\n\n      // Add a slight delay before showing the result for better animation\n      setTimeout(() => {\n        setShowPredictionResult(true);\n      }, 300);\n    } catch (error) {\n      console.error('Error making prediction:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleForecastGenerated = (summary) => {\n    setForecastSummary(summary);\n    setShowForecastAlert(true);\n\n    // Hide the alert after 10 seconds\n    setTimeout(() => {\n      setShowForecastAlert(false);\n    }, 10000);\n  };\n\n  return (\n    <ThemeProvider theme={theme}>\n      <CssBaseline />\n      <Header />\n      <VoiceEmergencyAssistant />\n\n      {initialLoading ? (\n        <Box\n          sx={{\n            display: 'flex',\n            flexDirection: 'column',\n            alignItems: 'center',\n            justifyContent: 'center',\n            minHeight: '80vh',\n            background: '#e6eef8'\n          }}\n        >\n          <Box sx={{ position: 'relative', width: 200, height: 200 }}>\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                width: '100%',\n                height: '100%',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.primary.main,\n                animation: 'spin 1.5s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 15,\n                left: 15,\n                width: 'calc(100% - 30px)',\n                height: 'calc(100% - 30px)',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.secondary.main,\n                animation: 'spin 2s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 30,\n                left: 30,\n                width: 'calc(100% - 60px)',\n                height: 'calc(100% - 60px)',\n                borderRadius: '50%',\n                border: '4px solid transparent',\n                borderTopColor: theme.palette.info.main,\n                animation: 'spin 2.5s linear infinite',\n                '@keyframes spin': {\n                  '0%': { transform: 'rotate(0deg)' },\n                  '100%': { transform: 'rotate(360deg)' }\n                }\n              }}\n            />\n          </Box>\n          <Typography\n            variant=\"h4\"\n            sx={{\n              mt: 4,\n              fontWeight: 600,\n              background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',\n              backgroundClip: 'text',\n              textFillColor: 'transparent',\n              animation: 'pulse 2s infinite',\n              '@keyframes pulse': {\n                '0%': { opacity: 0.6 },\n                '50%': { opacity: 1 },\n                '100%': { opacity: 0.6 }\n              }\n            }}\n          >\n            Loading Flood Prediction System...\n          </Typography>\n        </Box>\n      ) : (\n        <Container\n          maxWidth=\"xl\"\n          sx={{\n            mt: { xs: 2, sm: 3, md: 4 },\n            mb: { xs: 4, sm: 6, md: 8 },\n            px: { xs: 2, sm: 3, md: 4 },\n            py: { xs: 3, sm: 4, md: 5 },\n            overflow: 'hidden',\n            backgroundColor: '#e6eef8',\n            borderRadius: '24px',\n            boxShadow: `\n              inset 1px 1px 2px rgba(255, 255, 255, 0.5),\n              inset -1px -1px 2px rgba(174, 174, 192, 0.3)\n            `\n          }}\n        >\n          <Grid\n            container\n            spacing={{ xs: 3, sm: 4, md: 5 }}\n            sx={{\n              '& .MuiGrid-item': {\n                display: 'flex',\n                flexDirection: 'column'\n              }\n            }}\n          >\n            <Grid item xs={12}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, md: 4 },\n                  mb: 3,\n                  backgroundColor: '#f0f4f8',\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 2,\n                  transition: 'all 0.3s ease-in-out',\n                  boxShadow: `\n                    6px 6px 12px rgba(174, 174, 192, 0.3),\n                    -6px -6px 12px rgba(255, 255, 255, 0.5)\n                  `,\n                  '&:hover': {\n                    boxShadow: `\n                      8px 8px 16px rgba(174, 174, 192, 0.35),\n                      -8px -8px 16px rgba(255, 255, 255, 0.6)\n                    `\n                  }\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '180px',\n                    height: '180px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 0 100%',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    width: '120px',\n                    height: '120px',\n                    background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 100% 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n                    <Box\n                      sx={{\n                        mr: 2,\n                        p: 1.5,\n                        borderRadius: '50%',\n                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center',\n                        boxShadow: '0 4px 12px rgba(76, 201, 240, 0.15)'\n                      }}\n                    >\n                      <Typography variant=\"h4\" component=\"span\">🌊</Typography>\n                    </Box>\n                    <Typography\n                      variant=\"h2\"\n                      component=\"h1\"\n                      sx={{\n                        background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',\n                        backgroundClip: 'text',\n                        textFillColor: 'transparent',\n                        fontWeight: 800,\n                        letterSpacing: '-0.5px'\n                      }}\n                    >\n                      Flood Risk Prediction System\n                    </Typography>\n                  </Box>\n                  <Typography\n                    variant=\"body1\"\n                    paragraph\n                    sx={{\n                      fontSize: '1.1rem',\n                      maxWidth: '90%',\n                      color: 'text.secondary',\n                      lineHeight: 1.6,\n                      mb: 2\n                    }}\n                  >\n                    This interactive tool helps predict flood risk based on various environmental and geographical factors.\n                    For Indian cities, we provide accurate predictions using historical flood data and real-time weather conditions.\n                  </Typography>\n                  <Box\n                    sx={{\n                      display: 'flex',\n                      gap: 2,\n                      flexWrap: 'wrap',\n                      mt: 3\n                    }}\n                  >\n                    <Chip\n                      label=\"Real-time Weather Data\"\n                      color=\"primary\"\n                      size=\"medium\"\n                      icon={<span>⛈️</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                    <Chip\n                      label=\"Historical Flood Analysis\"\n                      color=\"info\"\n                      size=\"medium\"\n                      icon={<span>📊</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                    <Chip\n                      label=\"Indian Cities Database\"\n                      color=\"success\"\n                      size=\"medium\"\n                      icon={<span>🏙️</span>}\n                      sx={{ fontWeight: 500, px: 1 }}\n                    />\n                  </Box>\n                </Box>\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} lg={6}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, md: 4 },\n                  height: '100%',\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 2,\n                  transition: 'all 0.3s ease-in-out',\n                  '&:hover': {\n                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                  }\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    width: '100px',\n                    height: '100px',\n                    background: 'radial-gradient(circle, rgba(58,134,255,0.08) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 100% 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    right: 0,\n                    width: '80px',\n                    height: '80px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '100% 0 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                    <Box\n                      sx={{\n                        mr: 2,\n                        p: 1,\n                        borderRadius: '12px',\n                        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'center'\n                      }}\n                    >\n                      <Typography variant=\"h5\" component=\"span\">📝</Typography>\n                    </Box>\n                    <Typography\n                      variant=\"h4\"\n                      sx={{\n                        fontWeight: 700,\n                        color: theme.palette.primary.main,\n                        letterSpacing: '-0.5px'\n                      }}\n                    >\n                      Predict Flood Risk\n                    </Typography>\n                  </Box>\n                  <Typography\n                    variant=\"body1\"\n                    sx={{\n                      mb: 3,\n                      color: 'text.secondary',\n                      maxWidth: '95%'\n                    }}\n                  >\n                    Enter location and environmental factors to get a precise flood risk assessment.\n                    For Indian cities, we provide enhanced accuracy using historical data.\n                  </Typography>\n                  <PredictionForm\n                    options={options}\n                    onSubmit={handleSubmit}\n                    loading={loading}\n                  />\n                </Box>\n              </Paper>\n            </Grid>\n\n            <Grid item xs={12} lg={6}>\n              <Paper\n                elevation={3}\n                sx={{\n                  p: { xs: 3, md: 4 },\n                  height: '100%',\n                  position: 'relative',\n                  overflow: 'hidden',\n                  borderRadius: 2,\n                  transition: 'all 0.3s ease-in-out',\n                  '&:hover': {\n                    boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                  }\n                }}\n              >\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    top: 0,\n                    right: 0,\n                    width: '100px',\n                    height: '100px',\n                    background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 0 0 100%',\n                    zIndex: 0\n                  }}\n                />\n                <Box\n                  sx={{\n                    position: 'absolute',\n                    bottom: 0,\n                    left: 0,\n                    width: '80px',\n                    height: '80px',\n                    background: 'radial-gradient(circle, rgba(255,89,94,0.05) 0%, rgba(0,0,0,0) 70%)',\n                    borderRadius: '0 100% 0 0',\n                    zIndex: 0\n                  }}\n                />\n                <Box sx={{ position: 'relative', zIndex: 1 }}>\n                  {loading ? (\n                    <LoadingAnimation theme={theme} />\n                  ) : prediction && showPredictionResult ? (\n                    <Box sx={{\n                      opacity: showPredictionResult ? 1 : 0,\n                      transform: showPredictionResult ? 'translateY(0)' : 'translateY(20px)',\n                      transition: 'all 0.5s ease-in-out'\n                    }}>\n                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                        <Box\n                          sx={{\n                            mr: 2,\n                            p: 1,\n                            borderRadius: '12px',\n                            background: 'linear-gradient(135deg, rgba(76, 201, 240, 0.1) 0%, rgba(58, 134, 255, 0.1) 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          }}\n                        >\n                          <Typography variant=\"h5\" component=\"span\">📊</Typography>\n                        </Box>\n                        <Typography\n                          variant=\"h4\"\n                          sx={{\n                            fontWeight: 700,\n                            color: theme.palette.primary.main,\n                            letterSpacing: '-0.5px'\n                          }}\n                        >\n                          Risk Assessment\n                        </Typography>\n                      </Box>\n                      <ResultDisplay prediction={prediction} />\n                    </Box>\n                  ) : (\n                    <Box sx={{\n                      display: 'flex',\n                      flexDirection: 'column',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      py: 8,\n                      opacity: loading ? 0 : 1,\n                      transition: 'opacity 0.3s ease-in-out'\n                    }}>\n                      <Box\n                        sx={{\n                          width: 100,\n                          height: 100,\n                          borderRadius: '50%',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                          boxShadow: '0 8px 32px rgba(76, 201, 240, 0.12)',\n                          mb: 3,\n                          animation: 'float 3s ease-in-out infinite',\n                          '@keyframes float': {\n                            '0%': { transform: 'translateY(0px)' },\n                            '50%': { transform: 'translateY(-10px)' },\n                            '100%': { transform: 'translateY(0px)' }\n                          }\n                        }}\n                      >\n                        <Typography variant=\"h2\" component=\"span\">📊</Typography>\n                      </Box>\n                      <Typography variant=\"h5\" sx={{ fontWeight: 600, color: theme.palette.primary.main, textAlign: 'center', mb: 1 }}>\n                        Results will appear here\n                      </Typography>\n                      <Typography variant=\"body1\" color=\"text.secondary\" sx={{ textAlign: 'center', maxWidth: '80%', mx: 'auto' }}>\n                        Fill out the form on the left to generate a detailed flood risk assessment\n                      </Typography>\n                    </Box>\n                  )}\n                </Box>\n              </Paper>\n            </Grid>\n\n            {prediction && prediction.risk_assessment && showPredictionResult && (\n              <SlideUp delay={200}>\n                <Grid item xs={12}>\n                  <Paper\n                    elevation={4}\n                    sx={{\n                      p: { xs: 4, sm: 5, md: 6 },\n                      background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                      borderRadius: 4,\n                      position: 'relative',\n                      overflow: 'hidden',\n                      transition: 'all 0.3s ease-in-out',\n                      border: '1px solid rgba(58, 134, 255, 0.08)',\n                      '&:hover': {\n                        boxShadow: '0 12px 32px rgba(58, 134, 255, 0.15)',\n                        transform: 'translateY(-2px)'\n                      }\n                    }}\n                  >\n                    {/* Enhanced decorative elements */}\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        top: -30,\n                        right: -30,\n                        width: '200px',\n                        height: '200px',\n                        background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '50%',\n                        zIndex: 0\n                      }}\n                    />\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        bottom: -40,\n                        left: -40,\n                        width: '160px',\n                        height: '160px',\n                        background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '50%',\n                        zIndex: 0\n                      }}\n                    />\n                    <Box\n                      sx={{\n                        position: 'absolute',\n                        top: '50%',\n                        right: '10%',\n                        width: '80px',\n                        height: '80px',\n                        background: 'radial-gradient(circle, rgba(255,159,28,0.06) 0%, rgba(0,0,0,0) 70%)',\n                        borderRadius: '50%',\n                        zIndex: 0\n                      }}\n                    />\n\n                    <Box sx={{ position: 'relative', zIndex: 1 }}>\n                      <Box sx={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        mb: { xs: 4, sm: 5 },\n                        pb: 2,\n                        borderBottom: '2px solid rgba(58, 134, 255, 0.1)'\n                      }}>\n                        <Box\n                          sx={{\n                            mr: { xs: 2, sm: 3 },\n                            p: { xs: 1.5, sm: 2 },\n                            borderRadius: 3,\n                            background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.15) 0%, rgba(255, 159, 28, 0.15) 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            boxShadow: '0 4px 12px rgba(255, 89, 94, 0.2)'\n                          }}\n                        >\n                          <Typography variant=\"h4\" component=\"span\">📈</Typography>\n                        </Box>\n                        <Box>\n                          <Typography\n                            variant=\"h3\"\n                            sx={{\n                              fontWeight: 800,\n                              color: theme.palette.primary.dark,\n                              letterSpacing: '-0.8px',\n                              mb: 0.5\n                            }}\n                          >\n                            Risk Factor Analysis\n                          </Typography>\n                          <Typography\n                            variant=\"subtitle1\"\n                            sx={{\n                              color: 'text.secondary',\n                              fontWeight: 500\n                            }}\n                          >\n                            Detailed Environmental Impact Assessment\n                          </Typography>\n                        </Box>\n                      </Box>\n\n                      <Box sx={{ mb: { xs: 4, sm: 5 } }}>\n                        <Typography\n                          variant=\"body1\"\n                          sx={{\n                            fontSize: { xs: '1rem', sm: '1.1rem' },\n                            lineHeight: 1.7,\n                            color: 'text.secondary',\n                            maxWidth: '900px'\n                          }}\n                        >\n                          This comprehensive analysis shows how different environmental and geographical factors contribute to the overall flood risk assessment.\n                          {prediction.accurate_data && \" For Indian cities, this includes historical flood data and real-time weather conditions from our enhanced database.\"}\n                        </Typography>\n                      </Box>\n\n                      <Box sx={{\n                        p: { xs: 2, sm: 3 },\n                        borderRadius: 3,\n                        background: 'rgba(58, 134, 255, 0.02)',\n                        border: '1px solid rgba(58, 134, 255, 0.1)'\n                      }}>\n                        <RiskFactorsChart riskAssessment={prediction.risk_assessment} />\n                      </Box>\n                    </Box>\n                  </Paper>\n                </Grid>\n              </SlideUp>\n            )}\n\n            {/* Forecast Alert */}\n            {showForecastAlert && forecastSummary && (\n              <ScaleIn delay={100}>\n                <Grid item xs={12}>\n                  <Alert\n                    severity={forecastSummary.maxRiskScore > 70 ? \"error\" : forecastSummary.maxRiskScore > 40 ? \"warning\" : \"info\"}\n                    variant=\"filled\"\n                    sx={{\n                      mb: 3,\n                      borderRadius: 2,\n                      boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n                      '& .MuiAlert-icon': { fontSize: '1.8rem' },\n                      p: 2,\n                      animation: 'pulse 2s infinite',\n                      '@keyframes pulse': {\n                        '0%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' },\n                        '50%': { boxShadow: '0 8px 36px rgba(0, 0, 0, 0.25)' },\n                        '100%': { boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)' }\n                      }\n                    }}\n                  >\n                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>\n                      <Box sx={{ flexGrow: 1 }}>\n                        <Typography variant=\"h6\" sx={{ fontWeight: 'bold', mb: 0.5 }}>\n                          {forecastSummary.maxRiskScore > 70\n                            ? \"⚠️ High flood risk detected in the forecast!\"\n                            : forecastSummary.maxRiskScore > 40\n                              ? \"⚠️ Medium flood risk detected in the forecast\"\n                              : \"ℹ️ Flood risk forecast generated\"}\n                        </Typography>\n                        <Typography variant=\"body1\" sx={{ fontWeight: 500 }}>\n                          Location: <strong>{forecastSummary.location || 'Selected area'}</strong>\n                        </Typography>\n                        <Typography variant=\"body1\">\n                          Peak risk score of <strong>{forecastSummary.maxRiskScore.toFixed(1)}</strong> expected around <strong>{forecastSummary.maxRiskTime}</strong>.\n                          Risk trend is <strong>{forecastSummary.riskTrend}</strong>.\n                        </Typography>\n                        {forecastSummary.maxRiskScore > 60 && (\n                          <Typography variant=\"body2\" sx={{ mt: 1, fontStyle: 'italic' }}>\n                            Please monitor local weather updates and follow emergency guidelines.\n                          </Typography>\n                        )}\n                      </Box>\n                    </Box>\n                  </Alert>\n                </Grid>\n              </ScaleIn>\n            )}\n\n            {/* Temporal Prediction Component */}\n            {prediction && showPredictionResult && (\n              <SlideUp delay={300}>\n                <Grid item xs={12}>\n                  <Box sx={{\n                    p: { xs: 1, sm: 2 },\n                    borderRadius: 4,\n                    background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.02) 0%, rgba(76, 201, 240, 0.02) 100%)',\n                    border: '1px solid rgba(58, 134, 255, 0.08)'\n                  }}>\n                    <Paper\n                      elevation={4}\n                      sx={{\n                        borderRadius: 4,\n                        overflow: 'hidden',\n                        transition: 'all 0.3s ease-in-out',\n                        background: 'linear-gradient(135deg, #ffffff 0%, #f8faff 100%)',\n                        '&:hover': {\n                          boxShadow: '0 12px 32px rgba(58, 134, 255, 0.15)',\n                          transform: 'translateY(-2px)'\n                        }\n                      }}\n                    >\n                      <TimelineRiskPredictor\n                        formData={prediction.input_data}\n                        onForecastGenerated={handleForecastGenerated}\n                      />\n                    </Paper>\n                  </Box>\n                </Grid>\n              </SlideUp>\n            )}\n\n            <SlideUp delay={200}>\n              <Grid item xs={12}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: { xs: 3, md: 4 },\n                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                    borderRadius: 2,\n                    position: 'relative',\n                    overflow: 'hidden',\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                    }\n                  }}\n                >\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      right: 0,\n                      width: '150px',\n                      height: '150px',\n                      background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',\n                      borderRadius: '0 0 0 100%',\n                      zIndex: 0\n                    }}\n                  />\n                  <Box sx={{ position: 'relative', zIndex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                      <Box\n                        sx={{\n                          mr: 2,\n                          p: 1,\n                          borderRadius: '12px',\n                          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.1) 0%, rgba(76, 201, 240, 0.1) 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        }}\n                      >\n                        <Typography variant=\"h5\" component=\"span\">🗺️</Typography>\n                      </Box>\n                      <Typography\n                        variant=\"h4\"\n                        sx={{\n                          fontWeight: 700,\n                          color: theme.palette.primary.dark,\n                          letterSpacing: '-0.5px'\n                        }}\n                      >\n                        Flood Risk Map\n                      </Typography>\n                    </Box>\n                    <Typography\n                      variant=\"body1\"\n                      paragraph\n                      sx={{\n                        mb: 3,\n                        color: 'text.secondary',\n                        maxWidth: '800px'\n                      }}\n                    >\n                      This interactive map shows areas with predicted flood risk based on our analysis.\n                      Green markers indicate low risk areas, while red markers indicate high risk zones.\n                      Click on markers to see detailed information.\n                    </Typography>\n                    <Box sx={{\n                      borderRadius: 2,\n                      overflow: 'hidden',\n                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                      height: '500px'\n                    }}>\n                      <FloodMap mapData={mapData} />\n                    </Box>\n                  </Box>\n                </Paper>\n              </Grid>\n            </SlideUp>\n\n            {/* Community Reports Section */}\n            <SlideUp delay={300}>\n              <Grid item xs={12}>\n                <Paper\n                  elevation={3}\n                  sx={{\n                    p: { xs: 3, md: 4 },\n                    background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fe 100%)',\n                    borderRadius: 2,\n                    position: 'relative',\n                    overflow: 'hidden',\n                    transition: 'all 0.3s ease-in-out',\n                    '&:hover': {\n                      boxShadow: '0 8px 24px rgba(58, 134, 255, 0.12)'\n                    }\n                  }}\n                >\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      right: 0,\n                      width: '150px',\n                      height: '150px',\n                      background: 'radial-gradient(circle, rgba(255,89,94,0.08) 0%, rgba(0,0,0,0) 70%)',\n                      borderRadius: '0 0 0 100%',\n                      zIndex: 0\n                    }}\n                  />\n                  <Box sx={{ position: 'relative', zIndex: 1 }}>\n                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>\n                      <Box\n                        sx={{\n                          mr: 2,\n                          p: 1,\n                          borderRadius: '12px',\n                          background: 'linear-gradient(135deg, rgba(255, 89, 94, 0.1) 0%, rgba(255, 159, 28, 0.1) 100%)',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        }}\n                      >\n                        <Typography variant=\"h5\" component=\"span\">👥</Typography>\n                      </Box>\n                      <Typography\n                        variant=\"h4\"\n                        sx={{\n                          fontWeight: 700,\n                          color: theme.palette.primary.dark,\n                          letterSpacing: '-0.5px'\n                        }}\n                      >\n                        Community Reports\n                      </Typography>\n                    </Box>\n                    <Typography\n                      variant=\"body1\"\n                      paragraph\n                      sx={{\n                        mb: 3,\n                        color: 'text.secondary',\n                        maxWidth: '800px'\n                      }}\n                    >\n                      View and submit real-time flood reports from community members. These reports help validate our predictions\n                      and provide valuable on-the-ground information during flood events.\n                    </Typography>\n                    <CommunityReports />\n                  </Box>\n                </Paper>\n              </Grid>\n            </SlideUp>\n          </Grid>\n        </Container>\n      )}\n    </ThemeProvider>\n  );\n}\n\nexport default App;\n"], "mappings": "kIAAA,OAASA,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAC3C,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CACnD,OAASC,SAAS,CAAEC,GAAG,CAAEC,KAAK,CAAEC,UAAU,CAAEC,IAAI,CAAEC,KAAK,CAAEC,IAAI,KAAQ,eAAe,CACpF,MAAO,CAAAC,MAAM,KAAM,qBAAqB,CACxC,MAAO,CAAAC,QAAQ,KAAM,uBAAuB,CAC5C,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,aAAa,KAAM,4BAA4B,CACtD,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,MAAO,CAAAC,qBAAqB,KAAM,oCAAoC,CACtE,MAAO,CAAAC,uBAAuB,KAAM,sCAAsC,CAC1E,MAAO,CAAAC,gBAAgB,KAAM,+BAA+B,CAC5D,OAASC,OAAO,CAAEC,OAAO,KAAQ,4CAA4C,CAC7E,MAAO,CAAAC,gBAAgB,KAAM,0CAA0C,CACvE,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,kBAAkB,CAAEC,sBAAsB,CAAEC,qBAAqB,CAAEC,gBAAgB,KAAQ,2BAA2B,CAE/H;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAG7B,WAAW,CAAC,CACxB8B,OAAO,CAAAC,aAAA,CAAAA,aAAA,EACLC,IAAI,CAAE,OAAO,EACVV,sBAAsB,CAAC,SAAS,CAAC,MACpCW,IAAI,CAAE,CACJC,IAAI,CAAE,SAAS,CAAE;AACjBC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SAAS,CACfC,YAAY,CAAE,SAChB,CAAC,CACDC,OAAO,CAAE,qBAAqB,EAC/B,CACDC,UAAU,CAAE,CACVC,UAAU,CAAE,uDAAuD,CACnEC,EAAE,CAAE,CACFC,QAAQ,CAAE,4BAA4B,CACtCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDC,EAAE,CAAE,CACFJ,QAAQ,CAAE,2BAA2B,CACrCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDE,EAAE,CAAE,CACFL,QAAQ,CAAE,0BAA0B,CACpCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDG,EAAE,CAAE,CACFN,QAAQ,CAAE,gCAAgC,CAC1CC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDI,EAAE,CAAE,CACFP,QAAQ,CAAE,4BAA4B,CACtCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDK,EAAE,CAAE,CACFR,QAAQ,CAAE,6BAA6B,CACvCC,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,SAAS,CACxBC,UAAU,CAAE,GACd,CAAC,CACDM,KAAK,CAAE,CACLT,QAAQ,CAAE,8BAA8B,CACxCG,UAAU,CAAE,GACd,CAAC,CACDO,KAAK,CAAE,CACLV,QAAQ,CAAE,oCAAoC,CAC9CG,UAAU,CAAE,GACd,CAAC,CACDQ,MAAM,CAAE,CACNV,UAAU,CAAE,GAAG,CACfW,aAAa,CAAE,MAAM,CACrBV,aAAa,CAAE,QAAQ,CACvBF,QAAQ,CAAE,8BACZ,CAAC,CACDa,SAAS,CAAE,CACTb,QAAQ,CAAE,oCAAoC,CAC9CC,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,GACd,CAAC,CACDW,SAAS,CAAE,CACTd,QAAQ,CAAE,oCAAoC,CAC9CC,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,GACd,CACF,CAAC,CACDY,KAAK,CAAE,CACLC,YAAY,CAAE,EAChB,CAAC,CACDC,OAAO,CAAE,CACP,MAAM,CACN,kEAAkE,CAClE,kEAAkE,CAClE,kEAAkE,CAClE,mEAAmE,CACnE,qEAAqE,CACrE,qEAAqE,CACrE,qEAAqE,CACrE,qEAAqE,CACrE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACtE,sEAAsE,CACvE,CACDC,UAAU,CAAE,CACVC,YAAY,CAAE,CACZC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,WAAW,CAAE,CACXC,EAAE,CAAE,EAAE,CACNC,EAAE,CAAE,EAAE,CACNC,EAAE,CAAE,EACN,CAAC,CACDC,YAAY,CAAE,CACZH,EAAE,CAAE,EAAE,CACNC,EAAE,CAAE,EAAE,CACNC,EAAE,CAAE,EACN,CAAC,CACDE,QAAQ,CAAE,CACRJ,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MAAM,CACVG,EAAE,CAAE,QAAQ,CACZC,EAAE,CAAE,QACN,CACF,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPV,cAAc,CAAE,CACdW,SAAS,CAAE,CACTC,SAAS,CAAE,CACTT,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAC,CACDQ,YAAY,CAAE,CACZV,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CACF,CAAC,CACDS,IAAI,CAAE,CACJC,UAAU,CAAE,CACVZ,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CACN,CAAC,CACDW,aAAa,CAAE,CACbb,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CACN,CACF,CACF,CACF,CAAC,CACDY,QAAQ,CAAE,CACRjB,cAAc,CAAE,CACdC,IAAI,CAAE,CACJiB,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,EAAE,CAChBuB,SAAS,wHAGR,CACDC,UAAU,CAAE,sBAAsB,CAClCC,OAAO,CAAE,CACPlB,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAC,CACDiB,MAAM,CAAE,MAAM,CACd,SAAS,CAAE,CACTH,SAAS,+HAIX,CACF,CACF,CACF,CAAC,CACDI,SAAS,CAAE,CACTvB,cAAc,CAAE,CACdC,IAAI,CAAE,CACJiB,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,EAAE,CAChBuB,SAAS,wHAGR,CACDC,UAAU,CAAE,eAAe,CAC3BE,MAAM,CAAE,MAAM,CACdD,OAAO,CAAE,CACPlB,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,WACN,CAAC,CACDzB,QAAQ,CAAE,CACRuB,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,MACN,CAAC,CACD,SAAS,CAAE,CACTc,SAAS,+HAGR,CACDD,eAAe,CAAE,SACnB,CAAC,CACD,UAAU,CAAE,CACVC,SAAS,wIAGR,CACDD,eAAe,CAAE,SACnB,CACF,CAAC,CACDM,gBAAgB,CAAE,CAChBC,KAAK,CAAE,SAAS,CAChBP,eAAe,CAAE,SAAS,CAC1BC,SAAS,uHAGR,CACD,SAAS,CAAE,CACTD,eAAe,CAAE,SACnB,CAAC,CACD,UAAU,CAAE,CACVC,SAAS,kIAIX,CACF,CAAC,CACDO,kBAAkB,CAAE,CAClBD,KAAK,CAAE,SAAS,CAChBP,eAAe,CAAE,SAAS,CAC1BC,SAAS,sHAGR,CACD,SAAS,CAAE,CACTD,eAAe,CAAE,SACnB,CAAC,CACD,UAAU,CAAE,CACVC,SAAS,kIAIX,CACF,CAAC,CACDQ,QAAQ,CAAE,CACRT,eAAe,CAAE,aAAa,CAC9BC,SAAS,CAAE,MAAM,CACjBG,MAAM,CAAE,mBAAmB,CAC3B,SAAS,CAAE,CACTH,SAAS,4HAIX,CAAC,CACD,UAAU,CAAE,CACVA,SAAS,wIAIX,CACF,CACF,CACF,CAAC,CACDS,YAAY,CAAE,CACZ5B,cAAc,CAAE,CACdC,IAAI,CAAE,CACJmB,UAAU,CAAE,eAAe,CAC3BP,YAAY,CAAE,CACZV,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CACN,CAAC,CACD,0BAA0B,CAAE,CAC1Ba,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,EAAE,CAChBuB,SAAS,wIAGR,CACDG,MAAM,CAAE,MAAM,CACd,YAAY,CAAE,CACZA,MAAM,CAAE,MACV,CAAC,CACD,kBAAkB,CAAE,CAClBA,MAAM,CAAE,MACV,CAAC,CACD,eAAe,CAAE,CACfH,SAAS,8IAGR,CACD,YAAY,CAAE,CACZG,MAAM,CAAE,MACV,CACF,CACF,CAAC,CACD,uBAAuB,CAAE,CACvB1C,QAAQ,CAAE,CACRuB,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,MACN,CAAC,CACDoB,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GAAG,CACf,eAAe,CAAE,CACf4C,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CACF,CAAC,CACD,uBAAuB,CAAE,CACvBwC,OAAO,CAAE,WAAW,CACpBI,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CACF,CACF,CACF,CAAC,CACDgD,SAAS,CAAE,CACT7B,cAAc,CAAE,CACdC,IAAI,CAAE,CACJ6B,MAAM,CAAE,EAAE,CACV,oBAAoB,CAAE,CACpBR,MAAM,CAAE,MAAM,CACdH,SAAS,CAAE,4CAA4C,CACvDD,eAAe,CAAE,SACnB,CAAC,CACD,mBAAmB,CAAE,CACnBC,SAAS,wIAGR,CACDD,eAAe,CAAE,SAAS,CAC1Ba,OAAO,CAAE,CACX,CAAC,CACD,oBAAoB,CAAE,CACpBD,MAAM,CAAE,EAAE,CACVE,KAAK,CAAE,EAAE,CACTd,eAAe,CAAE,SAAS,CAC1BC,SAAS,8HAGR,CACD,oDAAoD,CAAE,CACpDA,SAAS,qIAIX,CACF,CACF,CACF,CACF,CAAC,CACDc,OAAO,CAAE,CACPjC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJiB,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,EAAE,CAChBuB,SAAS,wHAGR,CACDC,UAAU,CAAE,sBAAsB,CAClCE,MAAM,CAAE,MAAM,CACdY,QAAQ,CAAE,QAAQ,CAClB,SAAS,CAAE,CACTf,SAAS,+HAIX,CACF,CACF,CACF,CAAC,CACDgB,cAAc,CAAE,CACdnC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJoB,OAAO,CAAE,CACPlB,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MACN,CAAC,CACD,cAAc,CAAE,CACdW,aAAa,CAAE,CACbb,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MAAM,CACVC,EAAE,CAAE,MACN,CACF,CACF,CACF,CACF,CAAC,CACD+B,QAAQ,CAAE,CACRpC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJL,YAAY,CAAE,EAAE,CAChBuB,SAAS,wHAGR,CACDvC,QAAQ,CAAE,CACRuB,EAAE,CAAE,UAAU,CACdC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,MACN,CAAC,CACDxB,UAAU,CAAE,GACd,CAAC,CACDwD,eAAe,CAAE,CACfnB,eAAe,CAAE,wBAAwB,CACzCO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CAAC,CACDa,aAAa,CAAE,CACbpB,eAAe,CAAE,wBAAwB,CACzCO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CAAC,CACDc,eAAe,CAAE,CACfrB,eAAe,CAAE,yBAAyB,CAC1CO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CAAC,CACDe,YAAY,CAAE,CACZtB,eAAe,CAAE,yBAAyB,CAC1CO,KAAK,CAAE,SAAS,CAChB,kBAAkB,CAAE,CAClBA,KAAK,CAAE,SACT,CACF,CACF,CACF,CAAC,CACDgB,OAAO,CAAE,CACPzC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJL,YAAY,CAAE,EAAE,CAChBsB,eAAe,CAAE,SAAS,CAC1BC,SAAS,sHAGR,CACDvC,QAAQ,CAAE,CACRuB,EAAE,CAAE,SAAS,CACbC,EAAE,CAAE,WAAW,CACfC,EAAE,CAAE,UACN,CAAC,CACDoB,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GAAG,CACf,SAAS,CAAE,CACTsC,SAAS,6HAIX,CACF,CAAC,CACDuB,YAAY,CAAE,CACZxB,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDkB,cAAc,CAAE,CACdzB,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDmB,YAAY,CAAE,CACZ1B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDoB,UAAU,CAAE,CACV3B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDqB,YAAY,CAAE,CACZ5B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CAAC,CACDsB,SAAS,CAAE,CACT7B,eAAe,CAAE,SAAS,CAC1BO,KAAK,CAAE,SACT,CACF,CACF,CAAC,CACDuB,aAAa,CAAE,CACbhD,cAAc,CAAE,CACdC,IAAI,CAAE,CACJY,YAAY,CAAE,CACZV,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,GAAG,CACPC,EAAE,CAAE,CACN,CAAC,CACDoB,KAAK,CAAE,SACT,CAAC,CACD9C,EAAE,CAAE,CACF8C,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDG,EAAE,CAAE,CACFyC,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDI,EAAE,CAAE,CACFwC,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDK,EAAE,CAAE,CACFuC,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDM,EAAE,CAAE,CACFsC,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDO,EAAE,CAAE,CACFqC,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDY,SAAS,CAAE,CACTgC,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDa,SAAS,CAAE,CACT+B,KAAK,CAAE,SAAS,CAChB5C,UAAU,CAAE,GACd,CAAC,CACDQ,KAAK,CAAE,CACLoC,KAAK,CAAE,SACT,CAAC,CACDnC,KAAK,CAAE,CACLmC,KAAK,CAAE,SACT,CACF,CACF,CAAC,CACDwB,SAAS,CAAE,CACTjD,cAAc,CAAE,CACdC,IAAI,CAAE,CACJ+B,KAAK,CAAE,EAAE,CACTF,MAAM,CAAE,EAAE,CACVT,OAAO,CAAE,CACX,CAAC,CACD6B,UAAU,CAAE,CACV7B,OAAO,CAAE,CAAC,CACV,eAAe,CAAE,CACf8B,SAAS,CAAE,kBAAkB,CAC7B,sBAAsB,CAAE,CACtBpB,OAAO,CAAE,CAAC,CACVb,eAAe,CAAE,SACnB,CACF,CACF,CAAC,CACDkC,KAAK,CAAE,CACLpB,KAAK,CAAE,EAAE,CACTF,MAAM,CAAE,EAAE,CACVZ,eAAe,CAAE,SAAS,CAC1BC,SAAS,sHAIX,CAAC,CACDkC,KAAK,CAAE,CACLtB,OAAO,CAAE,CAAC,CACVnC,YAAY,CAAE,EAAE,CAChBsB,eAAe,CAAE,SAAS,CAC1BC,SAAS,kIAIX,CACF,CACF,CACF,CACF,CAAC,CAAC,CAEF,QAAS,CAAAmC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGzH,QAAQ,CAAC,EAAE,CAAC,CAC1C,KAAM,CAAC0H,OAAO,CAAEC,UAAU,CAAC,CAAG3H,QAAQ,CAAC,CAAE4H,UAAU,CAAE,EAAE,CAAEC,SAAS,CAAE,EAAG,CAAC,CAAC,CACzE,KAAM,CAACC,UAAU,CAAEC,aAAa,CAAC,CAAG/H,QAAQ,CAAC,IAAI,CAAC,CAClD,KAAM,CAACgI,OAAO,CAAEC,UAAU,CAAC,CAAGjI,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAACkI,cAAc,CAAEC,iBAAiB,CAAC,CAAGnI,QAAQ,CAAC,IAAI,CAAC,CAC1D,KAAM,CAACoI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGrI,QAAQ,CAAC,KAAK,CAAC,CACvE,KAAM,CAACsI,eAAe,CAAEC,kBAAkB,CAAC,CAAGvI,QAAQ,CAAC,IAAI,CAAC,CAC5D,KAAM,CAACwI,iBAAiB,CAAEC,oBAAoB,CAAC,CAAGzI,QAAQ,CAAC,KAAK,CAAC,CAEjEC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAAyI,SAAS,CAAG,KAAAA,CAAA,GAAY,CAC5BP,iBAAiB,CAAC,IAAI,CAAC,CACvB,GAAI,CAAAQ,YAAY,CAEhB,GAAI,CACF,KAAM,CAACC,WAAW,CAAEC,eAAe,CAAC,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAAC,CACvDxH,KAAK,CAACyH,GAAG,CAAC,eAAe,CAAC,CAC1BzH,KAAK,CAACyH,GAAG,CAAC,cAAc,CAAC,CAC1B,CAAC,CACFvB,UAAU,CAACmB,WAAW,CAACK,IAAI,CAAC,CAC5BtB,UAAU,CAACkB,eAAe,CAACI,IAAI,CAAC,CAClC,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CAAC,OAAS,CACR;AACA;AACAP,YAAY,CAAGS,UAAU,CAAC,IAAM,CAC9BjB,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CAAE,IAAI,CAAC,CACV,CAEA;AACA,MAAO,IAAM,CACX,GAAIQ,YAAY,CAAEU,YAAY,CAACV,YAAY,CAAC,CAC5CR,iBAAiB,CAAC,KAAK,CAAC,CAC1B,CAAC,CACH,CAAC,CAEDO,SAAS,CAAC,CAAC,CACb,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAY,YAAY,CAAG,KAAO,CAAAC,QAAQ,EAAK,CACvCtB,UAAU,CAAC,IAAI,CAAC,CAChBI,uBAAuB,CAAC,KAAK,CAAC,CAC9BI,oBAAoB,CAAC,KAAK,CAAC,CAE3B,GAAI,CACF;AACA,KAAM,IAAI,CAAAK,OAAO,CAACU,OAAO,EAAIJ,UAAU,CAACI,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAlI,KAAK,CAACmI,IAAI,CAAC,cAAc,CAAEH,QAAQ,CAAC,CAC3DxB,aAAa,CAAC0B,QAAQ,CAACR,IAAI,CAAC,CAE5B;AACAG,UAAU,CAAC,IAAM,CACff,uBAAuB,CAAC,IAAI,CAAC,CAC/B,CAAC,CAAE,GAAG,CAAC,CACT,CAAE,MAAOa,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAClD,CAAC,OAAS,CACRjB,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA0B,uBAAuB,CAAIC,OAAO,EAAK,CAC3CrB,kBAAkB,CAACqB,OAAO,CAAC,CAC3BnB,oBAAoB,CAAC,IAAI,CAAC,CAE1B;AACAW,UAAU,CAAC,IAAM,CACfX,oBAAoB,CAAC,KAAK,CAAC,CAC7B,CAAC,CAAE,KAAK,CAAC,CACX,CAAC,CAED,mBACE1G,KAAA,CAAC7B,aAAa,EAAC8B,KAAK,CAAEA,KAAM,CAAA6H,QAAA,eAC1BhI,IAAA,CAACzB,WAAW,GAAE,CAAC,cACfyB,IAAA,CAACjB,MAAM,GAAE,CAAC,cACViB,IAAA,CAACX,uBAAuB,GAAE,CAAC,CAE1BgH,cAAc,cACbnG,KAAA,CAACzB,GAAG,EACFwJ,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,SAAS,CAAE,MAAM,CACjBC,UAAU,CAAE,SACd,CAAE,CAAAP,QAAA,eAEF9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEpE,KAAK,CAAE,GAAG,CAAEF,MAAM,CAAE,GAAI,CAAE,CAAA8D,QAAA,eACzDhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPtE,KAAK,CAAE,MAAM,CACbF,MAAM,CAAE,MAAM,CACdlC,YAAY,CAAE,KAAK,CACnB0B,MAAM,CAAE,uBAAuB,CAC/BiF,cAAc,CAAExI,KAAK,CAACC,OAAO,CAACwI,OAAO,CAACpI,IAAI,CAC1CqI,SAAS,CAAE,2BAA2B,CACtC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEtD,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,cACFvF,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRtE,KAAK,CAAE,mBAAmB,CAC1BF,MAAM,CAAE,mBAAmB,CAC3BlC,YAAY,CAAE,KAAK,CACnB0B,MAAM,CAAE,uBAAuB,CAC/BiF,cAAc,CAAExI,KAAK,CAACC,OAAO,CAAC0I,SAAS,CAACtI,IAAI,CAC5CqI,SAAS,CAAE,yBAAyB,CACpC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEtD,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,cACFvF,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRtE,KAAK,CAAE,mBAAmB,CAC1BF,MAAM,CAAE,mBAAmB,CAC3BlC,YAAY,CAAE,KAAK,CACnB0B,MAAM,CAAE,uBAAuB,CAC/BiF,cAAc,CAAExI,KAAK,CAACC,OAAO,CAACG,IAAI,CAACC,IAAI,CACvCqI,SAAS,CAAE,2BAA2B,CACtC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEtD,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,EACC,CAAC,cACNvF,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFe,EAAE,CAAE,CAAC,CACL/H,UAAU,CAAE,GAAG,CACfsH,UAAU,CAAE,kDAAkD,CAC9DU,cAAc,CAAE,MAAM,CACtBC,aAAa,CAAE,aAAa,CAC5BL,SAAS,CAAE,mBAAmB,CAC9B,kBAAkB,CAAE,CAClB,IAAI,CAAE,CAAE1E,OAAO,CAAE,GAAI,CAAC,CACtB,KAAK,CAAE,CAAEA,OAAO,CAAE,CAAE,CAAC,CACrB,MAAM,CAAE,CAAEA,OAAO,CAAE,GAAI,CACzB,CACF,CAAE,CAAA6D,QAAA,CACH,oCAED,CAAY,CAAC,EACV,CAAC,cAENhI,IAAA,CAACxB,SAAS,EACRmE,QAAQ,CAAC,IAAI,CACbsF,EAAE,CAAE,CACFe,EAAE,CAAE,CAAEzG,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3B0G,EAAE,CAAE,CAAE5G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3B2G,EAAE,CAAE,CAAE7G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3B4G,EAAE,CAAE,CAAE9G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC3B6B,QAAQ,CAAE,QAAQ,CAClBhB,eAAe,CAAE,SAAS,CAC1BtB,YAAY,CAAE,MAAM,CACpBuB,SAAS,wIAIX,CAAE,CAAAyE,QAAA,cAEF9H,KAAA,CAACtB,IAAI,EACHmE,SAAS,MACTuG,OAAO,CAAE,CAAE/G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CACjCwF,EAAE,CAAE,CACF,iBAAiB,CAAE,CACjBC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QACjB,CACF,CAAE,CAAAH,QAAA,eAEFhI,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAAAyF,QAAA,cAChB9H,KAAA,CAACxB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAC,CACnB0G,EAAE,CAAE,CAAC,CACL7F,eAAe,CAAE,SAAS,CAC1BkF,QAAQ,CAAE,UAAU,CACpBlE,QAAQ,CAAE,QAAQ,CAClBtC,YAAY,CAAE,CAAC,CACfwB,UAAU,CAAE,sBAAsB,CAClCD,SAAS,gJAGR,CACD,SAAS,CAAE,CACTA,SAAS,uJAIX,CACF,CAAE,CAAAyE,QAAA,eAEFhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNgB,KAAK,CAAE,CAAC,CACRrF,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,qEAAqE,CACjFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF1J,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTjB,IAAI,CAAE,CAAC,CACPtE,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFxJ,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEkB,MAAM,CAAE,CAAE,CAAE,CAAA1B,QAAA,eAC3C9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,GAAG,CACNxH,YAAY,CAAE,KAAK,CACnBuG,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB9E,SAAS,CAAE,qCACb,CAAE,CAAAyE,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZc,SAAS,CAAC,IAAI,CACd5B,EAAE,CAAE,CACFM,UAAU,CAAE,kDAAkD,CAC9DU,cAAc,CAAE,MAAM,CACtBC,aAAa,CAAE,aAAa,CAC5BjI,UAAU,CAAE,GAAG,CACfC,aAAa,CAAE,QACjB,CAAE,CAAA8G,QAAA,CACH,8BAED,CAAY,CAAC,EACV,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,OAAO,CACfe,SAAS,MACT7B,EAAE,CAAE,CACFjH,QAAQ,CAAE,QAAQ,CAClB2B,QAAQ,CAAE,KAAK,CACfkB,KAAK,CAAE,gBAAgB,CACvB1C,UAAU,CAAE,GAAG,CACfgI,EAAE,CAAE,CACN,CAAE,CAAAnB,QAAA,CACH,0NAGD,CAAY,CAAC,cACb9H,KAAA,CAACzB,GAAG,EACFwJ,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACf6B,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,MAAM,CAChBhB,EAAE,CAAE,CACN,CAAE,CAAAhB,QAAA,eAEFhI,IAAA,CAAClB,IAAI,EACHmL,KAAK,CAAC,wBAAwB,CAC9BpG,KAAK,CAAC,SAAS,CACfqG,IAAI,CAAC,QAAQ,CACbC,IAAI,cAAEnK,IAAA,SAAAgI,QAAA,CAAM,cAAE,CAAM,CAAE,CACtBC,EAAE,CAAE,CAAEhH,UAAU,CAAE,GAAG,CAAEmI,EAAE,CAAE,CAAE,CAAE,CAChC,CAAC,cACFpJ,IAAA,CAAClB,IAAI,EACHmL,KAAK,CAAC,2BAA2B,CACjCpG,KAAK,CAAC,MAAM,CACZqG,IAAI,CAAC,QAAQ,CACbC,IAAI,cAAEnK,IAAA,SAAAgI,QAAA,CAAM,cAAE,CAAM,CAAE,CACtBC,EAAE,CAAE,CAAEhH,UAAU,CAAE,GAAG,CAAEmI,EAAE,CAAE,CAAE,CAAE,CAChC,CAAC,cACFpJ,IAAA,CAAClB,IAAI,EACHmL,KAAK,CAAC,wBAAwB,CAC9BpG,KAAK,CAAC,SAAS,CACfqG,IAAI,CAAC,QAAQ,CACbC,IAAI,cAAEnK,IAAA,SAAAgI,QAAA,CAAM,oBAAG,CAAM,CAAE,CACvBC,EAAE,CAAE,CAAEhH,UAAU,CAAE,GAAG,CAAEmI,EAAE,CAAE,CAAE,CAAE,CAChC,CAAC,EACC,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,cAEPpJ,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAACK,EAAE,CAAE,CAAE,CAAAoF,QAAA,cACvB9H,KAAA,CAACxB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAC,CACnByB,MAAM,CAAE,MAAM,CACdsE,QAAQ,CAAE,UAAU,CACpBlE,QAAQ,CAAE,QAAQ,CAClBtC,YAAY,CAAE,CAAC,CACfwB,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAAyE,QAAA,eAEFhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPtE,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF1J,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTF,KAAK,CAAE,CAAC,CACRrF,KAAK,CAAE,MAAM,CACbF,MAAM,CAAE,MAAM,CACdqE,UAAU,CAAE,sEAAsE,CAClFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFxJ,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEkB,MAAM,CAAE,CAAE,CAAE,CAAA1B,QAAA,eAC3C9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJxH,YAAY,CAAE,MAAM,CACpBuG,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFhH,UAAU,CAAE,GAAG,CACf4C,KAAK,CAAE1D,KAAK,CAACC,OAAO,CAACwI,OAAO,CAACpI,IAAI,CACjCU,aAAa,CAAE,QACjB,CAAE,CAAA8G,QAAA,CACH,oBAED,CAAY,CAAC,EACV,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,OAAO,CACfd,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLtF,KAAK,CAAE,gBAAgB,CACvBlB,QAAQ,CAAE,KACZ,CAAE,CAAAqF,QAAA,CACH,yJAGD,CAAY,CAAC,cACbhI,IAAA,CAACf,cAAc,EACb4G,OAAO,CAAEA,OAAQ,CACjBuE,QAAQ,CAAE3C,YAAa,CACvBtB,OAAO,CAAEA,OAAQ,CAClB,CAAC,EACC,CAAC,EACD,CAAC,CACJ,CAAC,cAEPnG,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAACK,EAAE,CAAE,CAAE,CAAAoF,QAAA,cACvB9H,KAAA,CAACxB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAC,CACnByB,MAAM,CAAE,MAAM,CACdsE,QAAQ,CAAE,UAAU,CACpBlE,QAAQ,CAAE,QAAQ,CAClBtC,YAAY,CAAE,CAAC,CACfwB,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAAyE,QAAA,eAEFhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNgB,KAAK,CAAE,CAAC,CACRrF,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF1J,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,CACTjB,IAAI,CAAE,CAAC,CACPtE,KAAK,CAAE,MAAM,CACbF,MAAM,CAAE,MAAM,CACdqE,UAAU,CAAE,qEAAqE,CACjFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF1J,IAAA,CAACvB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEkB,MAAM,CAAE,CAAE,CAAE,CAAA1B,QAAA,CAC1C7B,OAAO,cACNnG,IAAA,CAACP,gBAAgB,EAACU,KAAK,CAAEA,KAAM,CAAE,CAAC,CAChC8F,UAAU,EAAIM,oBAAoB,cACpCrG,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CACP9D,OAAO,CAAEoC,oBAAoB,CAAG,CAAC,CAAG,CAAC,CACrChB,SAAS,CAAEgB,oBAAoB,CAAG,eAAe,CAAG,kBAAkB,CACtE/C,UAAU,CAAE,sBACd,CAAE,CAAAwE,QAAA,eACA9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJxH,YAAY,CAAE,MAAM,CACpBuG,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFhH,UAAU,CAAE,GAAG,CACf4C,KAAK,CAAE1D,KAAK,CAACC,OAAO,CAACwI,OAAO,CAACpI,IAAI,CACjCU,aAAa,CAAE,QACjB,CAAE,CAAA8G,QAAA,CACH,iBAED,CAAY,CAAC,EACV,CAAC,cACNhI,IAAA,CAACd,aAAa,EAAC+G,UAAU,CAAEA,UAAW,CAAE,CAAC,EACtC,CAAC,cAEN/F,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,aAAa,CAAE,QAAQ,CACvBC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBgB,EAAE,CAAE,CAAC,CACLlF,OAAO,CAAEgC,OAAO,CAAG,CAAC,CAAG,CAAC,CACxB3C,UAAU,CAAE,0BACd,CAAE,CAAAwE,QAAA,eACAhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF7D,KAAK,CAAE,GAAG,CACVF,MAAM,CAAE,GAAG,CACXlC,YAAY,CAAE,KAAK,CACnBkG,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBE,UAAU,CAAE,mFAAmF,CAC/FhF,SAAS,CAAE,qCAAqC,CAChD4F,EAAE,CAAE,CAAC,CACLN,SAAS,CAAE,+BAA+B,CAC1C,kBAAkB,CAAE,CAClB,IAAI,CAAE,CAAEtD,SAAS,CAAE,iBAAkB,CAAC,CACtC,KAAK,CAAE,CAAEA,SAAS,CAAE,mBAAoB,CAAC,CACzC,MAAM,CAAE,CAAEA,SAAS,CAAE,iBAAkB,CACzC,CACF,CAAE,CAAAyC,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACd,EAAE,CAAE,CAAEhH,UAAU,CAAE,GAAG,CAAE4C,KAAK,CAAE1D,KAAK,CAACC,OAAO,CAACwI,OAAO,CAACpI,IAAI,CAAE6J,SAAS,CAAE,QAAQ,CAAElB,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,CAAC,0BAEjH,CAAY,CAAC,cACbhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,OAAO,CAAClF,KAAK,CAAC,gBAAgB,CAACoE,EAAE,CAAE,CAAEoC,SAAS,CAAE,QAAQ,CAAE1H,QAAQ,CAAE,KAAK,CAAE2H,EAAE,CAAE,MAAO,CAAE,CAAAtC,QAAA,CAAC,4EAE7G,CAAY,CAAC,EACV,CACN,CACE,CAAC,EACD,CAAC,CACJ,CAAC,CAEN/B,UAAU,EAAIA,UAAU,CAACsE,eAAe,EAAIhE,oBAAoB,eAC/DvG,IAAA,CAACT,OAAO,EAACiL,KAAK,CAAE,GAAI,CAAAxC,QAAA,cAClBhI,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAAAyF,QAAA,cAChB9H,KAAA,CAACxB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CAC1B8F,UAAU,CAAE,mDAAmD,CAC/DvG,YAAY,CAAE,CAAC,CACfwG,QAAQ,CAAE,UAAU,CACpBlE,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClCE,MAAM,CAAE,oCAAoC,CAC5C,SAAS,CAAE,CACTH,SAAS,CAAE,sCAAsC,CACjDgC,SAAS,CAAE,kBACb,CACF,CAAE,CAAAyC,QAAA,eAGFhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,EAAE,CACRgB,KAAK,CAAE,CAAC,EAAE,CACVrF,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,qEAAqE,CACjFvG,YAAY,CAAE,KAAK,CACnB0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF1J,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBmB,MAAM,CAAE,CAAC,EAAE,CACXjB,IAAI,CAAE,CAAC,EAAE,CACTtE,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,qEAAqE,CACjFvG,YAAY,CAAE,KAAK,CACnB0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACF1J,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,KAAK,CACVgB,KAAK,CAAE,KAAK,CACZrF,KAAK,CAAE,MAAM,CACbF,MAAM,CAAE,MAAM,CACdqE,UAAU,CAAE,sEAAsE,CAClFvG,YAAY,CAAE,KAAK,CACnB0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cAEFxJ,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEkB,MAAM,CAAE,CAAE,CAAE,CAAA1B,QAAA,eAC3C9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBe,EAAE,CAAE,CAAE5G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBiI,EAAE,CAAE,CAAC,CACLC,YAAY,CAAE,mCAChB,CAAE,CAAA1C,QAAA,eACAhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAErH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACpBgH,CAAC,CAAE,CAAEjH,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBR,YAAY,CAAE,CAAC,CACfuG,UAAU,CAAE,oFAAoF,CAChGL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxB9E,SAAS,CAAE,mCACb,CAAE,CAAAyE,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACN9H,KAAA,CAACzB,GAAG,EAAAuJ,QAAA,eACFhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFhH,UAAU,CAAE,GAAG,CACf4C,KAAK,CAAE1D,KAAK,CAACC,OAAO,CAACwI,OAAO,CAAClI,IAAI,CACjCQ,aAAa,CAAE,QAAQ,CACvBiI,EAAE,CAAE,GACN,CAAE,CAAAnB,QAAA,CACH,sBAED,CAAY,CAAC,cACbhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,WAAW,CACnBd,EAAE,CAAE,CACFpE,KAAK,CAAE,gBAAgB,CACvB5C,UAAU,CAAE,GACd,CAAE,CAAA+G,QAAA,CACH,0CAED,CAAY,CAAC,EACV,CAAC,EACH,CAAC,cAENhI,IAAA,CAACvB,GAAG,EAACwJ,EAAE,CAAE,CAAEkB,EAAE,CAAE,CAAE5G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAAwF,QAAA,cAChC9H,KAAA,CAACvB,UAAU,EACToK,OAAO,CAAC,OAAO,CACfd,EAAE,CAAE,CACFjH,QAAQ,CAAE,CAAEuB,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,QAAS,CAAC,CACtCrB,UAAU,CAAE,GAAG,CACf0C,KAAK,CAAE,gBAAgB,CACvBlB,QAAQ,CAAE,OACZ,CAAE,CAAAqF,QAAA,EACH,yIAEC,CAAC/B,UAAU,CAAC0E,aAAa,EAAI,sHAAsH,EACzI,CAAC,CACV,CAAC,cAEN3K,IAAA,CAACvB,GAAG,EAACwJ,EAAE,CAAE,CACPuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBR,YAAY,CAAE,CAAC,CACfuG,UAAU,CAAE,0BAA0B,CACtC7E,MAAM,CAAE,mCACV,CAAE,CAAAsE,QAAA,cACAhI,IAAA,CAACb,gBAAgB,EAACyL,cAAc,CAAE3E,UAAU,CAACsE,eAAgB,CAAE,CAAC,CAC7D,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,CACA,CACV,CAGA5D,iBAAiB,EAAIF,eAAe,eACnCzG,IAAA,CAACR,OAAO,EAACgL,KAAK,CAAE,GAAI,CAAAxC,QAAA,cAClBhI,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAAAyF,QAAA,cAChBhI,IAAA,CAACnB,KAAK,EACJgM,QAAQ,CAAEpE,eAAe,CAACqE,YAAY,CAAG,EAAE,CAAG,OAAO,CAAGrE,eAAe,CAACqE,YAAY,CAAG,EAAE,CAAG,SAAS,CAAG,MAAO,CAC/G/B,OAAO,CAAC,QAAQ,CAChBd,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLnH,YAAY,CAAE,CAAC,CACfuB,SAAS,CAAE,gCAAgC,CAC3C,kBAAkB,CAAE,CAAEvC,QAAQ,CAAE,QAAS,CAAC,CAC1CwI,CAAC,CAAE,CAAC,CACJX,SAAS,CAAE,mBAAmB,CAC9B,kBAAkB,CAAE,CAClB,IAAI,CAAE,CAAEtF,SAAS,CAAE,gCAAiC,CAAC,CACrD,KAAK,CAAE,CAAEA,SAAS,CAAE,gCAAiC,CAAC,CACtD,MAAM,CAAE,CAAEA,SAAS,CAAE,gCAAiC,CACxD,CACF,CAAE,CAAAyE,QAAA,cAEFhI,IAAA,CAACvB,GAAG,EAACwJ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,YAAa,CAAE,CAAAJ,QAAA,cACrD9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAE8C,QAAQ,CAAE,CAAE,CAAE,CAAA/C,QAAA,eACvBhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACd,EAAE,CAAE,CAAEhH,UAAU,CAAE,MAAM,CAAEkI,EAAE,CAAE,GAAI,CAAE,CAAAnB,QAAA,CAC1DvB,eAAe,CAACqE,YAAY,CAAG,EAAE,CAC9B,8CAA8C,CAC9CrE,eAAe,CAACqE,YAAY,CAAG,EAAE,CAC/B,+CAA+C,CAC/C,kCAAkC,CAC9B,CAAC,cACb5K,KAAA,CAACvB,UAAU,EAACoK,OAAO,CAAC,OAAO,CAACd,EAAE,CAAE,CAAEhH,UAAU,CAAE,GAAI,CAAE,CAAA+G,QAAA,EAAC,YACzC,cAAAhI,IAAA,WAAAgI,QAAA,CAASvB,eAAe,CAACuE,QAAQ,EAAI,eAAe,CAAS,CAAC,EAC9D,CAAC,cACb9K,KAAA,CAACvB,UAAU,EAACoK,OAAO,CAAC,OAAO,CAAAf,QAAA,EAAC,qBACP,cAAAhI,IAAA,WAAAgI,QAAA,CAASvB,eAAe,CAACqE,YAAY,CAACG,OAAO,CAAC,CAAC,CAAC,CAAS,CAAC,oBAAiB,cAAAjL,IAAA,WAAAgI,QAAA,CAASvB,eAAe,CAACyE,WAAW,CAAS,CAAC,mBAC9H,cAAAlL,IAAA,WAAAgI,QAAA,CAASvB,eAAe,CAAC0E,SAAS,CAAS,CAAC,IAC5D,EAAY,CAAC,CACZ1E,eAAe,CAACqE,YAAY,CAAG,EAAE,eAChC9K,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,OAAO,CAACd,EAAE,CAAE,CAAEe,EAAE,CAAE,CAAC,CAAEoC,SAAS,CAAE,QAAS,CAAE,CAAApD,QAAA,CAAC,uEAEhE,CAAY,CACb,EACE,CAAC,CACH,CAAC,CACD,CAAC,CACJ,CAAC,CACA,CACV,CAGA/B,UAAU,EAAIM,oBAAoB,eACjCvG,IAAA,CAACT,OAAO,EAACiL,KAAK,CAAE,GAAI,CAAAxC,QAAA,cAClBhI,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAAAyF,QAAA,cAChBhI,IAAA,CAACvB,GAAG,EAACwJ,EAAE,CAAE,CACPuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAC,CACnBR,YAAY,CAAE,CAAC,CACfuG,UAAU,CAAE,qFAAqF,CACjG7E,MAAM,CAAE,oCACV,CAAE,CAAAsE,QAAA,cACAhI,IAAA,CAACtB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFjG,YAAY,CAAE,CAAC,CACfsC,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC+E,UAAU,CAAE,mDAAmD,CAC/D,SAAS,CAAE,CACThF,SAAS,CAAE,sCAAsC,CACjDgC,SAAS,CAAE,kBACb,CACF,CAAE,CAAAyC,QAAA,cAEFhI,IAAA,CAACZ,qBAAqB,EACpBsI,QAAQ,CAAEzB,UAAU,CAACoF,UAAW,CAChCC,mBAAmB,CAAExD,uBAAwB,CAC9C,CAAC,CACG,CAAC,CACL,CAAC,CACF,CAAC,CACA,CACV,cAED9H,IAAA,CAACT,OAAO,EAACiL,KAAK,CAAE,GAAI,CAAAxC,QAAA,cAClBhI,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAAAyF,QAAA,cAChB9H,KAAA,CAACxB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAC,CACnB8F,UAAU,CAAE,mDAAmD,CAC/DvG,YAAY,CAAE,CAAC,CACfwG,QAAQ,CAAE,UAAU,CACpBlE,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAAyE,QAAA,eAEFhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNgB,KAAK,CAAE,CAAC,CACRrF,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,sEAAsE,CAClFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFxJ,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEkB,MAAM,CAAE,CAAE,CAAE,CAAA1B,QAAA,eAC3C9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJxH,YAAY,CAAE,MAAM,CACpBuG,UAAU,CAAE,mFAAmF,CAC/FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,oBAAG,CAAY,CAAC,CACvD,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFhH,UAAU,CAAE,GAAG,CACf4C,KAAK,CAAE1D,KAAK,CAACC,OAAO,CAACwI,OAAO,CAAClI,IAAI,CACjCQ,aAAa,CAAE,QACjB,CAAE,CAAA8G,QAAA,CACH,gBAED,CAAY,CAAC,EACV,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,OAAO,CACfe,SAAS,MACT7B,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLtF,KAAK,CAAE,gBAAgB,CACvBlB,QAAQ,CAAE,OACZ,CAAE,CAAAqF,QAAA,CACH,oNAID,CAAY,CAAC,cACbhI,IAAA,CAACvB,GAAG,EAACwJ,EAAE,CAAE,CACPjG,YAAY,CAAE,CAAC,CACfsC,QAAQ,CAAE,QAAQ,CAClBf,SAAS,CAAE,gCAAgC,CAC3CW,MAAM,CAAE,OACV,CAAE,CAAA8D,QAAA,cACAhI,IAAA,CAAChB,QAAQ,EAAC2G,OAAO,CAAEA,OAAQ,CAAE,CAAC,CAC3B,CAAC,EACH,CAAC,EACD,CAAC,CACJ,CAAC,CACA,CAAC,cAGV3F,IAAA,CAACT,OAAO,EAACiL,KAAK,CAAE,GAAI,CAAAxC,QAAA,cAClBhI,IAAA,CAACpB,IAAI,EAACsE,IAAI,MAACX,EAAE,CAAE,EAAG,CAAAyF,QAAA,cAChB9H,KAAA,CAACxB,KAAK,EACJ6K,SAAS,CAAE,CAAE,CACbtB,EAAE,CAAE,CACFuB,CAAC,CAAE,CAAEjH,EAAE,CAAE,CAAC,CAAEE,EAAE,CAAE,CAAE,CAAC,CACnB8F,UAAU,CAAE,mDAAmD,CAC/DvG,YAAY,CAAE,CAAC,CACfwG,QAAQ,CAAE,UAAU,CACpBlE,QAAQ,CAAE,QAAQ,CAClBd,UAAU,CAAE,sBAAsB,CAClC,SAAS,CAAE,CACTD,SAAS,CAAE,qCACb,CACF,CAAE,CAAAyE,QAAA,eAEFhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACFO,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNgB,KAAK,CAAE,CAAC,CACRrF,KAAK,CAAE,OAAO,CACdF,MAAM,CAAE,OAAO,CACfqE,UAAU,CAAE,qEAAqE,CACjFvG,YAAY,CAAE,YAAY,CAC1B0H,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cACFxJ,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEO,QAAQ,CAAE,UAAU,CAAEkB,MAAM,CAAE,CAAE,CAAE,CAAA1B,QAAA,eAC3C9H,KAAA,CAACzB,GAAG,EAACwJ,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEe,EAAE,CAAE,CAAE,CAAE,CAAAnB,QAAA,eACxDhI,IAAA,CAACvB,GAAG,EACFwJ,EAAE,CAAE,CACF2B,EAAE,CAAE,CAAC,CACLJ,CAAC,CAAE,CAAC,CACJxH,YAAY,CAAE,MAAM,CACpBuG,UAAU,CAAE,kFAAkF,CAC9FL,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAClB,CAAE,CAAAL,QAAA,cAEFhI,IAAA,CAACrB,UAAU,EAACoK,OAAO,CAAC,IAAI,CAACc,SAAS,CAAC,MAAM,CAAA7B,QAAA,CAAC,cAAE,CAAY,CAAC,CACtD,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,IAAI,CACZd,EAAE,CAAE,CACFhH,UAAU,CAAE,GAAG,CACf4C,KAAK,CAAE1D,KAAK,CAACC,OAAO,CAACwI,OAAO,CAAClI,IAAI,CACjCQ,aAAa,CAAE,QACjB,CAAE,CAAA8G,QAAA,CACH,mBAED,CAAY,CAAC,EACV,CAAC,cACNhI,IAAA,CAACrB,UAAU,EACToK,OAAO,CAAC,OAAO,CACfe,SAAS,MACT7B,EAAE,CAAE,CACFkB,EAAE,CAAE,CAAC,CACLtF,KAAK,CAAE,gBAAgB,CACvBlB,QAAQ,CAAE,OACZ,CAAE,CAAAqF,QAAA,CACH,iLAGD,CAAY,CAAC,cACbhI,IAAA,CAACV,gBAAgB,GAAE,CAAC,EACjB,CAAC,EACD,CAAC,CACJ,CAAC,CACA,CAAC,EACN,CAAC,CACE,CACZ,EACY,CAAC,CAEpB,CAEA,cAAe,CAAAoG,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}