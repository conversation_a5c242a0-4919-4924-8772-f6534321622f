{"ast": null, "code": "import _objectSpread from\"/Users/<USER>/Downloads/Flood/frontend/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback}from'react';import{<PERSON><PERSON>ontainer,Tile<PERSON>ayer,CircleMarker,Popup,useMap,LayerGroup,ZoomControl,Rectangle,Marker,Polygon}from'react-leaflet';import L from'leaflet';import'./FloodMap.css';// Import custom CSS for map\nimport{Box,Typography,Skeleton,Paper,Chip,Divider,Card,CardContent,Grid,useTheme,Button,Tooltip,IconButton,Fade,TextField,InputAdornment,List,ListItem,ListItemText,ListItemIcon,Alert,Collapse,Snackbar,ToggleButton,ToggleButtonGroup}from'@mui/material';import WaterDropIcon from'@mui/icons-material/WaterDrop';import TerrainIcon from'@mui/icons-material/Terrain';import ThunderstormIcon from'@mui/icons-material/Thunderstorm';import InfoIcon from'@mui/icons-material/Info';import SatelliteIcon from'@mui/icons-material/Satellite';import DarkModeIcon from'@mui/icons-material/DarkMode';import MapIcon from'@mui/icons-material/Map';import LayersIcon from'@mui/icons-material/Layers';import MyLocationIcon from'@mui/icons-material/MyLocation';import ZoomInIcon from'@mui/icons-material/ZoomIn';import ZoomOutIcon from'@mui/icons-material/ZoomOut';import SearchIcon from'@mui/icons-material/Search';import LocationOnIcon from'@mui/icons-material/LocationOn';import CloseIcon from'@mui/icons-material/Close';import WarningIcon from'@mui/icons-material/Warning';import EngineeringIcon from'@mui/icons-material/Engineering';import HomeIcon from'@mui/icons-material/Home';import{useSpring,animated}from'react-spring';import axios from'axios';// Legend component\nimport{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";const MapLegend=()=>{const theme=useTheme();const[expanded,setExpanded]=useState(false);const legendAnimation=useSpring({opacity:expanded?1:0.9,height:expanded?280:40,config:{tension:200,friction:20}});return/*#__PURE__*/_jsxs(animated.div,{style:_objectSpread({position:'absolute',bottom:20,right:10,zIndex:1000,backgroundColor:'white',padding:'10px 15px',borderRadius:8,boxShadow:'0 2px 10px rgba(0,0,0,0.1)',overflow:'hidden',width:220},legendAnimation),children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:expanded?1:0},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",children:\"Map Legend\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setExpanded(!expanded),sx:{backgroundColor:expanded?'rgba(0,0,0,0.05)':'transparent',transition:'all 0.2s ease'},children:/*#__PURE__*/_jsx(InfoIcon,{fontSize:\"small\"})})]}),expanded&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Divider,{sx:{my:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",fontWeight:\"bold\",sx:{mt:1},children:\"Flood Risk Indicators\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:16,height:16,borderRadius:'50%',backgroundColor:theme.palette.error.main,mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"High Risk Area\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:0.5},children:[/*#__PURE__*/_jsx(Box,{sx:{width:16,height:16,borderRadius:'50%',backgroundColor:theme.palette.success.main,mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Low Risk Area\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",fontWeight:\"bold\",sx:{mt:2},children:\"Risk Factors\"}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:1},children:[/*#__PURE__*/_jsx(ThunderstormIcon,{fontSize:\"small\",sx:{mr:1,color:theme.palette.info.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Heavy Rainfall (>200mm)\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:0.5},children:[/*#__PURE__*/_jsx(TerrainIcon,{fontSize:\"small\",sx:{mr:1,color:theme.palette.warning.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"Low Elevation (<100m)\"})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mt:0.5},children:[/*#__PURE__*/_jsx(WaterDropIcon,{fontSize:\"small\",sx:{mr:1,color:theme.palette.error.main}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"High Water Level (>8m)\"})]}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',mt:2,color:theme.palette.text.secondary},children:\"Click on markers for detailed information about specific locations.\"})]})]});};// Search component\nconst LocationSearch=_ref=>{let{onLocationSelect}=_ref;const map=useMap();const theme=useTheme();const[searchQuery,setSearchQuery]=useState('');const[searchResults,setSearchResults]=useState([]);const[searching,setSearching]=useState(false);const[showResults,setShowResults]=useState(false);const[error,setError]=useState(null);// Geocoding API (using OpenStreetMap Nominatim)\nconst searchLocation=useCallback(async query=>{if(!query||query.trim().length<3)return;setSearching(true);setError(null);// Clear any existing timeout to prevent memory leaks\nlet searchTimeout;try{// Add \"India\" to the search query to focus on Indian locations\nconst response=await axios.get(\"https://nominatim.openstreetmap.org/search\",{params:{q:\"\".concat(query,\", India\"),format:'json',limit:5,countrycodes:'in',// Limit to India\naddressdetails:1},headers:{'Accept-Language':'en-US,en;q=0.9','User-Agent':'FloodRiskPredictionApp'}});if(response.data&&response.data.length>0){setSearchResults(response.data);setShowResults(true);}else{setSearchResults([]);setError('No locations found. Try a different search term.');}}catch(err){console.error('Error searching for location:',err);setError('Error searching for location. Please try again.');}finally{// Ensure searching state is properly reset with a slight delay\n// This prevents UI flicker if multiple searches happen quickly\nsearchTimeout=setTimeout(()=>{setSearching(false);},300);}return()=>{if(searchTimeout)clearTimeout(searchTimeout);};},[]);const handleSearchChange=e=>{setSearchQuery(e.target.value);if(e.target.value.trim().length===0){setShowResults(false);}};const handleSearchSubmit=e=>{e.preventDefault();searchLocation(searchQuery);};const handleLocationClick=location=>{const lat=parseFloat(location.lat);const lng=parseFloat(location.lon);// Fly to the location\nmap.flyTo([lat,lng],12,{duration:1.5});// Pass the selected location to parent component\nonLocationSelect({lat,lng,name:location.display_name,type:location.type,importance:location.importance,address:location.address});// Clear search results\nsetShowResults(false);};return/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:10,left:10,zIndex:1000,width:300,maxWidth:'calc(100% - 20px)'},children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:1.5,borderRadius:2,boxShadow:'0 4px 12px rgba(0,0,0,0.1)'},children:[/*#__PURE__*/_jsx(\"form\",{onSubmit:handleSearchSubmit,children:/*#__PURE__*/_jsx(TextField,{fullWidth:true,placeholder:\"Search for a place in India...\",value:searchQuery,onChange:handleSearchChange,variant:\"outlined\",size:\"small\",InputProps:{startAdornment:/*#__PURE__*/_jsx(InputAdornment,{position:\"start\",children:/*#__PURE__*/_jsx(SearchIcon,{color:\"action\"})}),endAdornment:searching?/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(Box,{sx:{width:20,height:20,borderRadius:'50%',border:'2px solid transparent',borderTopColor:theme.palette.primary.main,animation:'spin 1s linear infinite'}})}):searchQuery?/*#__PURE__*/_jsx(InputAdornment,{position:\"end\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>{setSearchQuery('');setShowResults(false);},children:/*#__PURE__*/_jsx(CloseIcon,{fontSize:\"small\"})})}):null,sx:{borderRadius:2,'&.Mui-focused':{boxShadow:\"0 0 0 2px \".concat(theme.palette.primary.main,\"20\")}}}})}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"warning\",sx:{mt:1,borderRadius:1},onClose:()=>setError(null),children:error}),/*#__PURE__*/_jsx(Collapse,{in:showResults&&searchResults.length>0,children:/*#__PURE__*/_jsx(List,{sx:{mt:1,maxHeight:300,overflow:'auto'},children:searchResults.map((result,index)=>{var _result$address,_result$address2,_result$address3,_result$address4;return/*#__PURE__*/_jsxs(ListItem,{button:true,onClick:()=>handleLocationClick(result),sx:{borderRadius:1,mb:0.5,'&:hover':{backgroundColor:theme.palette.action.hover}},children:[/*#__PURE__*/_jsx(ListItemIcon,{sx:{minWidth:36},children:/*#__PURE__*/_jsx(LocationOnIcon,{color:\"primary\"})}),/*#__PURE__*/_jsx(ListItemText,{primary:((_result$address=result.address)===null||_result$address===void 0?void 0:_result$address.city)||((_result$address2=result.address)===null||_result$address2===void 0?void 0:_result$address2.town)||((_result$address3=result.address)===null||_result$address3===void 0?void 0:_result$address3.village)||((_result$address4=result.address)===null||_result$address4===void 0?void 0:_result$address4.state)||result.display_name.split(',')[0],secondary:result.display_name,secondaryTypographyProps:{noWrap:true,style:{fontSize:'0.75rem'}}})]},index);})})})]})});};// Map style toggle component\nconst MapStyleToggle=_ref2=>{let{mapStyle,onStyleChange}=_ref2;const theme=useTheme();return/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:10,left:10,zIndex:1000,backgroundColor:'rgba(255, 255, 255, 0.95)',backdropFilter:'blur(10px)',borderRadius:2,padding:1,boxShadow:'0 8px 24px rgba(0, 0, 0, 0.15)',border:'1px solid rgba(255, 255, 255, 0.2)'},children:/*#__PURE__*/_jsxs(ToggleButtonGroup,{value:mapStyle,exclusive:true,onChange:(event,newStyle)=>{if(newStyle!==null){onStyleChange(newStyle);}},size:\"small\",sx:{'& .MuiToggleButton-root':{border:'none',borderRadius:1.5,margin:0.25,padding:'6px 12px',transition:'all 0.3s ease','&:hover':{backgroundColor:theme.palette.primary.light+'20',transform:'scale(1.05)'},'&.Mui-selected':{backgroundColor:theme.palette.primary.main,color:'white','&:hover':{backgroundColor:theme.palette.primary.dark}}}},children:[/*#__PURE__*/_jsx(ToggleButton,{value:\"dark\",\"aria-label\":\"dark theme\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"Dark Theme\",placement:\"bottom\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(DarkModeIcon,{fontSize:\"small\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:{xs:'none',sm:'block'}},children:\"Dark\"})]})})}),/*#__PURE__*/_jsx(ToggleButton,{value:\"satellite\",\"aria-label\":\"satellite view\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"Satellite View\",placement:\"bottom\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(SatelliteIcon,{fontSize:\"small\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:{xs:'none',sm:'block'}},children:\"Satellite\"})]})})}),/*#__PURE__*/_jsx(ToggleButton,{value:\"terrain\",\"aria-label\":\"terrain view\",children:/*#__PURE__*/_jsx(Tooltip,{title:\"Terrain View\",placement:\"bottom\",children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:0.5},children:[/*#__PURE__*/_jsx(TerrainIcon,{fontSize:\"small\"}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:{xs:'none',sm:'block'}},children:\"Terrain\"})]})})})]})});};// Map controls component\nconst MapControls=()=>{const map=useMap();const theme=useTheme();const handleZoomIn=()=>{map.zoomIn();};const handleZoomOut=()=>{map.zoomOut();};const handleLocate=()=>{map.locate({setView:true,maxZoom:10});};return/*#__PURE__*/_jsxs(Box,{sx:{position:'absolute',top:10,right:10,zIndex:1000,display:'flex',flexDirection:'column',gap:1},children:[/*#__PURE__*/_jsx(Tooltip,{title:\"Zoom In\",placement:\"left\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleZoomIn,sx:{backgroundColor:'rgba(255, 255, 255, 0.95)',backdropFilter:'blur(10px)',boxShadow:'0 4px 12px rgba(0,0,0,0.15)',border:'1px solid rgba(255, 255, 255, 0.2)',transition:'all 0.3s ease','&:hover':{backgroundColor:theme.palette.primary.main,color:'white',transform:'scale(1.1)'}},children:/*#__PURE__*/_jsx(ZoomInIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Zoom Out\",placement:\"left\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleZoomOut,sx:{backgroundColor:'rgba(255, 255, 255, 0.95)',backdropFilter:'blur(10px)',boxShadow:'0 4px 12px rgba(0,0,0,0.15)',border:'1px solid rgba(255, 255, 255, 0.2)',transition:'all 0.3s ease','&:hover':{backgroundColor:theme.palette.primary.main,color:'white',transform:'scale(1.1)'}},children:/*#__PURE__*/_jsx(ZoomOutIcon,{})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"My Location\",placement:\"left\",children:/*#__PURE__*/_jsx(IconButton,{onClick:handleLocate,sx:{backgroundColor:'rgba(255, 255, 255, 0.95)',backdropFilter:'blur(10px)',boxShadow:'0 4px 12px rgba(0,0,0,0.15)',border:'1px solid rgba(255, 255, 255, 0.2)',transition:'all 0.3s ease','&:hover':{backgroundColor:theme.palette.success.main,color:'white',transform:'scale(1.1)'}},children:/*#__PURE__*/_jsx(MyLocationIcon,{})})})]});};// Heat map overlay component for risk visualization\nconst RiskHeatMap=_ref3=>{let{mapData}=_ref3;const map=useMap();const[heatMapVisible,setHeatMapVisible]=useState(true);useEffect(()=>{if(!mapData||mapData.length===0||!heatMapVisible)return;// Create heat map zones based on risk density\nconst highRiskPoints=mapData.filter(point=>point.Flood_Prediction===1);const riskZones=[];// Group nearby high-risk points into zones\nconst processedPoints=new Set();highRiskPoints.forEach((point,index)=>{if(processedPoints.has(index))return;const zone=[point];const zoneCenter={lat:point.Latitude,lng:point.Longitude};// Find nearby points within 0.5 degrees\nhighRiskPoints.forEach((otherPoint,otherIndex)=>{if(index===otherIndex||processedPoints.has(otherIndex))return;const distance=Math.sqrt(Math.pow(point.Latitude-otherPoint.Latitude,2)+Math.pow(point.Longitude-otherPoint.Longitude,2));if(distance<0.5){zone.push(otherPoint);processedPoints.add(otherIndex);}});if(zone.length>=2){// Create a risk zone polygon\nconst bounds=zone.reduce((acc,p)=>{acc.minLat=Math.min(acc.minLat,p.Latitude);acc.maxLat=Math.max(acc.maxLat,p.Latitude);acc.minLng=Math.min(acc.minLng,p.Longitude);acc.maxLng=Math.max(acc.maxLng,p.Longitude);return acc;},{minLat:Infinity,maxLat:-Infinity,minLng:Infinity,maxLng:-Infinity});// Expand bounds slightly for better visualization\nconst padding=0.1;const polygon=[[bounds.minLat-padding,bounds.minLng-padding],[bounds.minLat-padding,bounds.maxLng+padding],[bounds.maxLat+padding,bounds.maxLng+padding],[bounds.maxLat+padding,bounds.minLng-padding]];riskZones.push({polygon,intensity:zone.length,points:zone});}processedPoints.add(index);});// Add risk zones to map\nriskZones.forEach((zone,index)=>{const intensity=Math.min(zone.intensity/5,1);// Normalize intensity\nconst polygon=L.polygon(zone.polygon,{color:\"rgba(255, 68, 68, \".concat(0.3+intensity*0.4,\")\"),fillColor:\"rgba(255, 107, 107, \".concat(0.2+intensity*0.3,\")\"),fillOpacity:0.3+intensity*0.4,weight:2,className:'risk-heat-zone'}).addTo(map);polygon.bindTooltip(\"High Risk Zone<br/>Risk Points: \".concat(zone.intensity),{permanent:false,direction:'center',className:'risk-zone-tooltip'});});return()=>{// Clean up polygons when component unmounts\nmap.eachLayer(layer=>{if(layer.options&&layer.options.className==='risk-heat-zone'){map.removeLayer(layer);}});};},[map,mapData,heatMapVisible]);return null;};// Risk clusters component\nconst RiskClusters=_ref4=>{let{mapData}=_ref4;const map=useMap();const theme=useTheme();// Group points by risk level and proximity\nuseEffect(()=>{if(!mapData||mapData.length===0)return;// Find clusters of high-risk areas\nconst highRiskPoints=mapData.filter(point=>point.Flood_Prediction===1);const clusters=[];// Simple clustering algorithm (this is a simplified version)\nhighRiskPoints.forEach(point=>{const lat=point.Latitude;const lng=point.Longitude;// Check if point is already in a cluster\nconst inCluster=clusters.some(cluster=>{return Math.abs(cluster.centerLat-lat)<1&&Math.abs(cluster.centerLng-lng)<1;});if(!inCluster&&highRiskPoints.filter(p=>Math.abs(p.Latitude-lat)<1&&Math.abs(p.Longitude-lng)<1).length>3){// Create a new cluster if there are at least 3 high-risk points in proximity\nclusters.push({centerLat:lat,centerLng:lng,count:highRiskPoints.filter(p=>Math.abs(p.Latitude-lat)<1&&Math.abs(p.Longitude-lng)<1).length});}});return()=>{// Cleanup if needed\n};},[mapData,map]);return null;// Visual representation is handled by the markers\n};// Risk mitigation measures component\nconst RiskMitigationMeasures=_ref5=>{let{riskLevel,riskFactors}=_ref5;const theme=useTheme();// Define mitigation measures based on risk factors\nconst getMitigationMeasures=()=>{const measures=[];// General measures based on risk level\nif(riskLevel==='high'){measures.push({title:'Evacuation Planning',description:'Develop and practice evacuation plans. Identify safe routes and emergency shelters.',icon:/*#__PURE__*/_jsx(HomeIcon,{sx:{color:theme.palette.error.main}})});measures.push({title:'Early Warning System',description:'Install flood early warning systems and stay updated with weather forecasts.',icon:/*#__PURE__*/_jsx(WarningIcon,{sx:{color:theme.palette.error.main}})});}// Specific measures based on risk factors\nriskFactors.forEach(factor=>{if(factor.factor==='Heavy Rainfall'&&factor.severity==='high'){measures.push({title:'Drainage Improvement',description:'Ensure proper drainage systems are in place and regularly maintained to handle heavy rainfall.',icon:/*#__PURE__*/_jsx(EngineeringIcon,{sx:{color:theme.palette.info.main}})});}if(factor.factor==='Low Elevation'&&factor.severity==='high'){measures.push({title:'Elevated Structures',description:'Consider raising the foundation of buildings or using stilts in flood-prone low-lying areas.',icon:/*#__PURE__*/_jsx(HomeIcon,{sx:{color:theme.palette.warning.main}})});}if(factor.factor==='High Water Level'&&factor.severity==='high'){measures.push({title:'Flood Barriers',description:'Install temporary or permanent flood barriers, sandbags, or flood walls to protect property.',icon:/*#__PURE__*/_jsx(WaterDropIcon,{sx:{color:theme.palette.error.main}})});}});// Add general measures if none specific were found\nif(measures.length===0){measures.push({title:'Regular Monitoring',description:'Monitor weather forecasts and water levels during monsoon season.',icon:/*#__PURE__*/_jsx(InfoIcon,{sx:{color:theme.palette.info.main}})});}return measures;};const measures=getMitigationMeasures();return/*#__PURE__*/_jsxs(Box,{sx:{mt:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",fontWeight:\"bold\",gutterBottom:true,children:\"Recommended Mitigation Measures:\"}),measures.map((measure,index)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',mb:1.5,p:1,borderRadius:1,backgroundColor:'rgba(0, 0, 0, 0.02)'},children:[/*#__PURE__*/_jsx(Box,{sx:{mr:1.5,mt:0.5},children:measure.icon}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",fontWeight:\"medium\",children:measure.title}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:measure.description})]})]},index))]});};// Get location name from coordinates\nconst useReverseGeocoding=()=>{const[locationCache,setLocationCache]=useState({});const getLocationName=useCallback(async(lat,lng)=>{// Check cache first\nconst cacheKey=\"\".concat(lat.toFixed(4),\",\").concat(lng.toFixed(4));if(locationCache[cacheKey]){return locationCache[cacheKey];}try{const response=await axios.get(\"https://nominatim.openstreetmap.org/reverse\",{params:{lat,lon:lng,format:'json',zoom:10,// Adjust zoom level for appropriate place name detail\naddressdetails:1},headers:{'Accept-Language':'en-US,en;q=0.9','User-Agent':'FloodRiskPredictionApp'}});if(response.data){// Extract the most relevant name from the response\nconst locationData=response.data;let placeName='';// Try to get the most specific name first\nif(locationData.address){placeName=locationData.address.village||locationData.address.town||locationData.address.city||locationData.address.county||locationData.address.state_district||locationData.address.state;}// If no specific name found, use the display name\nif(!placeName&&locationData.display_name){placeName=locationData.display_name.split(',')[0];}// If still no name, use coordinates\nif(!placeName){placeName=\"Location at \".concat(lat.toFixed(4),\", \").concat(lng.toFixed(4));}// Cache the result\nsetLocationCache(prev=>_objectSpread(_objectSpread({},prev),{},{[cacheKey]:{name:placeName,fullName:locationData.display_name||'',address:locationData.address||{}}}));return{name:placeName,fullName:locationData.display_name||'',address:locationData.address||{}};}}catch(error){console.error('Error in reverse geocoding:',error);}// Return a default if geocoding fails\nreturn{name:\"Location at \".concat(lat.toFixed(4),\", \").concat(lng.toFixed(4)),fullName:'',address:{}};},[locationCache]);return{getLocationName,locationCache};};// Enhanced popup content\nconst EnhancedPopup=_ref6=>{let{point,showMitigationMeasures=false,locationName=null}=_ref6;const theme=useTheme();const isHighRisk=point.Flood_Prediction===1;const{getLocationName,locationCache}=useReverseGeocoding();const[location,setLocation]=useState(null);const[loading,setLoading]=useState(!locationName);useEffect(()=>{if(locationName){setLocation(locationName);setLoading(false);return;}// Check if we already have this location in cache\nconst cacheKey=\"\".concat(point.Latitude.toFixed(4),\",\").concat(point.Longitude.toFixed(4));if(locationCache[cacheKey]){setLocation(locationCache[cacheKey]);setLoading(false);return;}// Fetch location name\nconst fetchLocationName=async()=>{setLoading(true);const result=await getLocationName(point.Latitude,point.Longitude);setLocation(result);setLoading(false);};fetchLocationName();},[point,locationName,getLocationName,locationCache]);// Determine risk factors\nconst riskFactors=[];if(point['Rainfall (mm)']>200){riskFactors.push({factor:'Heavy Rainfall',value:\"\".concat(point['Rainfall (mm)'],\" mm\"),icon:/*#__PURE__*/_jsx(ThunderstormIcon,{fontSize:\"small\",sx:{color:theme.palette.info.main}}),severity:'high'});}if(point['Elevation (m)']<100){riskFactors.push({factor:'Low Elevation',value:\"\".concat(point['Elevation (m)'],\" m\"),icon:/*#__PURE__*/_jsx(TerrainIcon,{fontSize:\"small\",sx:{color:theme.palette.warning.main}}),severity:point['Elevation (m)']<50?'high':'medium'});}if(point['Water Level (m)']>6){riskFactors.push({factor:'High Water Level',value:\"\".concat(point['Water Level (m)'],\" m\"),icon:/*#__PURE__*/_jsx(WaterDropIcon,{fontSize:\"small\",sx:{color:theme.palette.error.main}}),severity:point['Water Level (m)']>8?'high':'medium'});}return/*#__PURE__*/_jsxs(Box,{sx:{width:'100%',maxWidth:showMitigationMeasures?350:280,overflowX:'hidden'},className:\"popup-content\",children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",sx:{mb:0.5,color:isHighRisk?theme.palette.error.main:theme.palette.success.main,display:'flex',alignItems:'center',gap:1,position:'sticky',top:0,backgroundColor:'white',zIndex:10,py:0.5},children:[/*#__PURE__*/_jsx(WaterDropIcon,{fontSize:\"small\"}),isHighRisk?'High Flood Risk Area':'Low Flood Risk Area']}),loading?/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',my:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:16,height:16,borderRadius:'50%',border:'2px solid transparent',borderTopColor:theme.palette.primary.main,animation:'spin 1s linear infinite',mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Loading location...\"})]}):location?/*#__PURE__*/_jsxs(Box,{sx:{mb:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",fontWeight:\"medium\",children:location.name}),location.fullName&&/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{display:'block',mb:0.5},children:location.fullName})]}):null,/*#__PURE__*/_jsx(Divider,{sx:{mb:1.5}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:1,sx:{mb:1.5},children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Rainfall\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"medium\",children:[point['Rainfall (mm)'],\" mm\"]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Elevation\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"medium\",children:[point['Elevation (m)'],\" m\"]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Water Level\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"medium\",children:[point['Water Level (m)'],\" m\"]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",children:\"Coordinates\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",fontWeight:\"medium\",children:[point.Latitude.toFixed(2),\", \",point.Longitude.toFixed(2)]})]})]}),riskFactors.length>0&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"text.secondary\",sx:{display:'block',mb:0.5},children:\"Risk Factors:\"}),riskFactors.map((factor,idx)=>/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:0.5},children:[factor.icon,/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{ml:0.5},children:[factor.factor,\": \",/*#__PURE__*/_jsx(\"strong\",{children:factor.value})]})]},idx))]}),isHighRisk&&!showMitigationMeasures&&/*#__PURE__*/_jsxs(Box,{sx:{mt:1.5,p:1,backgroundColor:'rgba(255, 89, 94, 0.1)',borderRadius:1,borderLeft:\"3px solid \".concat(theme.palette.error.main)},children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{display:'block',fontWeight:'medium'},children:\"Recommendation:\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:\"This area requires flood mitigation measures and close monitoring during heavy rainfall.\"})]}),showMitigationMeasures&&/*#__PURE__*/_jsx(RiskMitigationMeasures,{riskLevel:isHighRisk?'high':'low',riskFactors:riskFactors})]});};const FloodMap=_ref7=>{let{mapData}=_ref7;const theme=useTheme();const[loading,setLoading]=useState(true);const[searchedLocation,setSearchedLocation]=useState(null);const[nearestPoint,setNearestPoint]=useState(null);const[showLocationInfo,setShowLocationInfo]=useState(false);const[mapStyle,setMapStyle]=useState('dark');// 'dark', 'satellite', 'terrain'\nconst[notification,setNotification]=useState({open:false,message:'',severity:'info'});useEffect(()=>{if(mapData&&mapData.length>0){// Add a small delay to simulate loading for better UX, but ensure it stops\nconst timer=setTimeout(()=>{setLoading(false);},1000);return()=>{clearTimeout(timer);// Ensure loading is set to false when component unmounts\nsetLoading(false);};}else{// If no data, still set loading to false after a short delay\nconst timer=setTimeout(()=>{setLoading(false);},500);return()=>clearTimeout(timer);}},[mapData]);// Find the nearest data point to a searched location\nconst findNearestPoint=useCallback(location=>{if(!mapData||mapData.length===0)return null;let nearest=null;let minDistance=Infinity;mapData.forEach(point=>{const distance=Math.sqrt(Math.pow(point.Latitude-location.lat,2)+Math.pow(point.Longitude-location.lng,2));if(distance<minDistance){minDistance=distance;nearest=point;}});// Only consider it a match if it's reasonably close (within ~50km)\nif(minDistance>0.5){setNotification({open:true,message:'No exact flood data for this location. Showing nearest available data point.',severity:'info'});}return nearest;},[mapData]);// Handle location selection from search\nconst handleLocationSelect=useCallback(location=>{setSearchedLocation(location);const nearest=findNearestPoint(location);setNearestPoint(nearest);setShowLocationInfo(true);},[findNearestPoint]);// Close notification\nconst handleCloseNotification=()=>{setNotification(prev=>_objectSpread(_objectSpread({},prev),{},{open:false}));};// Handle map style change\nconst handleMapStyleChange=newStyle=>{setMapStyle(newStyle);};if(!mapData||mapData.length===0){return/*#__PURE__*/_jsx(Box,{sx:{width:'100%',height:500,borderRadius:2},children:/*#__PURE__*/_jsx(Skeleton,{variant:\"rectangular\",width:\"100%\",height:\"100%\"})});}// Count high and low risk areas\nconst highRiskCount=mapData.filter(point=>point.Flood_Prediction===1).length;const lowRiskCount=mapData.filter(point=>point.Flood_Prediction===0).length;// Calculate statistics\nconst avgRainfall=mapData.reduce((sum,point)=>sum+point['Rainfall (mm)'],0)/mapData.length;const avgElevation=mapData.reduce((sum,point)=>sum+point['Elevation (m)'],0)/mapData.length;return/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(Box,{sx:{mb:3},children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:{xs:3,sm:4,md:5},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:{xs:2.5,sm:3},borderRadius:3,background:\"linear-gradient(135deg, \".concat(theme.palette.error.light,\"20 0%, \").concat(theme.palette.error.light,\"05 100%)\"),borderLeft:\"4px solid \".concat(theme.palette.error.main),height:'100%',display:'flex',flexDirection:'column',justifyContent:'center',transition:'all 0.3s ease','&:hover':{transform:'translateY(-2px)',boxShadow:'0 8px 24px rgba(0, 0, 0, 0.12)'}},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",sx:{mb:1},children:\"High Risk Areas\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",color:theme.palette.error.main,children:highRiskCount})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,borderRadius:2,background:\"linear-gradient(135deg, \".concat(theme.palette.success.light,\"20 0%, \").concat(theme.palette.success.light,\"05 100%)\"),borderLeft:\"4px solid \".concat(theme.palette.success.main)},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:\"Low Risk Areas\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h4\",fontWeight:\"bold\",color:theme.palette.success.main,children:lowRiskCount})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,borderRadius:2,background:\"linear-gradient(135deg, \".concat(theme.palette.info.light,\"20 0%, \").concat(theme.palette.info.light,\"05 100%)\"),borderLeft:\"4px solid \".concat(theme.palette.info.main)},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:\"Avg. Rainfall\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h4\",fontWeight:\"bold\",color:theme.palette.info.main,children:[avgRainfall.toFixed(1),\" \",/*#__PURE__*/_jsx(Typography,{component:\"span\",variant:\"body2\",children:\"mm\"})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,sm:6,md:3,children:/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,borderRadius:2,background:\"linear-gradient(135deg, \".concat(theme.palette.warning.light,\"20 0%, \").concat(theme.palette.warning.light,\"05 100%)\"),borderLeft:\"4px solid \".concat(theme.palette.warning.main)},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:\"Avg. Elevation\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h4\",fontWeight:\"bold\",color:theme.palette.warning.main,children:[avgElevation.toFixed(1),\" \",/*#__PURE__*/_jsx(Typography,{component:\"span\",variant:\"body2\",children:\"m\"})]})]})})]})}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',height:600,borderRadius:2,overflow:'hidden'},children:[loading&&/*#__PURE__*/_jsx(Fade,{in:loading,timeout:300,children:/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,right:0,bottom:0,display:'flex',alignItems:'center',justifyContent:'center',backgroundColor:'rgba(255,255,255,0.8)',zIndex:1000},children:/*#__PURE__*/_jsxs(Box,{sx:{textAlign:'center'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'relative',width:60,height:60,margin:'0 auto'},children:/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,width:'100%',height:'100%',borderRadius:'50%',border:'3px solid transparent',borderTopColor:theme.palette.primary.main,animation:'spin 1s linear infinite','@keyframes spin':{'0%':{transform:'rotate(0deg)'},'100%':{transform:'rotate(360deg)'}}}})}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",sx:{mt:2},children:\"Loading map data...\"})]})})}),/*#__PURE__*/_jsxs(MapContainer,{center:[22.0,80.0],zoom:5,scrollWheelZoom:true,style:{height:'100%',width:'100%',borderRadius:8},zoomControl:false,children:[mapStyle==='dark'&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(TileLayer,{attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors | \\xA9 <a href=\\\"https://carto.com/attributions\\\">CARTO</a>\",url:\"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\",subdomains:\"abcd\",maxZoom:19}),/*#__PURE__*/_jsx(TileLayer,{attribution:\"Imagery \\xA9 <a href=\\\"https://www.mapbox.com/\\\">Mapbox</a>\",url:\"https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw\",opacity:0.2,maxZoom:19})]}),mapStyle==='satellite'&&/*#__PURE__*/_jsx(TileLayer,{attribution:\"Imagery \\xA9 <a href=\\\"https://www.mapbox.com/\\\">Mapbox</a>\",url:\"https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw\",maxZoom:19}),mapStyle==='terrain'&&/*#__PURE__*/_jsx(TileLayer,{attribution:\"\\xA9 <a href=\\\"https://www.openstreetmap.org/copyright\\\">OpenStreetMap</a> contributors\",url:\"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\",maxZoom:17}),/*#__PURE__*/_jsx(LayerGroup,{children:mapData.filter(point=>point.Flood_Prediction===1).map((point,index)=>/*#__PURE__*/_jsx(CircleMarker,{center:[point.Latitude,point.Longitude],radius:8,pathOptions:{color:'#FF4444',fillColor:'#FF6B6B',fillOpacity:0.8,weight:3,opacity:1,className:'pulsing-marker high-risk-marker'},eventHandlers:{mouseover:e=>{e.target.setStyle({radius:12,fillOpacity:0.9,weight:4});},mouseout:e=>{e.target.setStyle({radius:8,fillOpacity:0.8,weight:3});}},children:/*#__PURE__*/_jsx(Popup,{minWidth:300,maxWidth:350,maxHeight:400,autoPan:true,autoPanPadding:[25,25],className:\"scrollable-popup enhanced-popup\",children:/*#__PURE__*/_jsx(EnhancedPopup,{point:point})})},\"high-\".concat(index)))}),/*#__PURE__*/_jsx(LayerGroup,{children:mapData.filter(point=>point.Flood_Prediction===0).map((point,index)=>/*#__PURE__*/_jsx(CircleMarker,{center:[point.Latitude,point.Longitude],radius:6,pathOptions:{color:'#00E676',fillColor:'#4CAF50',fillOpacity:0.7,weight:2,opacity:0.9,className:'low-risk-marker'},eventHandlers:{mouseover:e=>{e.target.setStyle({radius:9,fillOpacity:0.85,weight:3});},mouseout:e=>{e.target.setStyle({radius:6,fillOpacity:0.7,weight:2});}},children:/*#__PURE__*/_jsx(Popup,{minWidth:300,maxWidth:350,maxHeight:400,autoPan:true,autoPanPadding:[25,25],className:\"scrollable-popup enhanced-popup\",children:/*#__PURE__*/_jsx(EnhancedPopup,{point:point})})},\"low-\".concat(index)))}),searchedLocation&&nearestPoint&&/*#__PURE__*/_jsx(LayerGroup,{children:/*#__PURE__*/_jsx(Marker,{position:[searchedLocation.lat,searchedLocation.lng],icon:L.divIcon({className:'custom-div-icon search-location-marker',html:\"\\n                    <div style=\\\"\\n                      background: linear-gradient(45deg, #3A86FF, #06FFA5);\\n                      border: 3px solid white;\\n                      border-radius: 50%;\\n                      width: 16px;\\n                      height: 16px;\\n                      box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);\\n                      animation: pulse-search 2s infinite;\\n                    \\\"></div>\\n                  \",iconSize:[16,16],iconAnchor:[8,8]}),children:/*#__PURE__*/_jsx(Popup,{minWidth:300,maxWidth:350,maxHeight:400,autoPan:true,autoPanPadding:[30,30],className:\"scrollable-popup\",children:/*#__PURE__*/_jsxs(Box,{sx:{maxHeight:'380px',overflowY:'auto'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",gutterBottom:true,children:searchedLocation.name.split(',')[0]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",paragraph:true,children:searchedLocation.name}),/*#__PURE__*/_jsx(Divider,{sx:{my:1.5}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",paragraph:true,children:[\"Showing flood risk analysis for the nearest data point (\",(Math.sqrt(Math.pow(nearestPoint.Latitude-searchedLocation.lat,2)+Math.pow(nearestPoint.Longitude-searchedLocation.lng,2))*111).toFixed(1),\" km away).\"]}),/*#__PURE__*/_jsx(EnhancedPopup,{point:nearestPoint,showMitigationMeasures:true})]})})})}),/*#__PURE__*/_jsx(RiskHeatMap,{mapData:mapData}),/*#__PURE__*/_jsx(RiskClusters,{mapData:mapData}),/*#__PURE__*/_jsx(LocationSearch,{onLocationSelect:handleLocationSelect}),/*#__PURE__*/_jsx(MapStyleToggle,{mapStyle:mapStyle,onStyleChange:handleMapStyleChange}),/*#__PURE__*/_jsx(MapControls,{}),/*#__PURE__*/_jsx(MapLegend,{})]}),/*#__PURE__*/_jsx(Snackbar,{open:notification.open,autoHideDuration:6000,onClose:handleCloseNotification,anchorOrigin:{vertical:'bottom',horizontal:'center'},children:/*#__PURE__*/_jsx(Alert,{onClose:handleCloseNotification,severity:notification.severity,sx:{width:'100%'},children:notification.message})})]}),searchedLocation&&nearestPoint&&showLocationInfo&&/*#__PURE__*/_jsx(Box,{sx:{mt:3,mb:4},children:/*#__PURE__*/_jsxs(Paper,{elevation:3,sx:{p:3,borderRadius:2,background:\"linear-gradient(135deg, \".concat(theme.palette.primary.light,\"10 0%, \").concat(theme.palette.primary.light,\"01 100%)\"),position:'relative',overflow:'hidden'},children:[/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,right:0,width:'150px',height:'150px',background:'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',borderRadius:'0 0 0 100%',zIndex:0}}),/*#__PURE__*/_jsxs(Box,{sx:{position:'relative',zIndex:1},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h4\",component:\"h2\",gutterBottom:true,sx:{fontWeight:600,color:theme.palette.primary.main,display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(LocationOnIcon,{}),\" \",searchedLocation.name.split(',')[0]]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",paragraph:true,color:\"text.secondary\",children:searchedLocation.name}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,fontWeight:\"medium\",children:\"Flood Risk Assessment\"}),/*#__PURE__*/_jsxs(Box,{sx:{p:2,borderRadius:2,backgroundColor:nearestPoint.Flood_Prediction===1?'rgba(255, 89, 94, 0.1)':'rgba(6, 214, 160, 0.1)',borderLeft:\"4px solid \".concat(nearestPoint.Flood_Prediction===1?theme.palette.error.main:theme.palette.success.main),mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",sx:{color:nearestPoint.Flood_Prediction===1?theme.palette.error.main:theme.palette.success.main},children:nearestPoint.Flood_Prediction===1?'High Flood Risk Area':'Low Flood Risk Area'}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{mt:1},children:nearestPoint.Flood_Prediction===1?'This area has significant flood risk due to environmental and geographical factors. Residents should take precautionary measures.':'This area has minimal flood risk under normal conditions. However, it\\'s still important to stay informed during extreme weather events.'})]}),/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",gutterBottom:true,children:\"Environmental Factors:\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,sx:{mb:2},children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:1,sx:{p:1.5,textAlign:'center',borderTop:\"3px solid \".concat(theme.palette.info.main)},children:[/*#__PURE__*/_jsx(ThunderstormIcon,{sx:{color:theme.palette.info.main,mb:0.5}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Rainfall\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",fontWeight:\"medium\",children:[nearestPoint['Rainfall (mm)'],\" mm\"]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:1,sx:{p:1.5,textAlign:'center',borderTop:\"3px solid \".concat(theme.palette.warning.main)},children:[/*#__PURE__*/_jsx(TerrainIcon,{sx:{color:theme.palette.warning.main,mb:0.5}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Elevation\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",fontWeight:\"medium\",children:[nearestPoint['Elevation (m)'],\" m\"]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:1,sx:{p:1.5,textAlign:'center',borderTop:\"3px solid \".concat(theme.palette.error.main)},children:[/*#__PURE__*/_jsx(WaterDropIcon,{sx:{color:theme.palette.error.main,mb:0.5}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Water Level\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",fontWeight:\"medium\",children:[nearestPoint['Water Level (m)'],\" m\"]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:1,sx:{p:1.5,textAlign:'center',borderTop:\"3px solid \".concat(theme.palette.primary.main)},children:[/*#__PURE__*/_jsx(LocationOnIcon,{sx:{color:theme.palette.primary.main,mb:0.5}}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"text.secondary\",children:\"Distance\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",fontWeight:\"medium\",children:[(Math.sqrt(Math.pow(nearestPoint.Latitude-searchedLocation.lat,2)+Math.pow(nearestPoint.Longitude-searchedLocation.lng,2))*111).toFixed(1),\" km\"]})]})})]})]}),/*#__PURE__*/_jsxs(Grid,{item:true,xs:12,md:6,children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,fontWeight:\"medium\",children:\"Recommended Mitigation Measures\"}),/*#__PURE__*/_jsx(RiskMitigationMeasures,{riskLevel:nearestPoint.Flood_Prediction===1?'high':'low',riskFactors:[...(nearestPoint['Rainfall (mm)']>200?[{factor:'Heavy Rainfall',value:\"\".concat(nearestPoint['Rainfall (mm)'],\" mm\"),severity:'high'}]:[]),...(nearestPoint['Elevation (m)']<100?[{factor:'Low Elevation',value:\"\".concat(nearestPoint['Elevation (m)'],\" m\"),severity:nearestPoint['Elevation (m)']<50?'high':'medium'}]:[]),...(nearestPoint['Water Level (m)']>6?[{factor:'High Water Level',value:\"\".concat(nearestPoint['Water Level (m)'],\" m\"),severity:nearestPoint['Water Level (m)']>8?'high':'medium'}]:[])]}),/*#__PURE__*/_jsx(Box,{sx:{mt:3},children:/*#__PURE__*/_jsx(Button,{variant:\"outlined\",color:\"primary\",onClick:()=>setShowLocationInfo(false),startIcon:/*#__PURE__*/_jsx(CloseIcon,{}),sx:{mt:2},children:\"Close Location Analysis\"})})]})]})]})]})}),/*#__PURE__*/_jsxs(Box,{sx:{mt:3},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",gutterBottom:true,fontWeight:\"medium\",children:\"Flood Risk Analysis\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,borderRadius:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",color:theme.palette.error.main,gutterBottom:true,children:[\"High Risk Areas (\",highRiskCount,\")\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",paragraph:true,children:\"These areas show significant flood risk due to factors like heavy rainfall, low elevation, or high water levels. Residents in these areas should be prepared for potential flooding during monsoon seasons.\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Key characteristics:\"})}),/*#__PURE__*/_jsxs(\"ul\",{style:{paddingLeft:'20px',margin:'8px 0'},children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Average rainfall: \",(mapData.filter(point=>point.Flood_Prediction===1).reduce((sum,point)=>sum+point['Rainfall (mm)'],0)/(highRiskCount||1)).toFixed(1),\" mm\"]})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Average elevation: \",(mapData.filter(point=>point.Flood_Prediction===1).reduce((sum,point)=>sum+point['Elevation (m)'],0)/(highRiskCount||1)).toFixed(1),\" m\"]})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Average water level: \",(mapData.filter(point=>point.Flood_Prediction===1).reduce((sum,point)=>sum+point['Water Level (m)'],0)/(highRiskCount||1)).toFixed(1),\" m\"]})})]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsxs(Paper,{elevation:2,sx:{p:2,borderRadius:2},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"subtitle1\",fontWeight:\"bold\",color:theme.palette.success.main,gutterBottom:true,children:[\"Low Risk Areas (\",lowRiskCount,\")\"]}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",paragraph:true,children:\"These areas have minimal flood risk due to favorable geographical and environmental conditions. They typically feature higher elevations, moderate rainfall, or effective drainage systems.\"}),/*#__PURE__*/_jsx(Typography,{variant:\"body2\",children:/*#__PURE__*/_jsx(\"strong\",{children:\"Key characteristics:\"})}),/*#__PURE__*/_jsxs(\"ul\",{style:{paddingLeft:'20px',margin:'8px 0'},children:[/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Average rainfall: \",(mapData.filter(point=>point.Flood_Prediction===0).reduce((sum,point)=>sum+point['Rainfall (mm)'],0)/(lowRiskCount||1)).toFixed(1),\" mm\"]})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Average elevation: \",(mapData.filter(point=>point.Flood_Prediction===0).reduce((sum,point)=>sum+point['Elevation (m)'],0)/(lowRiskCount||1)).toFixed(1),\" m\"]})}),/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[\"Average water level: \",(mapData.filter(point=>point.Flood_Prediction===0).reduce((sum,point)=>sum+point['Water Level (m)'],0)/(lowRiskCount||1)).toFixed(1),\" m\"]})})]})]})})]})]})]});};export default FloodMap;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "MapContainer", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CircleMarker", "Popup", "useMap", "LayerGroup", "ZoomControl", "Rectangle", "<PERSON><PERSON>", "Polygon", "L", "Box", "Typography", "Skeleton", "Paper", "Chip", "Divider", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Grid", "useTheme", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "IconButton", "Fade", "TextField", "InputAdornment", "List", "ListItem", "ListItemText", "ListItemIcon", "<PERSON><PERSON>", "Collapse", "Snackbar", "ToggleButton", "ToggleButtonGroup", "WaterDropIcon", "TerrainIcon", "ThunderstormIcon", "InfoIcon", "SatelliteIcon", "DarkModeIcon", "MapIcon", "LayersIcon", "MyLocationIcon", "ZoomInIcon", "ZoomOutIcon", "SearchIcon", "LocationOnIcon", "CloseIcon", "WarningIcon", "EngineeringIcon", "HomeIcon", "useSpring", "animated", "axios", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MapLegend", "theme", "expanded", "setExpanded", "legendAnimation", "opacity", "height", "config", "tension", "friction", "div", "style", "_objectSpread", "position", "bottom", "right", "zIndex", "backgroundColor", "padding", "borderRadius", "boxShadow", "overflow", "width", "children", "sx", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "size", "onClick", "transition", "fontSize", "my", "mt", "palette", "error", "main", "mr", "success", "color", "info", "warning", "text", "secondary", "LocationSearch", "_ref", "onLocationSelect", "map", "searchQuery", "setSearch<PERSON>uery", "searchResults", "setSearchResults", "searching", "setSearching", "showResults", "setShowResults", "setError", "searchLocation", "query", "trim", "length", "searchTimeout", "response", "get", "params", "q", "concat", "format", "limit", "countrycodes", "addressdetails", "headers", "data", "err", "console", "setTimeout", "clearTimeout", "handleSearchChange", "e", "target", "value", "handleSearchSubmit", "preventDefault", "handleLocationClick", "location", "lat", "parseFloat", "lng", "lon", "flyTo", "duration", "name", "display_name", "type", "importance", "address", "top", "left", "max<PERSON><PERSON><PERSON>", "elevation", "p", "onSubmit", "fullWidth", "placeholder", "onChange", "InputProps", "startAdornment", "endAdornment", "border", "borderTopColor", "primary", "animation", "severity", "onClose", "in", "maxHeight", "result", "index", "_result$address", "_result$address2", "_result$address3", "_result$address4", "button", "action", "hover", "min<PERSON><PERSON><PERSON>", "city", "town", "village", "state", "split", "secondaryTypographyProps", "noWrap", "MapStyleToggle", "_ref2", "mapStyle", "onStyleChange", "<PERSON><PERSON>ilter", "exclusive", "event", "newStyle", "margin", "light", "transform", "dark", "title", "placement", "gap", "xs", "sm", "MapControls", "handleZoomIn", "zoomIn", "handleZoomOut", "zoomOut", "handleLocate", "locate", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON>", "flexDirection", "RiskHeatMap", "_ref3", "mapData", "heatMapVisible", "setHeatMapVisible", "highRiskPoints", "filter", "point", "Flood_Prediction", "riskZones", "processedPoints", "Set", "for<PERSON>ach", "has", "zone", "zoneCenter", "Latitude", "Longitude", "otherPoint", "otherIndex", "distance", "Math", "sqrt", "pow", "push", "add", "bounds", "reduce", "acc", "minLat", "min", "maxLat", "max", "minLng", "maxLng", "Infinity", "polygon", "intensity", "points", "fillColor", "fillOpacity", "weight", "className", "addTo", "bindTooltip", "permanent", "direction", "eachLayer", "layer", "options", "<PERSON><PERSON><PERSON>er", "RiskClusters", "_ref4", "clusters", "inCluster", "some", "cluster", "abs", "centerLat", "centerLng", "count", "RiskMitigationMeasures", "_ref5", "riskLevel", "riskFactors", "getMitigationMeasures", "measures", "description", "icon", "factor", "gutterBottom", "measure", "useReverseGeocoding", "locationCache", "setLocationCache", "getLocationName", "cache<PERSON>ey", "toFixed", "zoom", "locationData", "placeName", "county", "state_district", "prev", "fullName", "Enhan<PERSON><PERSON><PERSON><PERSON>", "_ref6", "showMitigationMeasures", "locationName", "isHighRisk", "setLocation", "loading", "setLoading", "fetchLocationName", "overflowX", "py", "container", "spacing", "item", "idx", "ml", "borderLeft", "FloodMap", "_ref7", "searchedLocation", "setSearchedLocation", "nearestPoint", "setNearestPoint", "showLocationInfo", "setShowLocationInfo", "setMapStyle", "notification", "setNotification", "open", "message", "timer", "findNearestPoint", "nearest", "minDistance", "handleLocationSelect", "handleCloseNotification", "handleMapStyleChange", "highRiskCount", "lowRiskCount", "avgRainfall", "sum", "avgElevation", "md", "background", "component", "timeout", "textAlign", "center", "scrollWheelZoom", "zoomControl", "attribution", "url", "subdomains", "radius", "pathOptions", "eventHandlers", "mouseover", "setStyle", "mouseout", "autoPan", "autoPanPadding", "divIcon", "html", "iconSize", "iconAnchor", "overflowY", "paragraph", "autoHideDuration", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal", "borderTop", "startIcon", "paddingLeft"], "sources": ["/Users/<USER>/Downloads/Flood/frontend/src/components/FloodMap.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  CircleMarker,\n  <PERSON>up,\n  useMap,\n  LayerGroup,\n  ZoomControl,\n  Rectangle,\n  Marker,\n  Polygon\n} from 'react-leaflet';\nimport L from 'leaflet';\nimport './FloodMap.css'; // Import custom CSS for map\nimport {\n  Box,\n  Typography,\n  Skeleton,\n  Paper,\n  Chip,\n  Divider,\n  Card,\n  CardContent,\n  Grid,\n  useTheme,\n  Button,\n  Tooltip,\n  IconButton,\n  Fade,\n  TextField,\n  InputAdornment,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemIcon,\n  Alert,\n  Collapse,\n  Snackbar,\n  ToggleButton,\n  ToggleButtonGroup\n} from '@mui/material';\nimport WaterDropIcon from '@mui/icons-material/WaterDrop';\nimport TerrainIcon from '@mui/icons-material/Terrain';\nimport ThunderstormIcon from '@mui/icons-material/Thunderstorm';\nimport InfoIcon from '@mui/icons-material/Info';\nimport SatelliteIcon from '@mui/icons-material/Satellite';\nimport DarkModeIcon from '@mui/icons-material/DarkMode';\nimport MapIcon from '@mui/icons-material/Map';\nimport LayersIcon from '@mui/icons-material/Layers';\nimport MyLocationIcon from '@mui/icons-material/MyLocation';\nimport ZoomInIcon from '@mui/icons-material/ZoomIn';\nimport ZoomOutIcon from '@mui/icons-material/ZoomOut';\nimport SearchIcon from '@mui/icons-material/Search';\nimport LocationOnIcon from '@mui/icons-material/LocationOn';\nimport CloseIcon from '@mui/icons-material/Close';\nimport WarningIcon from '@mui/icons-material/Warning';\nimport EngineeringIcon from '@mui/icons-material/Engineering';\nimport HomeIcon from '@mui/icons-material/Home';\nimport { useSpring, animated } from 'react-spring';\nimport axios from 'axios';\n\n// Legend component\nconst MapLegend = () => {\n  const theme = useTheme();\n  const [expanded, setExpanded] = useState(false);\n\n  const legendAnimation = useSpring({\n    opacity: expanded ? 1 : 0.9,\n    height: expanded ? 280 : 40,\n    config: { tension: 200, friction: 20 }\n  });\n\n  return (\n    <animated.div style={{\n      position: 'absolute',\n      bottom: 20,\n      right: 10,\n      zIndex: 1000,\n      backgroundColor: 'white',\n      padding: '10px 15px',\n      borderRadius: 8,\n      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',\n      overflow: 'hidden',\n      width: 220,\n      ...legendAnimation\n    }}>\n      <Box sx={{\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: expanded ? 1 : 0\n      }}>\n        <Typography variant=\"subtitle1\" fontWeight=\"bold\">\n          Map Legend\n        </Typography>\n        <IconButton\n          size=\"small\"\n          onClick={() => setExpanded(!expanded)}\n          sx={{\n            backgroundColor: expanded ? 'rgba(0,0,0,0.05)' : 'transparent',\n            transition: 'all 0.2s ease'\n          }}\n        >\n          <InfoIcon fontSize=\"small\" />\n        </IconButton>\n      </Box>\n\n      {expanded && (\n        <>\n          <Divider sx={{ my: 1 }} />\n\n          <Typography variant=\"subtitle2\" fontWeight=\"bold\" sx={{ mt: 1 }}>\n            Flood Risk Indicators\n          </Typography>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n            <Box sx={{\n              width: 16,\n              height: 16,\n              borderRadius: '50%',\n              backgroundColor: theme.palette.error.main,\n              mr: 1\n            }} />\n            <Typography variant=\"body2\">High Risk Area</Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>\n            <Box sx={{\n              width: 16,\n              height: 16,\n              borderRadius: '50%',\n              backgroundColor: theme.palette.success.main,\n              mr: 1\n            }} />\n            <Typography variant=\"body2\">Low Risk Area</Typography>\n          </Box>\n\n          <Typography variant=\"subtitle2\" fontWeight=\"bold\" sx={{ mt: 2 }}>\n            Risk Factors\n          </Typography>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>\n            <ThunderstormIcon fontSize=\"small\" sx={{ mr: 1, color: theme.palette.info.main }} />\n            <Typography variant=\"body2\">Heavy Rainfall (>200mm)</Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>\n            <TerrainIcon fontSize=\"small\" sx={{ mr: 1, color: theme.palette.warning.main }} />\n            <Typography variant=\"body2\">Low Elevation (&lt;100m)</Typography>\n          </Box>\n\n          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>\n            <WaterDropIcon fontSize=\"small\" sx={{ mr: 1, color: theme.palette.error.main }} />\n            <Typography variant=\"body2\">High Water Level (>8m)</Typography>\n          </Box>\n\n          <Typography variant=\"caption\" sx={{ display: 'block', mt: 2, color: theme.palette.text.secondary }}>\n            Click on markers for detailed information about specific locations.\n          </Typography>\n        </>\n      )}\n    </animated.div>\n  );\n};\n\n// Search component\nconst LocationSearch = ({ onLocationSelect }) => {\n  const map = useMap();\n  const theme = useTheme();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState([]);\n  const [searching, setSearching] = useState(false);\n  const [showResults, setShowResults] = useState(false);\n  const [error, setError] = useState(null);\n\n  // Geocoding API (using OpenStreetMap Nominatim)\n  const searchLocation = useCallback(async (query) => {\n    if (!query || query.trim().length < 3) return;\n\n    setSearching(true);\n    setError(null);\n\n    // Clear any existing timeout to prevent memory leaks\n    let searchTimeout;\n\n    try {\n      // Add \"India\" to the search query to focus on Indian locations\n      const response = await axios.get(`https://nominatim.openstreetmap.org/search`, {\n        params: {\n          q: `${query}, India`,\n          format: 'json',\n          limit: 5,\n          countrycodes: 'in', // Limit to India\n          addressdetails: 1\n        },\n        headers: {\n          'Accept-Language': 'en-US,en;q=0.9',\n          'User-Agent': 'FloodRiskPredictionApp'\n        }\n      });\n\n      if (response.data && response.data.length > 0) {\n        setSearchResults(response.data);\n        setShowResults(true);\n      } else {\n        setSearchResults([]);\n        setError('No locations found. Try a different search term.');\n      }\n    } catch (err) {\n      console.error('Error searching for location:', err);\n      setError('Error searching for location. Please try again.');\n    } finally {\n      // Ensure searching state is properly reset with a slight delay\n      // This prevents UI flicker if multiple searches happen quickly\n      searchTimeout = setTimeout(() => {\n        setSearching(false);\n      }, 300);\n    }\n\n    return () => {\n      if (searchTimeout) clearTimeout(searchTimeout);\n    };\n  }, []);\n\n  const handleSearchChange = (e) => {\n    setSearchQuery(e.target.value);\n    if (e.target.value.trim().length === 0) {\n      setShowResults(false);\n    }\n  };\n\n  const handleSearchSubmit = (e) => {\n    e.preventDefault();\n    searchLocation(searchQuery);\n  };\n\n  const handleLocationClick = (location) => {\n    const lat = parseFloat(location.lat);\n    const lng = parseFloat(location.lon);\n\n    // Fly to the location\n    map.flyTo([lat, lng], 12, {\n      duration: 1.5\n    });\n\n    // Pass the selected location to parent component\n    onLocationSelect({\n      lat,\n      lng,\n      name: location.display_name,\n      type: location.type,\n      importance: location.importance,\n      address: location.address\n    });\n\n    // Clear search results\n    setShowResults(false);\n  };\n\n  return (\n    <Box sx={{\n      position: 'absolute',\n      top: 10,\n      left: 10,\n      zIndex: 1000,\n      width: 300,\n      maxWidth: 'calc(100% - 20px)'\n    }}>\n      <Paper\n        elevation={3}\n        sx={{\n          p: 1.5,\n          borderRadius: 2,\n          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'\n        }}\n      >\n        <form onSubmit={handleSearchSubmit}>\n          <TextField\n            fullWidth\n            placeholder=\"Search for a place in India...\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            variant=\"outlined\"\n            size=\"small\"\n            InputProps={{\n              startAdornment: (\n                <InputAdornment position=\"start\">\n                  <SearchIcon color=\"action\" />\n                </InputAdornment>\n              ),\n              endAdornment: searching ? (\n                <InputAdornment position=\"end\">\n                  <Box\n                    sx={{\n                      width: 20,\n                      height: 20,\n                      borderRadius: '50%',\n                      border: '2px solid transparent',\n                      borderTopColor: theme.palette.primary.main,\n                      animation: 'spin 1s linear infinite',\n                    }}\n                  />\n                </InputAdornment>\n              ) : searchQuery ? (\n                <InputAdornment position=\"end\">\n                  <IconButton\n                    size=\"small\"\n                    onClick={() => {\n                      setSearchQuery('');\n                      setShowResults(false);\n                    }}\n                  >\n                    <CloseIcon fontSize=\"small\" />\n                  </IconButton>\n                </InputAdornment>\n              ) : null,\n              sx: {\n                borderRadius: 2,\n                '&.Mui-focused': {\n                  boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`\n                }\n              }\n            }}\n          />\n        </form>\n\n        {error && (\n          <Alert\n            severity=\"warning\"\n            sx={{ mt: 1, borderRadius: 1 }}\n            onClose={() => setError(null)}\n          >\n            {error}\n          </Alert>\n        )}\n\n        <Collapse in={showResults && searchResults.length > 0}>\n          <List sx={{ mt: 1, maxHeight: 300, overflow: 'auto' }}>\n            {searchResults.map((result, index) => (\n              <ListItem\n                key={index}\n                button\n                onClick={() => handleLocationClick(result)}\n                sx={{\n                  borderRadius: 1,\n                  mb: 0.5,\n                  '&:hover': {\n                    backgroundColor: theme.palette.action.hover\n                  }\n                }}\n              >\n                <ListItemIcon sx={{ minWidth: 36 }}>\n                  <LocationOnIcon color=\"primary\" />\n                </ListItemIcon>\n                <ListItemText\n                  primary={result.address?.city || result.address?.town || result.address?.village || result.address?.state || result.display_name.split(',')[0]}\n                  secondary={result.display_name}\n                  secondaryTypographyProps={{\n                    noWrap: true,\n                    style: { fontSize: '0.75rem' }\n                  }}\n                />\n              </ListItem>\n            ))}\n          </List>\n        </Collapse>\n      </Paper>\n    </Box>\n  );\n};\n\n// Map style toggle component\nconst MapStyleToggle = ({ mapStyle, onStyleChange }) => {\n  const theme = useTheme();\n\n  return (\n    <Box\n      sx={{\n        position: 'absolute',\n        top: 10,\n        left: 10,\n        zIndex: 1000,\n        backgroundColor: 'rgba(255, 255, 255, 0.95)',\n        backdropFilter: 'blur(10px)',\n        borderRadius: 2,\n        padding: 1,\n        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',\n        border: '1px solid rgba(255, 255, 255, 0.2)'\n      }}\n    >\n      <ToggleButtonGroup\n        value={mapStyle}\n        exclusive\n        onChange={(event, newStyle) => {\n          if (newStyle !== null) {\n            onStyleChange(newStyle);\n          }\n        }}\n        size=\"small\"\n        sx={{\n          '& .MuiToggleButton-root': {\n            border: 'none',\n            borderRadius: 1.5,\n            margin: 0.25,\n            padding: '6px 12px',\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              backgroundColor: theme.palette.primary.light + '20',\n              transform: 'scale(1.05)'\n            },\n            '&.Mui-selected': {\n              backgroundColor: theme.palette.primary.main,\n              color: 'white',\n              '&:hover': {\n                backgroundColor: theme.palette.primary.dark\n              }\n            }\n          }\n        }}\n      >\n        <ToggleButton value=\"dark\" aria-label=\"dark theme\">\n          <Tooltip title=\"Dark Theme\" placement=\"bottom\">\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n              <DarkModeIcon fontSize=\"small\" />\n              <Typography variant=\"caption\" sx={{ display: { xs: 'none', sm: 'block' } }}>\n                Dark\n              </Typography>\n            </Box>\n          </Tooltip>\n        </ToggleButton>\n\n        <ToggleButton value=\"satellite\" aria-label=\"satellite view\">\n          <Tooltip title=\"Satellite View\" placement=\"bottom\">\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n              <SatelliteIcon fontSize=\"small\" />\n              <Typography variant=\"caption\" sx={{ display: { xs: 'none', sm: 'block' } }}>\n                Satellite\n              </Typography>\n            </Box>\n          </Tooltip>\n        </ToggleButton>\n\n        <ToggleButton value=\"terrain\" aria-label=\"terrain view\">\n          <Tooltip title=\"Terrain View\" placement=\"bottom\">\n            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>\n              <TerrainIcon fontSize=\"small\" />\n              <Typography variant=\"caption\" sx={{ display: { xs: 'none', sm: 'block' } }}>\n                Terrain\n              </Typography>\n            </Box>\n          </Tooltip>\n        </ToggleButton>\n      </ToggleButtonGroup>\n    </Box>\n  );\n};\n\n// Map controls component\nconst MapControls = () => {\n  const map = useMap();\n  const theme = useTheme();\n\n  const handleZoomIn = () => {\n    map.zoomIn();\n  };\n\n  const handleZoomOut = () => {\n    map.zoomOut();\n  };\n\n  const handleLocate = () => {\n    map.locate({ setView: true, maxZoom: 10 });\n  };\n\n  return (\n    <Box sx={{\n      position: 'absolute',\n      top: 10,\n      right: 10,\n      zIndex: 1000,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: 1\n    }}>\n      <Tooltip title=\"Zoom In\" placement=\"left\">\n        <IconButton\n          onClick={handleZoomIn}\n          sx={{\n            backgroundColor: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n            border: '1px solid rgba(255, 255, 255, 0.2)',\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              backgroundColor: theme.palette.primary.main,\n              color: 'white',\n              transform: 'scale(1.1)'\n            }\n          }}\n        >\n          <ZoomInIcon />\n        </IconButton>\n      </Tooltip>\n\n      <Tooltip title=\"Zoom Out\" placement=\"left\">\n        <IconButton\n          onClick={handleZoomOut}\n          sx={{\n            backgroundColor: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n            border: '1px solid rgba(255, 255, 255, 0.2)',\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              backgroundColor: theme.palette.primary.main,\n              color: 'white',\n              transform: 'scale(1.1)'\n            }\n          }}\n        >\n          <ZoomOutIcon />\n        </IconButton>\n      </Tooltip>\n\n      <Tooltip title=\"My Location\" placement=\"left\">\n        <IconButton\n          onClick={handleLocate}\n          sx={{\n            backgroundColor: 'rgba(255, 255, 255, 0.95)',\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',\n            border: '1px solid rgba(255, 255, 255, 0.2)',\n            transition: 'all 0.3s ease',\n            '&:hover': {\n              backgroundColor: theme.palette.success.main,\n              color: 'white',\n              transform: 'scale(1.1)'\n            }\n          }}\n        >\n          <MyLocationIcon />\n        </IconButton>\n      </Tooltip>\n    </Box>\n  );\n};\n\n// Heat map overlay component for risk visualization\nconst RiskHeatMap = ({ mapData }) => {\n  const map = useMap();\n  const [heatMapVisible, setHeatMapVisible] = useState(true);\n\n  useEffect(() => {\n    if (!mapData || mapData.length === 0 || !heatMapVisible) return;\n\n    // Create heat map zones based on risk density\n    const highRiskPoints = mapData.filter(point => point.Flood_Prediction === 1);\n    const riskZones = [];\n\n    // Group nearby high-risk points into zones\n    const processedPoints = new Set();\n\n    highRiskPoints.forEach((point, index) => {\n      if (processedPoints.has(index)) return;\n\n      const zone = [point];\n      const zoneCenter = { lat: point.Latitude, lng: point.Longitude };\n\n      // Find nearby points within 0.5 degrees\n      highRiskPoints.forEach((otherPoint, otherIndex) => {\n        if (index === otherIndex || processedPoints.has(otherIndex)) return;\n\n        const distance = Math.sqrt(\n          Math.pow(point.Latitude - otherPoint.Latitude, 2) +\n          Math.pow(point.Longitude - otherPoint.Longitude, 2)\n        );\n\n        if (distance < 0.5) {\n          zone.push(otherPoint);\n          processedPoints.add(otherIndex);\n        }\n      });\n\n      if (zone.length >= 2) {\n        // Create a risk zone polygon\n        const bounds = zone.reduce((acc, p) => {\n          acc.minLat = Math.min(acc.minLat, p.Latitude);\n          acc.maxLat = Math.max(acc.maxLat, p.Latitude);\n          acc.minLng = Math.min(acc.minLng, p.Longitude);\n          acc.maxLng = Math.max(acc.maxLng, p.Longitude);\n          return acc;\n        }, {\n          minLat: Infinity,\n          maxLat: -Infinity,\n          minLng: Infinity,\n          maxLng: -Infinity\n        });\n\n        // Expand bounds slightly for better visualization\n        const padding = 0.1;\n        const polygon = [\n          [bounds.minLat - padding, bounds.minLng - padding],\n          [bounds.minLat - padding, bounds.maxLng + padding],\n          [bounds.maxLat + padding, bounds.maxLng + padding],\n          [bounds.maxLat + padding, bounds.minLng - padding]\n        ];\n\n        riskZones.push({\n          polygon,\n          intensity: zone.length,\n          points: zone\n        });\n      }\n\n      processedPoints.add(index);\n    });\n\n    // Add risk zones to map\n    riskZones.forEach((zone, index) => {\n      const intensity = Math.min(zone.intensity / 5, 1); // Normalize intensity\n      const polygon = L.polygon(zone.polygon, {\n        color: `rgba(255, 68, 68, ${0.3 + intensity * 0.4})`,\n        fillColor: `rgba(255, 107, 107, ${0.2 + intensity * 0.3})`,\n        fillOpacity: 0.3 + intensity * 0.4,\n        weight: 2,\n        className: 'risk-heat-zone'\n      }).addTo(map);\n\n      polygon.bindTooltip(`High Risk Zone<br/>Risk Points: ${zone.intensity}`, {\n        permanent: false,\n        direction: 'center',\n        className: 'risk-zone-tooltip'\n      });\n    });\n\n    return () => {\n      // Clean up polygons when component unmounts\n      map.eachLayer((layer) => {\n        if (layer.options && layer.options.className === 'risk-heat-zone') {\n          map.removeLayer(layer);\n        }\n      });\n    };\n  }, [map, mapData, heatMapVisible]);\n\n  return null;\n};\n\n// Risk clusters component\nconst RiskClusters = ({ mapData }) => {\n  const map = useMap();\n  const theme = useTheme();\n\n  // Group points by risk level and proximity\n  useEffect(() => {\n    if (!mapData || mapData.length === 0) return;\n\n    // Find clusters of high-risk areas\n    const highRiskPoints = mapData.filter(point => point.Flood_Prediction === 1);\n    const clusters = [];\n\n    // Simple clustering algorithm (this is a simplified version)\n    highRiskPoints.forEach(point => {\n      const lat = point.Latitude;\n      const lng = point.Longitude;\n\n      // Check if point is already in a cluster\n      const inCluster = clusters.some(cluster => {\n        return Math.abs(cluster.centerLat - lat) < 1 &&\n               Math.abs(cluster.centerLng - lng) < 1;\n      });\n\n      if (!inCluster && highRiskPoints.filter(p =>\n        Math.abs(p.Latitude - lat) < 1 &&\n        Math.abs(p.Longitude - lng) < 1\n      ).length > 3) {\n        // Create a new cluster if there are at least 3 high-risk points in proximity\n        clusters.push({\n          centerLat: lat,\n          centerLng: lng,\n          count: highRiskPoints.filter(p =>\n            Math.abs(p.Latitude - lat) < 1 &&\n            Math.abs(p.Longitude - lng) < 1\n          ).length\n        });\n      }\n    });\n\n    return () => {\n      // Cleanup if needed\n    };\n  }, [mapData, map]);\n\n  return null; // Visual representation is handled by the markers\n};\n\n// Risk mitigation measures component\nconst RiskMitigationMeasures = ({ riskLevel, riskFactors }) => {\n  const theme = useTheme();\n\n  // Define mitigation measures based on risk factors\n  const getMitigationMeasures = () => {\n    const measures = [];\n\n    // General measures based on risk level\n    if (riskLevel === 'high') {\n      measures.push({\n        title: 'Evacuation Planning',\n        description: 'Develop and practice evacuation plans. Identify safe routes and emergency shelters.',\n        icon: <HomeIcon sx={{ color: theme.palette.error.main }} />\n      });\n      measures.push({\n        title: 'Early Warning System',\n        description: 'Install flood early warning systems and stay updated with weather forecasts.',\n        icon: <WarningIcon sx={{ color: theme.palette.error.main }} />\n      });\n    }\n\n    // Specific measures based on risk factors\n    riskFactors.forEach(factor => {\n      if (factor.factor === 'Heavy Rainfall' && factor.severity === 'high') {\n        measures.push({\n          title: 'Drainage Improvement',\n          description: 'Ensure proper drainage systems are in place and regularly maintained to handle heavy rainfall.',\n          icon: <EngineeringIcon sx={{ color: theme.palette.info.main }} />\n        });\n      }\n\n      if (factor.factor === 'Low Elevation' && factor.severity === 'high') {\n        measures.push({\n          title: 'Elevated Structures',\n          description: 'Consider raising the foundation of buildings or using stilts in flood-prone low-lying areas.',\n          icon: <HomeIcon sx={{ color: theme.palette.warning.main }} />\n        });\n      }\n\n      if (factor.factor === 'High Water Level' && factor.severity === 'high') {\n        measures.push({\n          title: 'Flood Barriers',\n          description: 'Install temporary or permanent flood barriers, sandbags, or flood walls to protect property.',\n          icon: <WaterDropIcon sx={{ color: theme.palette.error.main }} />\n        });\n      }\n    });\n\n    // Add general measures if none specific were found\n    if (measures.length === 0) {\n      measures.push({\n        title: 'Regular Monitoring',\n        description: 'Monitor weather forecasts and water levels during monsoon season.',\n        icon: <InfoIcon sx={{ color: theme.palette.info.main }} />\n      });\n    }\n\n    return measures;\n  };\n\n  const measures = getMitigationMeasures();\n\n  return (\n    <Box sx={{ mt: 2 }}>\n      <Typography variant=\"subtitle2\" fontWeight=\"bold\" gutterBottom>\n        Recommended Mitigation Measures:\n      </Typography>\n\n      {measures.map((measure, index) => (\n        <Box\n          key={index}\n          sx={{\n            display: 'flex',\n            mb: 1.5,\n            p: 1,\n            borderRadius: 1,\n            backgroundColor: 'rgba(0, 0, 0, 0.02)'\n          }}\n        >\n          <Box sx={{ mr: 1.5, mt: 0.5 }}>\n            {measure.icon}\n          </Box>\n          <Box>\n            <Typography variant=\"body2\" fontWeight=\"medium\">\n              {measure.title}\n            </Typography>\n            <Typography variant=\"caption\" color=\"text.secondary\">\n              {measure.description}\n            </Typography>\n          </Box>\n        </Box>\n      ))}\n    </Box>\n  );\n};\n\n// Get location name from coordinates\nconst useReverseGeocoding = () => {\n  const [locationCache, setLocationCache] = useState({});\n\n  const getLocationName = useCallback(async (lat, lng) => {\n    // Check cache first\n    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;\n    if (locationCache[cacheKey]) {\n      return locationCache[cacheKey];\n    }\n\n    try {\n      const response = await axios.get(`https://nominatim.openstreetmap.org/reverse`, {\n        params: {\n          lat,\n          lon: lng,\n          format: 'json',\n          zoom: 10, // Adjust zoom level for appropriate place name detail\n          addressdetails: 1\n        },\n        headers: {\n          'Accept-Language': 'en-US,en;q=0.9',\n          'User-Agent': 'FloodRiskPredictionApp'\n        }\n      });\n\n      if (response.data) {\n        // Extract the most relevant name from the response\n        const locationData = response.data;\n        let placeName = '';\n\n        // Try to get the most specific name first\n        if (locationData.address) {\n          placeName = locationData.address.village ||\n                     locationData.address.town ||\n                     locationData.address.city ||\n                     locationData.address.county ||\n                     locationData.address.state_district ||\n                     locationData.address.state;\n        }\n\n        // If no specific name found, use the display name\n        if (!placeName && locationData.display_name) {\n          placeName = locationData.display_name.split(',')[0];\n        }\n\n        // If still no name, use coordinates\n        if (!placeName) {\n          placeName = `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`;\n        }\n\n        // Cache the result\n        setLocationCache(prev => ({\n          ...prev,\n          [cacheKey]: {\n            name: placeName,\n            fullName: locationData.display_name || '',\n            address: locationData.address || {}\n          }\n        }));\n\n        return {\n          name: placeName,\n          fullName: locationData.display_name || '',\n          address: locationData.address || {}\n        };\n      }\n    } catch (error) {\n      console.error('Error in reverse geocoding:', error);\n    }\n\n    // Return a default if geocoding fails\n    return {\n      name: `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`,\n      fullName: '',\n      address: {}\n    };\n  }, [locationCache]);\n\n  return { getLocationName, locationCache };\n};\n\n// Enhanced popup content\nconst EnhancedPopup = ({ point, showMitigationMeasures = false, locationName = null }) => {\n  const theme = useTheme();\n  const isHighRisk = point.Flood_Prediction === 1;\n  const { getLocationName, locationCache } = useReverseGeocoding();\n  const [location, setLocation] = useState(null);\n  const [loading, setLoading] = useState(!locationName);\n\n  useEffect(() => {\n    if (locationName) {\n      setLocation(locationName);\n      setLoading(false);\n      return;\n    }\n\n    // Check if we already have this location in cache\n    const cacheKey = `${point.Latitude.toFixed(4)},${point.Longitude.toFixed(4)}`;\n    if (locationCache[cacheKey]) {\n      setLocation(locationCache[cacheKey]);\n      setLoading(false);\n      return;\n    }\n\n    // Fetch location name\n    const fetchLocationName = async () => {\n      setLoading(true);\n      const result = await getLocationName(point.Latitude, point.Longitude);\n      setLocation(result);\n      setLoading(false);\n    };\n\n    fetchLocationName();\n  }, [point, locationName, getLocationName, locationCache]);\n\n  // Determine risk factors\n  const riskFactors = [];\n  if (point['Rainfall (mm)'] > 200) {\n    riskFactors.push({\n      factor: 'Heavy Rainfall',\n      value: `${point['Rainfall (mm)']} mm`,\n      icon: <ThunderstormIcon fontSize=\"small\" sx={{ color: theme.palette.info.main }} />,\n      severity: 'high'\n    });\n  }\n\n  if (point['Elevation (m)'] < 100) {\n    riskFactors.push({\n      factor: 'Low Elevation',\n      value: `${point['Elevation (m)']} m`,\n      icon: <TerrainIcon fontSize=\"small\" sx={{ color: theme.palette.warning.main }} />,\n      severity: point['Elevation (m)'] < 50 ? 'high' : 'medium'\n    });\n  }\n\n  if (point['Water Level (m)'] > 6) {\n    riskFactors.push({\n      factor: 'High Water Level',\n      value: `${point['Water Level (m)']} m`,\n      icon: <WaterDropIcon fontSize=\"small\" sx={{ color: theme.palette.error.main }} />,\n      severity: point['Water Level (m)'] > 8 ? 'high' : 'medium'\n    });\n  }\n\n  return (\n    <Box\n      sx={{\n        width: '100%',\n        maxWidth: showMitigationMeasures ? 350 : 280,\n        overflowX: 'hidden'\n      }}\n      className=\"popup-content\"\n    >\n      <Typography\n        variant=\"subtitle1\"\n        fontWeight=\"bold\"\n        sx={{\n          mb: 0.5,\n          color: isHighRisk ? theme.palette.error.main : theme.palette.success.main,\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1,\n          position: 'sticky',\n          top: 0,\n          backgroundColor: 'white',\n          zIndex: 10,\n          py: 0.5\n        }}\n      >\n        <WaterDropIcon fontSize=\"small\" />\n        {isHighRisk ? 'High Flood Risk Area' : 'Low Flood Risk Area'}\n      </Typography>\n\n      {loading ? (\n        <Box sx={{ display: 'flex', alignItems: 'center', my: 1 }}>\n          <Box\n            sx={{\n              width: 16,\n              height: 16,\n              borderRadius: '50%',\n              border: '2px solid transparent',\n              borderTopColor: theme.palette.primary.main,\n              animation: 'spin 1s linear infinite',\n              mr: 1\n            }}\n          />\n          <Typography variant=\"body2\" color=\"text.secondary\">\n            Loading location...\n          </Typography>\n        </Box>\n      ) : location ? (\n        <Box sx={{ mb: 1 }}>\n          <Typography variant=\"subtitle2\" fontWeight=\"medium\">\n            {location.name}\n          </Typography>\n          {location.fullName && (\n            <Typography variant=\"caption\" color=\"text.secondary\" sx={{ display: 'block', mb: 0.5 }}>\n              {location.fullName}\n            </Typography>\n          )}\n        </Box>\n      ) : null}\n\n      <Divider sx={{ mb: 1.5 }} />\n\n      <Grid container spacing={1} sx={{ mb: 1.5 }}>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Rainfall\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point['Rainfall (mm)']} mm\n          </Typography>\n        </Grid>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Elevation\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point['Elevation (m)']} m\n          </Typography>\n        </Grid>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Water Level\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point['Water Level (m)']} m\n          </Typography>\n        </Grid>\n        <Grid item xs={6}>\n          <Typography variant=\"caption\" color=\"text.secondary\">\n            Coordinates\n          </Typography>\n          <Typography variant=\"body2\" fontWeight=\"medium\">\n            {point.Latitude.toFixed(2)}, {point.Longitude.toFixed(2)}\n          </Typography>\n        </Grid>\n      </Grid>\n\n      {riskFactors.length > 0 && (\n        <>\n          <Typography variant=\"caption\" color=\"text.secondary\" sx={{ display: 'block', mb: 0.5 }}>\n            Risk Factors:\n          </Typography>\n\n          {riskFactors.map((factor, idx) => (\n            <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>\n              {factor.icon}\n              <Typography variant=\"body2\" sx={{ ml: 0.5 }}>\n                {factor.factor}: <strong>{factor.value}</strong>\n              </Typography>\n            </Box>\n          ))}\n        </>\n      )}\n\n      {isHighRisk && !showMitigationMeasures && (\n        <Box sx={{\n          mt: 1.5,\n          p: 1,\n          backgroundColor: 'rgba(255, 89, 94, 0.1)',\n          borderRadius: 1,\n          borderLeft: `3px solid ${theme.palette.error.main}`\n        }}>\n          <Typography variant=\"caption\" sx={{ display: 'block', fontWeight: 'medium' }}>\n            Recommendation:\n          </Typography>\n          <Typography variant=\"body2\">\n            This area requires flood mitigation measures and close monitoring during heavy rainfall.\n          </Typography>\n        </Box>\n      )}\n\n      {showMitigationMeasures && (\n        <RiskMitigationMeasures\n          riskLevel={isHighRisk ? 'high' : 'low'}\n          riskFactors={riskFactors}\n        />\n      )}\n    </Box>\n  );\n};\n\nconst FloodMap = ({ mapData }) => {\n  const theme = useTheme();\n  const [loading, setLoading] = useState(true);\n  const [searchedLocation, setSearchedLocation] = useState(null);\n  const [nearestPoint, setNearestPoint] = useState(null);\n  const [showLocationInfo, setShowLocationInfo] = useState(false);\n  const [mapStyle, setMapStyle] = useState('dark'); // 'dark', 'satellite', 'terrain'\n  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });\n\n  useEffect(() => {\n    if (mapData && mapData.length > 0) {\n      // Add a small delay to simulate loading for better UX, but ensure it stops\n      const timer = setTimeout(() => {\n        setLoading(false);\n      }, 1000);\n\n      return () => {\n        clearTimeout(timer);\n        // Ensure loading is set to false when component unmounts\n        setLoading(false);\n      };\n    } else {\n      // If no data, still set loading to false after a short delay\n      const timer = setTimeout(() => {\n        setLoading(false);\n      }, 500);\n\n      return () => clearTimeout(timer);\n    }\n  }, [mapData]);\n\n  // Find the nearest data point to a searched location\n  const findNearestPoint = useCallback((location) => {\n    if (!mapData || mapData.length === 0) return null;\n\n    let nearest = null;\n    let minDistance = Infinity;\n\n    mapData.forEach(point => {\n      const distance = Math.sqrt(\n        Math.pow(point.Latitude - location.lat, 2) +\n        Math.pow(point.Longitude - location.lng, 2)\n      );\n\n      if (distance < minDistance) {\n        minDistance = distance;\n        nearest = point;\n      }\n    });\n\n    // Only consider it a match if it's reasonably close (within ~50km)\n    if (minDistance > 0.5) {\n      setNotification({\n        open: true,\n        message: 'No exact flood data for this location. Showing nearest available data point.',\n        severity: 'info'\n      });\n    }\n\n    return nearest;\n  }, [mapData]);\n\n  // Handle location selection from search\n  const handleLocationSelect = useCallback((location) => {\n    setSearchedLocation(location);\n    const nearest = findNearestPoint(location);\n    setNearestPoint(nearest);\n    setShowLocationInfo(true);\n  }, [findNearestPoint]);\n\n  // Close notification\n  const handleCloseNotification = () => {\n    setNotification(prev => ({ ...prev, open: false }));\n  };\n\n  // Handle map style change\n  const handleMapStyleChange = (newStyle) => {\n    setMapStyle(newStyle);\n  };\n\n  if (!mapData || mapData.length === 0) {\n    return (\n      <Box sx={{ width: '100%', height: 500, borderRadius: 2 }}>\n        <Skeleton variant=\"rectangular\" width=\"100%\" height=\"100%\" />\n      </Box>\n    );\n  }\n\n  // Count high and low risk areas\n  const highRiskCount = mapData.filter(point => point.Flood_Prediction === 1).length;\n  const lowRiskCount = mapData.filter(point => point.Flood_Prediction === 0).length;\n\n  // Calculate statistics\n  const avgRainfall = mapData.reduce((sum, point) => sum + point['Rainfall (mm)'], 0) / mapData.length;\n  const avgElevation = mapData.reduce((sum, point) => sum + point['Elevation (m)'], 0) / mapData.length;\n\n  return (\n    <>\n      <Box sx={{ mb: 3 }}>\n        <Grid container spacing={{ xs: 3, sm: 4, md: 5 }}>\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={3}\n              sx={{\n                p: { xs: 2.5, sm: 3 },\n                borderRadius: 3,\n                background: `linear-gradient(135deg, ${theme.palette.error.light}20 0%, ${theme.palette.error.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.error.main}`,\n                height: '100%',\n                display: 'flex',\n                flexDirection: 'column',\n                justifyContent: 'center',\n                transition: 'all 0.3s ease',\n                '&:hover': {\n                  transform: 'translateY(-2px)',\n                  boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)'\n                }\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\" sx={{ mb: 1 }}>\n                High Risk Areas\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.error.main}>\n                {highRiskCount}\n              </Typography>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.success.light}20 0%, ${theme.palette.success.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.success.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                Low Risk Areas\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.success.main}>\n                {lowRiskCount}\n              </Typography>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.info.light}20 0%, ${theme.palette.info.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.info.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                Avg. Rainfall\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.info.main}>\n                {avgRainfall.toFixed(1)} <Typography component=\"span\" variant=\"body2\">mm</Typography>\n              </Typography>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} sm={6} md={3}>\n            <Paper\n              elevation={2}\n              sx={{\n                p: 2,\n                borderRadius: 2,\n                background: `linear-gradient(135deg, ${theme.palette.warning.light}20 0%, ${theme.palette.warning.light}05 100%)`,\n                borderLeft: `4px solid ${theme.palette.warning.main}`\n              }}\n            >\n              <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                Avg. Elevation\n              </Typography>\n              <Typography variant=\"h4\" fontWeight=\"bold\" color={theme.palette.warning.main}>\n                {avgElevation.toFixed(1)} <Typography component=\"span\" variant=\"body2\">m</Typography>\n              </Typography>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n\n      <Box sx={{ position: 'relative', height: 600, borderRadius: 2, overflow: 'hidden' }}>\n        {loading && (\n          <Fade in={loading} timeout={300}>\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                backgroundColor: 'rgba(255,255,255,0.8)',\n                zIndex: 1000\n              }}\n            >\n              <Box sx={{ textAlign: 'center' }}>\n                <Box sx={{ position: 'relative', width: 60, height: 60, margin: '0 auto' }}>\n                  <Box\n                    sx={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      width: '100%',\n                      height: '100%',\n                      borderRadius: '50%',\n                      border: '3px solid transparent',\n                      borderTopColor: theme.palette.primary.main,\n                      animation: 'spin 1s linear infinite',\n                      '@keyframes spin': {\n                        '0%': { transform: 'rotate(0deg)' },\n                        '100%': { transform: 'rotate(360deg)' }\n                      }\n                    }}\n                  />\n                </Box>\n                <Typography variant=\"body1\" sx={{ mt: 2 }}>\n                  Loading map data...\n                </Typography>\n              </Box>\n            </Box>\n          </Fade>\n        )}\n\n        <MapContainer\n          center={[22.0, 80.0]}\n          zoom={5}\n          scrollWheelZoom={true}\n          style={{ height: '100%', width: '100%', borderRadius: 8 }}\n          zoomControl={false}\n        >\n          {/* Dynamic tile layers based on map style */}\n          {mapStyle === 'dark' && (\n            <>\n              <TileLayer\n                attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors | &copy; <a href=\"https://carto.com/attributions\">CARTO</a>'\n                url=\"https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png\"\n                subdomains=\"abcd\"\n                maxZoom={19}\n              />\n              <TileLayer\n                attribution='Imagery &copy; <a href=\"https://www.mapbox.com/\">Mapbox</a>'\n                url=\"https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw\"\n                opacity={0.2}\n                maxZoom={19}\n              />\n            </>\n          )}\n\n          {mapStyle === 'satellite' && (\n            <TileLayer\n              attribution='Imagery &copy; <a href=\"https://www.mapbox.com/\">Mapbox</a>'\n              url=\"https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw\"\n              maxZoom={19}\n            />\n          )}\n\n          {mapStyle === 'terrain' && (\n            <TileLayer\n              attribution='&copy; <a href=\"https://www.openstreetmap.org/copyright\">OpenStreetMap</a> contributors'\n              url=\"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png\"\n              maxZoom={17}\n            />\n          )}\n\n          {/* High risk markers with enhanced visual effects */}\n          <LayerGroup>\n            {mapData\n              .filter(point => point.Flood_Prediction === 1)\n              .map((point, index) => (\n                <CircleMarker\n                  key={`high-${index}`}\n                  center={[point.Latitude, point.Longitude]}\n                  radius={8}\n                  pathOptions={{\n                    color: '#FF4444',\n                    fillColor: '#FF6B6B',\n                    fillOpacity: 0.8,\n                    weight: 3,\n                    opacity: 1,\n                    className: 'pulsing-marker high-risk-marker'\n                  }}\n                  eventHandlers={{\n                    mouseover: (e) => {\n                      e.target.setStyle({\n                        radius: 12,\n                        fillOpacity: 0.9,\n                        weight: 4\n                      });\n                    },\n                    mouseout: (e) => {\n                      e.target.setStyle({\n                        radius: 8,\n                        fillOpacity: 0.8,\n                        weight: 3\n                      });\n                    }\n                  }}\n                >\n                  <Popup\n                    minWidth={300}\n                    maxWidth={350}\n                    maxHeight={400}\n                    autoPan={true}\n                    autoPanPadding={[25, 25]}\n                    className=\"scrollable-popup enhanced-popup\"\n                  >\n                    <EnhancedPopup point={point} />\n                  </Popup>\n                </CircleMarker>\n              ))}\n          </LayerGroup>\n\n          {/* Low risk markers with enhanced visual effects */}\n          <LayerGroup>\n            {mapData\n              .filter(point => point.Flood_Prediction === 0)\n              .map((point, index) => (\n                <CircleMarker\n                  key={`low-${index}`}\n                  center={[point.Latitude, point.Longitude]}\n                  radius={6}\n                  pathOptions={{\n                    color: '#00E676',\n                    fillColor: '#4CAF50',\n                    fillOpacity: 0.7,\n                    weight: 2,\n                    opacity: 0.9,\n                    className: 'low-risk-marker'\n                  }}\n                  eventHandlers={{\n                    mouseover: (e) => {\n                      e.target.setStyle({\n                        radius: 9,\n                        fillOpacity: 0.85,\n                        weight: 3\n                      });\n                    },\n                    mouseout: (e) => {\n                      e.target.setStyle({\n                        radius: 6,\n                        fillOpacity: 0.7,\n                        weight: 2\n                      });\n                    }\n                  }}\n                >\n                  <Popup\n                    minWidth={300}\n                    maxWidth={350}\n                    maxHeight={400}\n                    autoPan={true}\n                    autoPanPadding={[25, 25]}\n                    className=\"scrollable-popup enhanced-popup\"\n                  >\n                    <EnhancedPopup point={point} />\n                  </Popup>\n                </CircleMarker>\n              ))}\n          </LayerGroup>\n\n          {/* Searched location marker */}\n          {searchedLocation && nearestPoint && (\n            <LayerGroup>\n              {/* Marker for the exact searched location */}\n              <Marker\n                position={[searchedLocation.lat, searchedLocation.lng]}\n                icon={L.divIcon({\n                  className: 'custom-div-icon search-location-marker',\n                  html: `\n                    <div style=\"\n                      background: linear-gradient(45deg, #3A86FF, #06FFA5);\n                      border: 3px solid white;\n                      border-radius: 50%;\n                      width: 16px;\n                      height: 16px;\n                      box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);\n                      animation: pulse-search 2s infinite;\n                    \"></div>\n                  `,\n                  iconSize: [16, 16],\n                  iconAnchor: [8, 8]\n                })}\n              >\n                <Popup\n                  minWidth={300}\n                  maxWidth={350}\n                  maxHeight={400}\n                  autoPan={true}\n                  autoPanPadding={[30, 30]}\n                  className=\"scrollable-popup\"\n                >\n                  <Box sx={{ maxHeight: '380px', overflowY: 'auto' }}>\n                    <Typography variant=\"subtitle1\" fontWeight=\"bold\" gutterBottom>\n                      {searchedLocation.name.split(',')[0]}\n                    </Typography>\n                    <Typography variant=\"body2\" color=\"text.secondary\" paragraph>\n                      {searchedLocation.name}\n                    </Typography>\n                    <Divider sx={{ my: 1.5 }} />\n                    <Typography variant=\"body2\" paragraph>\n                      Showing flood risk analysis for the nearest data point ({(Math.sqrt(\n                        Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) +\n                        Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)\n                      ) * 111).toFixed(1)} km away).\n                    </Typography>\n                    <EnhancedPopup point={nearestPoint} showMitigationMeasures={true} />\n                  </Box>\n                </Popup>\n              </Marker>\n\n              {/* Line connecting to the nearest data point */}\n              {/* We would need to use a Polyline here, but for simplicity we'll skip it */}\n            </LayerGroup>\n          )}\n\n          {/* Add risk heat map overlay */}\n          <RiskHeatMap mapData={mapData} />\n\n          {/* Add risk clusters */}\n          <RiskClusters mapData={mapData} />\n\n          {/* Add search component */}\n          <LocationSearch onLocationSelect={handleLocationSelect} />\n\n          {/* Add map style toggle */}\n          <MapStyleToggle mapStyle={mapStyle} onStyleChange={handleMapStyleChange} />\n\n          {/* Add map controls */}\n          <MapControls />\n\n          {/* Add legend */}\n          <MapLegend />\n        </MapContainer>\n\n        {/* Notification for search results */}\n        <Snackbar\n          open={notification.open}\n          autoHideDuration={6000}\n          onClose={handleCloseNotification}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}\n        >\n          <Alert\n            onClose={handleCloseNotification}\n            severity={notification.severity}\n            sx={{ width: '100%' }}\n          >\n            {notification.message}\n          </Alert>\n        </Snackbar>\n      </Box>\n\n      {/* Searched Location Analysis */}\n      {searchedLocation && nearestPoint && showLocationInfo && (\n        <Box sx={{ mt: 3, mb: 4 }}>\n          <Paper\n            elevation={3}\n            sx={{\n              p: 3,\n              borderRadius: 2,\n              background: `linear-gradient(135deg, ${theme.palette.primary.light}10 0%, ${theme.palette.primary.light}01 100%)`,\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n          >\n            <Box\n              sx={{\n                position: 'absolute',\n                top: 0,\n                right: 0,\n                width: '150px',\n                height: '150px',\n                background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',\n                borderRadius: '0 0 0 100%',\n                zIndex: 0\n              }}\n            />\n\n            <Box sx={{ position: 'relative', zIndex: 1 }}>\n              <Typography\n                variant=\"h4\"\n                component=\"h2\"\n                gutterBottom\n                sx={{\n                  fontWeight: 600,\n                  color: theme.palette.primary.main,\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: 1\n                }}\n              >\n                <LocationOnIcon /> {searchedLocation.name.split(',')[0]}\n              </Typography>\n\n              <Typography variant=\"body1\" paragraph color=\"text.secondary\">\n                {searchedLocation.name}\n              </Typography>\n\n              <Divider sx={{ my: 2 }} />\n\n              <Grid container spacing={3}>\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom fontWeight=\"medium\">\n                    Flood Risk Assessment\n                  </Typography>\n\n                  <Box\n                    sx={{\n                      p: 2,\n                      borderRadius: 2,\n                      backgroundColor: nearestPoint.Flood_Prediction === 1\n                        ? 'rgba(255, 89, 94, 0.1)'\n                        : 'rgba(6, 214, 160, 0.1)',\n                      borderLeft: `4px solid ${nearestPoint.Flood_Prediction === 1\n                        ? theme.palette.error.main\n                        : theme.palette.success.main}`,\n                      mb: 2\n                    }}\n                  >\n                    <Typography\n                      variant=\"subtitle1\"\n                      fontWeight=\"bold\"\n                      sx={{\n                        color: nearestPoint.Flood_Prediction === 1\n                          ? theme.palette.error.main\n                          : theme.palette.success.main\n                      }}\n                    >\n                      {nearestPoint.Flood_Prediction === 1\n                        ? 'High Flood Risk Area'\n                        : 'Low Flood Risk Area'}\n                    </Typography>\n\n                    <Typography variant=\"body2\" sx={{ mt: 1 }}>\n                      {nearestPoint.Flood_Prediction === 1\n                        ? 'This area has significant flood risk due to environmental and geographical factors. Residents should take precautionary measures.'\n                        : 'This area has minimal flood risk under normal conditions. However, it\\'s still important to stay informed during extreme weather events.'}\n                    </Typography>\n                  </Box>\n\n                  <Typography variant=\"subtitle2\" gutterBottom>\n                    Environmental Factors:\n                  </Typography>\n\n                  <Grid container spacing={2} sx={{ mb: 2 }}>\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.info.main}`\n                        }}\n                      >\n                        <ThunderstormIcon sx={{ color: theme.palette.info.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Rainfall\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {nearestPoint['Rainfall (mm)']} mm\n                        </Typography>\n                      </Paper>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.warning.main}`\n                        }}\n                      >\n                        <TerrainIcon sx={{ color: theme.palette.warning.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Elevation\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {nearestPoint['Elevation (m)']} m\n                        </Typography>\n                      </Paper>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.error.main}`\n                        }}\n                      >\n                        <WaterDropIcon sx={{ color: theme.palette.error.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Water Level\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {nearestPoint['Water Level (m)']} m\n                        </Typography>\n                      </Paper>\n                    </Grid>\n\n                    <Grid item xs={6}>\n                      <Paper\n                        elevation={1}\n                        sx={{\n                          p: 1.5,\n                          textAlign: 'center',\n                          borderTop: `3px solid ${theme.palette.primary.main}`\n                        }}\n                      >\n                        <LocationOnIcon sx={{ color: theme.palette.primary.main, mb: 0.5 }} />\n                        <Typography variant=\"body2\" color=\"text.secondary\">\n                          Distance\n                        </Typography>\n                        <Typography variant=\"h6\" fontWeight=\"medium\">\n                          {(Math.sqrt(\n                            Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) +\n                            Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)\n                          ) * 111).toFixed(1)} km\n                        </Typography>\n                      </Paper>\n                    </Grid>\n                  </Grid>\n                </Grid>\n\n                <Grid item xs={12} md={6}>\n                  <Typography variant=\"h6\" gutterBottom fontWeight=\"medium\">\n                    Recommended Mitigation Measures\n                  </Typography>\n\n                  <RiskMitigationMeasures\n                    riskLevel={nearestPoint.Flood_Prediction === 1 ? 'high' : 'low'}\n                    riskFactors={[\n                      ...(nearestPoint['Rainfall (mm)'] > 200 ? [{\n                        factor: 'Heavy Rainfall',\n                        value: `${nearestPoint['Rainfall (mm)']} mm`,\n                        severity: 'high'\n                      }] : []),\n                      ...(nearestPoint['Elevation (m)'] < 100 ? [{\n                        factor: 'Low Elevation',\n                        value: `${nearestPoint['Elevation (m)']} m`,\n                        severity: nearestPoint['Elevation (m)'] < 50 ? 'high' : 'medium'\n                      }] : []),\n                      ...(nearestPoint['Water Level (m)'] > 6 ? [{\n                        factor: 'High Water Level',\n                        value: `${nearestPoint['Water Level (m)']} m`,\n                        severity: nearestPoint['Water Level (m)'] > 8 ? 'high' : 'medium'\n                      }] : [])\n                    ]}\n                  />\n\n                  <Box sx={{ mt: 3 }}>\n                    <Button\n                      variant=\"outlined\"\n                      color=\"primary\"\n                      onClick={() => setShowLocationInfo(false)}\n                      startIcon={<CloseIcon />}\n                      sx={{ mt: 2 }}\n                    >\n                      Close Location Analysis\n                    </Button>\n                  </Box>\n                </Grid>\n              </Grid>\n            </Box>\n          </Paper>\n        </Box>\n      )}\n\n      <Box sx={{ mt: 3 }}>\n        <Typography variant=\"h6\" gutterBottom fontWeight=\"medium\">\n          Flood Risk Analysis\n        </Typography>\n\n        <Grid container spacing={2}>\n          <Grid item xs={12} md={6}>\n            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\" color={theme.palette.error.main} gutterBottom>\n                High Risk Areas ({highRiskCount})\n              </Typography>\n              <Typography variant=\"body2\" paragraph>\n                These areas show significant flood risk due to factors like heavy rainfall, low elevation,\n                or high water levels. Residents in these areas should be prepared for potential flooding\n                during monsoon seasons.\n              </Typography>\n              <Typography variant=\"body2\">\n                <strong>Key characteristics:</strong>\n              </Typography>\n              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average rainfall: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 1)\n                        .reduce((sum, point) => sum + point['Rainfall (mm)'], 0) /\n                      (highRiskCount || 1)).toFixed(1)\n                    } mm\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average elevation: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 1)\n                        .reduce((sum, point) => sum + point['Elevation (m)'], 0) /\n                      (highRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average water level: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 1)\n                        .reduce((sum, point) => sum + point['Water Level (m)'], 0) /\n                      (highRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n              </ul>\n            </Paper>\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>\n              <Typography variant=\"subtitle1\" fontWeight=\"bold\" color={theme.palette.success.main} gutterBottom>\n                Low Risk Areas ({lowRiskCount})\n              </Typography>\n              <Typography variant=\"body2\" paragraph>\n                These areas have minimal flood risk due to favorable geographical and environmental conditions.\n                They typically feature higher elevations, moderate rainfall, or effective drainage systems.\n              </Typography>\n              <Typography variant=\"body2\">\n                <strong>Key characteristics:</strong>\n              </Typography>\n              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average rainfall: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 0)\n                        .reduce((sum, point) => sum + point['Rainfall (mm)'], 0) /\n                      (lowRiskCount || 1)).toFixed(1)\n                    } mm\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average elevation: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 0)\n                        .reduce((sum, point) => sum + point['Elevation (m)'], 0) /\n                      (lowRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n                <li>\n                  <Typography variant=\"body2\">\n                    Average water level: {\n                      (mapData\n                        .filter(point => point.Flood_Prediction === 0)\n                        .reduce((sum, point) => sum + point['Water Level (m)'], 0) /\n                      (lowRiskCount || 1)).toFixed(1)\n                    } m\n                  </Typography>\n                </li>\n              </ul>\n            </Paper>\n          </Grid>\n        </Grid>\n      </Box>\n    </>\n  );\n};\n\nexport default FloodMap;\n"], "mappings": "kIAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAC/D,OACEC,YAAY,CACZC,SAAS,CACTC,YAAY,CACZC,KAAK,CACLC,MAAM,CACNC,UAAU,CACVC,WAAW,CACXC,SAAS,CACTC,MAAM,CACNC,OAAO,KACF,eAAe,CACtB,MAAO,CAAAC,CAAC,KAAM,SAAS,CACvB,MAAO,gBAAgB,CAAE;AACzB,OACEC,GAAG,CACHC,UAAU,CACVC,QAAQ,CACRC,KAAK,CACLC,IAAI,CACJC,OAAO,CACPC,IAAI,CACJC,WAAW,CACXC,IAAI,CACJC,QAAQ,CACRC,MAAM,CACNC,OAAO,CACPC,UAAU,CACVC,IAAI,CACJC,SAAS,CACTC,cAAc,CACdC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,YAAY,CACZC,KAAK,CACLC,QAAQ,CACRC,QAAQ,CACRC,YAAY,CACZC,iBAAiB,KACZ,eAAe,CACtB,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,gBAAgB,KAAM,kCAAkC,CAC/D,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,MAAO,CAAAC,aAAa,KAAM,+BAA+B,CACzD,MAAO,CAAAC,YAAY,KAAM,8BAA8B,CACvD,MAAO,CAAAC,OAAO,KAAM,yBAAyB,CAC7C,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,UAAU,KAAM,4BAA4B,CACnD,MAAO,CAAAC,cAAc,KAAM,gCAAgC,CAC3D,MAAO,CAAAC,SAAS,KAAM,2BAA2B,CACjD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,eAAe,KAAM,iCAAiC,CAC7D,MAAO,CAAAC,QAAQ,KAAM,0BAA0B,CAC/C,OAASC,SAAS,CAAEC,QAAQ,KAAQ,cAAc,CAClD,MAAO,CAAAC,KAAK,KAAM,OAAO,CAEzB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBACA,KAAM,CAAAC,SAAS,CAAGA,CAAA,GAAM,CACtB,KAAM,CAAAC,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAC4C,QAAQ,CAAEC,WAAW,CAAC,CAAGpE,QAAQ,CAAC,KAAK,CAAC,CAE/C,KAAM,CAAAqE,eAAe,CAAGb,SAAS,CAAC,CAChCc,OAAO,CAAEH,QAAQ,CAAG,CAAC,CAAG,GAAG,CAC3BI,MAAM,CAAEJ,QAAQ,CAAG,GAAG,CAAG,EAAE,CAC3BK,MAAM,CAAE,CAAEC,OAAO,CAAE,GAAG,CAAEC,QAAQ,CAAE,EAAG,CACvC,CAAC,CAAC,CAEF,mBACEZ,KAAA,CAACL,QAAQ,CAACkB,GAAG,EAACC,KAAK,CAAAC,aAAA,EACjBC,QAAQ,CAAE,UAAU,CACpBC,MAAM,CAAE,EAAE,CACVC,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,IAAI,CACZC,eAAe,CAAE,OAAO,CACxBC,OAAO,CAAE,WAAW,CACpBC,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,4BAA4B,CACvCC,QAAQ,CAAE,QAAQ,CAClBC,KAAK,CAAE,GAAG,EACPlB,eAAe,CAClB,CAAAmB,QAAA,eACA1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CACPC,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,EAAE,CAAE1B,QAAQ,CAAG,CAAC,CAAG,CACrB,CAAE,CAAAqB,QAAA,eACA5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAAAP,QAAA,CAAC,YAElD,CAAY,CAAC,cACb5B,IAAA,CAAClC,UAAU,EACTsE,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM7B,WAAW,CAAC,CAACD,QAAQ,CAAE,CACtCsB,EAAE,CAAE,CACFP,eAAe,CAAEf,QAAQ,CAAG,kBAAkB,CAAG,aAAa,CAC9D+B,UAAU,CAAE,eACd,CAAE,CAAAV,QAAA,cAEF5B,IAAA,CAAClB,QAAQ,EAACyD,QAAQ,CAAC,OAAO,CAAE,CAAC,CACnB,CAAC,EACV,CAAC,CAELhC,QAAQ,eACPL,KAAA,CAAAE,SAAA,EAAAwB,QAAA,eACE5B,IAAA,CAACzC,OAAO,EAACsE,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BxC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAACN,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CAAC,uBAEjE,CAAY,CAAC,cAEb1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD5B,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CACPF,KAAK,CAAE,EAAE,CACThB,MAAM,CAAE,EAAE,CACVa,YAAY,CAAE,KAAK,CACnBF,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CACzCC,EAAE,CAAE,CACN,CAAE,CAAE,CAAC,cACL7C,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,gBAAc,CAAY,CAAC,EACpD,CAAC,cAEN1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,eAC1D5B,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CACPF,KAAK,CAAE,EAAE,CACThB,MAAM,CAAE,EAAE,CACVa,YAAY,CAAE,KAAK,CACnBF,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAI,CAC3CC,EAAE,CAAE,CACN,CAAE,CAAE,CAAC,cACL7C,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,eAAa,CAAY,CAAC,EACnD,CAAC,cAEN5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAACN,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CAAC,cAEjE,CAAY,CAAC,cAEb1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACxD5B,IAAA,CAACnB,gBAAgB,EAAC0D,QAAQ,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAC,CAAEE,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAK,CAAE,CAAE,CAAC,cACpF5C,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,yBAAuB,CAAY,CAAC,EAC7D,CAAC,cAEN1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,eAC1D5B,IAAA,CAACpB,WAAW,EAAC2D,QAAQ,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAC,CAAEE,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAK,CAAE,CAAE,CAAC,cAClF5C,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,uBAAwB,CAAY,CAAC,EAC9D,CAAC,cAEN1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAES,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,eAC1D5B,IAAA,CAACrB,aAAa,EAAC4D,QAAQ,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEgB,EAAE,CAAE,CAAC,CAAEE,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAAE,CAAE,CAAC,cAClF5C,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,wBAAsB,CAAY,CAAC,EAC5D,CAAC,cAEN5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACL,EAAE,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEW,EAAE,CAAE,CAAC,CAAEM,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACQ,IAAI,CAACC,SAAU,CAAE,CAAAvB,QAAA,CAAC,qEAEpG,CAAY,CAAC,EACb,CACH,EACW,CAAC,CAEnB,CAAC,CAED;AACA,KAAM,CAAAwB,cAAc,CAAGC,IAAA,EAA0B,IAAzB,CAAEC,gBAAiB,CAAC,CAAAD,IAAA,CAC1C,KAAM,CAAAE,GAAG,CAAG5G,MAAM,CAAC,CAAC,CACpB,KAAM,CAAA2D,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAC6F,WAAW,CAAEC,cAAc,CAAC,CAAGrH,QAAQ,CAAC,EAAE,CAAC,CAClD,KAAM,CAACsH,aAAa,CAAEC,gBAAgB,CAAC,CAAGvH,QAAQ,CAAC,EAAE,CAAC,CACtD,KAAM,CAACwH,SAAS,CAAEC,YAAY,CAAC,CAAGzH,QAAQ,CAAC,KAAK,CAAC,CACjD,KAAM,CAAC0H,WAAW,CAAEC,cAAc,CAAC,CAAG3H,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAACuG,KAAK,CAAEqB,QAAQ,CAAC,CAAG5H,QAAQ,CAAC,IAAI,CAAC,CAExC;AACA,KAAM,CAAA6H,cAAc,CAAG3H,WAAW,CAAC,KAAO,CAAA4H,KAAK,EAAK,CAClD,GAAI,CAACA,KAAK,EAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,CAAG,CAAC,CAAE,OAEvCP,YAAY,CAAC,IAAI,CAAC,CAClBG,QAAQ,CAAC,IAAI,CAAC,CAEd;AACA,GAAI,CAAAK,aAAa,CAEjB,GAAI,CACF;AACA,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAxE,KAAK,CAACyE,GAAG,8CAA+C,CAC7EC,MAAM,CAAE,CACNC,CAAC,IAAAC,MAAA,CAAKR,KAAK,WAAS,CACpBS,MAAM,CAAE,MAAM,CACdC,KAAK,CAAE,CAAC,CACRC,YAAY,CAAE,IAAI,CAAE;AACpBC,cAAc,CAAE,CAClB,CAAC,CACDC,OAAO,CAAE,CACP,iBAAiB,CAAE,gBAAgB,CACnC,YAAY,CAAE,wBAChB,CACF,CAAC,CAAC,CAEF,GAAIT,QAAQ,CAACU,IAAI,EAAIV,QAAQ,CAACU,IAAI,CAACZ,MAAM,CAAG,CAAC,CAAE,CAC7CT,gBAAgB,CAACW,QAAQ,CAACU,IAAI,CAAC,CAC/BjB,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,IAAM,CACLJ,gBAAgB,CAAC,EAAE,CAAC,CACpBK,QAAQ,CAAC,kDAAkD,CAAC,CAC9D,CACF,CAAE,MAAOiB,GAAG,CAAE,CACZC,OAAO,CAACvC,KAAK,CAAC,+BAA+B,CAAEsC,GAAG,CAAC,CACnDjB,QAAQ,CAAC,iDAAiD,CAAC,CAC7D,CAAC,OAAS,CACR;AACA;AACAK,aAAa,CAAGc,UAAU,CAAC,IAAM,CAC/BtB,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAAE,GAAG,CAAC,CACT,CAEA,MAAO,IAAM,CACX,GAAIQ,aAAa,CAAEe,YAAY,CAACf,aAAa,CAAC,CAChD,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgB,kBAAkB,CAAIC,CAAC,EAAK,CAChC7B,cAAc,CAAC6B,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAC9B,GAAIF,CAAC,CAACC,MAAM,CAACC,KAAK,CAACrB,IAAI,CAAC,CAAC,CAACC,MAAM,GAAK,CAAC,CAAE,CACtCL,cAAc,CAAC,KAAK,CAAC,CACvB,CACF,CAAC,CAED,KAAM,CAAA0B,kBAAkB,CAAIH,CAAC,EAAK,CAChCA,CAAC,CAACI,cAAc,CAAC,CAAC,CAClBzB,cAAc,CAACT,WAAW,CAAC,CAC7B,CAAC,CAED,KAAM,CAAAmC,mBAAmB,CAAIC,QAAQ,EAAK,CACxC,KAAM,CAAAC,GAAG,CAAGC,UAAU,CAACF,QAAQ,CAACC,GAAG,CAAC,CACpC,KAAM,CAAAE,GAAG,CAAGD,UAAU,CAACF,QAAQ,CAACI,GAAG,CAAC,CAEpC;AACAzC,GAAG,CAAC0C,KAAK,CAAC,CAACJ,GAAG,CAAEE,GAAG,CAAC,CAAE,EAAE,CAAE,CACxBG,QAAQ,CAAE,GACZ,CAAC,CAAC,CAEF;AACA5C,gBAAgB,CAAC,CACfuC,GAAG,CACHE,GAAG,CACHI,IAAI,CAAEP,QAAQ,CAACQ,YAAY,CAC3BC,IAAI,CAAET,QAAQ,CAACS,IAAI,CACnBC,UAAU,CAAEV,QAAQ,CAACU,UAAU,CAC/BC,OAAO,CAAEX,QAAQ,CAACW,OACpB,CAAC,CAAC,CAEF;AACAxC,cAAc,CAAC,KAAK,CAAC,CACvB,CAAC,CAED,mBACE/D,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CACPX,QAAQ,CAAE,UAAU,CACpBsF,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRpF,MAAM,CAAE,IAAI,CACZM,KAAK,CAAE,GAAG,CACV+E,QAAQ,CAAE,mBACZ,CAAE,CAAA9E,QAAA,cACA1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,GAAG,CACNpF,YAAY,CAAE,CAAC,CACfC,SAAS,CAAE,4BACb,CAAE,CAAAG,QAAA,eAEF5B,IAAA,SAAM6G,QAAQ,CAAEpB,kBAAmB,CAAA7D,QAAA,cACjC5B,IAAA,CAAChC,SAAS,EACR8I,SAAS,MACTC,WAAW,CAAC,gCAAgC,CAC5CvB,KAAK,CAAEhC,WAAY,CACnBwD,QAAQ,CAAE3B,kBAAmB,CAC7BnD,OAAO,CAAC,UAAU,CAClBE,IAAI,CAAC,OAAO,CACZ6E,UAAU,CAAE,CACVC,cAAc,cACZlH,IAAA,CAAC/B,cAAc,EAACiD,QAAQ,CAAC,OAAO,CAAAU,QAAA,cAC9B5B,IAAA,CAACV,UAAU,EAACyD,KAAK,CAAC,QAAQ,CAAE,CAAC,CACf,CACjB,CACDoE,YAAY,CAAEvD,SAAS,cACrB5D,IAAA,CAAC/B,cAAc,EAACiD,QAAQ,CAAC,KAAK,CAAAU,QAAA,cAC5B5B,IAAA,CAAC9C,GAAG,EACF2E,EAAE,CAAE,CACFF,KAAK,CAAE,EAAE,CACThB,MAAM,CAAE,EAAE,CACVa,YAAY,CAAE,KAAK,CACnB4F,MAAM,CAAE,uBAAuB,CAC/BC,cAAc,CAAE/G,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAC1C2E,SAAS,CAAE,yBACb,CAAE,CACH,CAAC,CACY,CAAC,CACf/D,WAAW,cACbxD,IAAA,CAAC/B,cAAc,EAACiD,QAAQ,CAAC,KAAK,CAAAU,QAAA,cAC5B5B,IAAA,CAAClC,UAAU,EACTsE,IAAI,CAAC,OAAO,CACZC,OAAO,CAAEA,CAAA,GAAM,CACboB,cAAc,CAAC,EAAE,CAAC,CAClBM,cAAc,CAAC,KAAK,CAAC,CACvB,CAAE,CAAAnC,QAAA,cAEF5B,IAAA,CAACR,SAAS,EAAC+C,QAAQ,CAAC,OAAO,CAAE,CAAC,CACpB,CAAC,CACC,CAAC,CACf,IAAI,CACRV,EAAE,CAAE,CACFL,YAAY,CAAE,CAAC,CACf,eAAe,CAAE,CACfC,SAAS,cAAAiD,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,MACpD,CACF,CACF,CAAE,CACH,CAAC,CACE,CAAC,CAEND,KAAK,eACJ3C,IAAA,CAAC1B,KAAK,EACJkJ,QAAQ,CAAC,SAAS,CAClB3F,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAC,CAAEjB,YAAY,CAAE,CAAE,CAAE,CAC/BiG,OAAO,CAAEA,CAAA,GAAMzD,QAAQ,CAAC,IAAI,CAAE,CAAApC,QAAA,CAE7Be,KAAK,CACD,CACR,cAED3C,IAAA,CAACzB,QAAQ,EAACmJ,EAAE,CAAE5D,WAAW,EAAIJ,aAAa,CAACU,MAAM,CAAG,CAAE,CAAAxC,QAAA,cACpD5B,IAAA,CAAC9B,IAAI,EAAC2D,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAC,CAAEkF,SAAS,CAAE,GAAG,CAAEjG,QAAQ,CAAE,MAAO,CAAE,CAAAE,QAAA,CACnD8B,aAAa,CAACH,GAAG,CAAC,CAACqE,MAAM,CAAEC,KAAK,QAAAC,eAAA,CAAAC,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,oBAC/B/H,KAAA,CAAC/B,QAAQ,EAEP+J,MAAM,MACN7F,OAAO,CAAEA,CAAA,GAAMsD,mBAAmB,CAACiC,MAAM,CAAE,CAC3C/F,EAAE,CAAE,CACFL,YAAY,CAAE,CAAC,CACfS,EAAE,CAAE,GAAG,CACP,SAAS,CAAE,CACTX,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAACyF,MAAM,CAACC,KACxC,CACF,CAAE,CAAAxG,QAAA,eAEF5B,IAAA,CAAC3B,YAAY,EAACwD,EAAE,CAAE,CAAEwG,QAAQ,CAAE,EAAG,CAAE,CAAAzG,QAAA,cACjC5B,IAAA,CAACT,cAAc,EAACwD,KAAK,CAAC,SAAS,CAAE,CAAC,CACtB,CAAC,cACf/C,IAAA,CAAC5B,YAAY,EACXkJ,OAAO,CAAE,EAAAQ,eAAA,CAAAF,MAAM,CAACrB,OAAO,UAAAuB,eAAA,iBAAdA,eAAA,CAAgBQ,IAAI,KAAAP,gBAAA,CAAIH,MAAM,CAACrB,OAAO,UAAAwB,gBAAA,iBAAdA,gBAAA,CAAgBQ,IAAI,KAAAP,gBAAA,CAAIJ,MAAM,CAACrB,OAAO,UAAAyB,gBAAA,iBAAdA,gBAAA,CAAgBQ,OAAO,KAAAP,gBAAA,CAAIL,MAAM,CAACrB,OAAO,UAAA0B,gBAAA,iBAAdA,gBAAA,CAAgBQ,KAAK,GAAIb,MAAM,CAACxB,YAAY,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,CAC/IvF,SAAS,CAAEyE,MAAM,CAACxB,YAAa,CAC/BuC,wBAAwB,CAAE,CACxBC,MAAM,CAAE,IAAI,CACZ5H,KAAK,CAAE,CAAEuB,QAAQ,CAAE,SAAU,CAC/B,CAAE,CACH,CAAC,GArBGsF,KAsBG,CAAC,EACZ,CAAC,CACE,CAAC,CACC,CAAC,EACN,CAAC,CACL,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAgB,cAAc,CAAGC,KAAA,EAAiC,IAAhC,CAAEC,QAAQ,CAAEC,aAAc,CAAC,CAAAF,KAAA,CACjD,KAAM,CAAAxI,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CAExB,mBACEqC,IAAA,CAAC9C,GAAG,EACF2E,EAAE,CAAE,CACFX,QAAQ,CAAE,UAAU,CACpBsF,GAAG,CAAE,EAAE,CACPC,IAAI,CAAE,EAAE,CACRpF,MAAM,CAAE,IAAI,CACZC,eAAe,CAAE,2BAA2B,CAC5C2H,cAAc,CAAE,YAAY,CAC5BzH,YAAY,CAAE,CAAC,CACfD,OAAO,CAAE,CAAC,CACVE,SAAS,CAAE,gCAAgC,CAC3C2F,MAAM,CAAE,oCACV,CAAE,CAAAxF,QAAA,cAEF1B,KAAA,CAACxB,iBAAiB,EAChB8G,KAAK,CAAEuD,QAAS,CAChBG,SAAS,MACTlC,QAAQ,CAAEA,CAACmC,KAAK,CAAEC,QAAQ,GAAK,CAC7B,GAAIA,QAAQ,GAAK,IAAI,CAAE,CACrBJ,aAAa,CAACI,QAAQ,CAAC,CACzB,CACF,CAAE,CACFhH,IAAI,CAAC,OAAO,CACZP,EAAE,CAAE,CACF,yBAAyB,CAAE,CACzBuF,MAAM,CAAE,MAAM,CACd5F,YAAY,CAAE,GAAG,CACjB6H,MAAM,CAAE,IAAI,CACZ9H,OAAO,CAAE,UAAU,CACnBe,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACThB,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAACgC,KAAK,CAAG,IAAI,CACnDC,SAAS,CAAE,aACb,CAAC,CACD,gBAAgB,CAAE,CAChBjI,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAC3CG,KAAK,CAAE,OAAO,CACd,SAAS,CAAE,CACTzB,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAACkC,IACzC,CACF,CACF,CACF,CAAE,CAAA5H,QAAA,eAEF5B,IAAA,CAACvB,YAAY,EAAC+G,KAAK,CAAC,MAAM,CAAC,aAAW,YAAY,CAAA5D,QAAA,cAChD5B,IAAA,CAACnC,OAAO,EAAC4L,KAAK,CAAC,YAAY,CAACC,SAAS,CAAC,QAAQ,CAAA9H,QAAA,cAC5C1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE2H,GAAG,CAAE,GAAI,CAAE,CAAA/H,QAAA,eAC3D5B,IAAA,CAAChB,YAAY,EAACuD,QAAQ,CAAC,OAAO,CAAE,CAAC,cACjCvC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACL,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAE8H,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAQ,CAAE,CAAE,CAAAjI,QAAA,CAAC,MAE5E,CAAY,CAAC,EACV,CAAC,CACC,CAAC,CACE,CAAC,cAEf5B,IAAA,CAACvB,YAAY,EAAC+G,KAAK,CAAC,WAAW,CAAC,aAAW,gBAAgB,CAAA5D,QAAA,cACzD5B,IAAA,CAACnC,OAAO,EAAC4L,KAAK,CAAC,gBAAgB,CAACC,SAAS,CAAC,QAAQ,CAAA9H,QAAA,cAChD1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE2H,GAAG,CAAE,GAAI,CAAE,CAAA/H,QAAA,eAC3D5B,IAAA,CAACjB,aAAa,EAACwD,QAAQ,CAAC,OAAO,CAAE,CAAC,cAClCvC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACL,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAE8H,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAQ,CAAE,CAAE,CAAAjI,QAAA,CAAC,WAE5E,CAAY,CAAC,EACV,CAAC,CACC,CAAC,CACE,CAAC,cAEf5B,IAAA,CAACvB,YAAY,EAAC+G,KAAK,CAAC,SAAS,CAAC,aAAW,cAAc,CAAA5D,QAAA,cACrD5B,IAAA,CAACnC,OAAO,EAAC4L,KAAK,CAAC,cAAc,CAACC,SAAS,CAAC,QAAQ,CAAA9H,QAAA,cAC9C1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAE2H,GAAG,CAAE,GAAI,CAAE,CAAA/H,QAAA,eAC3D5B,IAAA,CAACpB,WAAW,EAAC2D,QAAQ,CAAC,OAAO,CAAE,CAAC,cAChCvC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACL,EAAE,CAAE,CAAEC,OAAO,CAAE,CAAE8H,EAAE,CAAE,MAAM,CAAEC,EAAE,CAAE,OAAQ,CAAE,CAAE,CAAAjI,QAAA,CAAC,SAE5E,CAAY,CAAC,EACV,CAAC,CACC,CAAC,CACE,CAAC,EACE,CAAC,CACjB,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAkI,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAvG,GAAG,CAAG5G,MAAM,CAAC,CAAC,CACpB,KAAM,CAAA2D,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CAExB,KAAM,CAAAoM,YAAY,CAAGA,CAAA,GAAM,CACzBxG,GAAG,CAACyG,MAAM,CAAC,CAAC,CACd,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,GAAM,CAC1B1G,GAAG,CAAC2G,OAAO,CAAC,CAAC,CACf,CAAC,CAED,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzB5G,GAAG,CAAC6G,MAAM,CAAC,CAAEC,OAAO,CAAE,IAAI,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAC5C,CAAC,CAED,mBACEpK,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CACPX,QAAQ,CAAE,UAAU,CACpBsF,GAAG,CAAE,EAAE,CACPpF,KAAK,CAAE,EAAE,CACTC,MAAM,CAAE,IAAI,CACZS,OAAO,CAAE,MAAM,CACfyI,aAAa,CAAE,QAAQ,CACvBZ,GAAG,CAAE,CACP,CAAE,CAAA/H,QAAA,eACA5B,IAAA,CAACnC,OAAO,EAAC4L,KAAK,CAAC,SAAS,CAACC,SAAS,CAAC,MAAM,CAAA9H,QAAA,cACvC5B,IAAA,CAAClC,UAAU,EACTuE,OAAO,CAAE0H,YAAa,CACtBlI,EAAE,CAAE,CACFP,eAAe,CAAE,2BAA2B,CAC5C2H,cAAc,CAAE,YAAY,CAC5BxH,SAAS,CAAE,6BAA6B,CACxC2F,MAAM,CAAE,oCAAoC,CAC5C9E,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACThB,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAC3CG,KAAK,CAAE,OAAO,CACdwG,SAAS,CAAE,YACb,CACF,CAAE,CAAA3H,QAAA,cAEF5B,IAAA,CAACZ,UAAU,GAAE,CAAC,CACJ,CAAC,CACN,CAAC,cAEVY,IAAA,CAACnC,OAAO,EAAC4L,KAAK,CAAC,UAAU,CAACC,SAAS,CAAC,MAAM,CAAA9H,QAAA,cACxC5B,IAAA,CAAClC,UAAU,EACTuE,OAAO,CAAE4H,aAAc,CACvBpI,EAAE,CAAE,CACFP,eAAe,CAAE,2BAA2B,CAC5C2H,cAAc,CAAE,YAAY,CAC5BxH,SAAS,CAAE,6BAA6B,CACxC2F,MAAM,CAAE,oCAAoC,CAC5C9E,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACThB,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAC3CG,KAAK,CAAE,OAAO,CACdwG,SAAS,CAAE,YACb,CACF,CAAE,CAAA3H,QAAA,cAEF5B,IAAA,CAACX,WAAW,GAAE,CAAC,CACL,CAAC,CACN,CAAC,cAEVW,IAAA,CAACnC,OAAO,EAAC4L,KAAK,CAAC,aAAa,CAACC,SAAS,CAAC,MAAM,CAAA9H,QAAA,cAC3C5B,IAAA,CAAClC,UAAU,EACTuE,OAAO,CAAE8H,YAAa,CACtBtI,EAAE,CAAE,CACFP,eAAe,CAAE,2BAA2B,CAC5C2H,cAAc,CAAE,YAAY,CAC5BxH,SAAS,CAAE,6BAA6B,CACxC2F,MAAM,CAAE,oCAAoC,CAC5C9E,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACThB,eAAe,CAAEhB,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAI,CAC3CG,KAAK,CAAE,OAAO,CACdwG,SAAS,CAAE,YACb,CACF,CAAE,CAAA3H,QAAA,cAEF5B,IAAA,CAACb,cAAc,GAAE,CAAC,CACR,CAAC,CACN,CAAC,EACP,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAqL,WAAW,CAAGC,KAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,KAAA,CAC9B,KAAM,CAAAlH,GAAG,CAAG5G,MAAM,CAAC,CAAC,CACpB,KAAM,CAACgO,cAAc,CAAEC,iBAAiB,CAAC,CAAGxO,QAAQ,CAAC,IAAI,CAAC,CAE1DC,SAAS,CAAC,IAAM,CACd,GAAI,CAACqO,OAAO,EAAIA,OAAO,CAACtG,MAAM,GAAK,CAAC,EAAI,CAACuG,cAAc,CAAE,OAEzD;AACA,KAAM,CAAAE,cAAc,CAAGH,OAAO,CAACI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC5E,KAAM,CAAAC,SAAS,CAAG,EAAE,CAEpB;AACA,KAAM,CAAAC,eAAe,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CAEjCN,cAAc,CAACO,OAAO,CAAC,CAACL,KAAK,CAAElD,KAAK,GAAK,CACvC,GAAIqD,eAAe,CAACG,GAAG,CAACxD,KAAK,CAAC,CAAE,OAEhC,KAAM,CAAAyD,IAAI,CAAG,CAACP,KAAK,CAAC,CACpB,KAAM,CAAAQ,UAAU,CAAG,CAAE1F,GAAG,CAAEkF,KAAK,CAACS,QAAQ,CAAEzF,GAAG,CAAEgF,KAAK,CAACU,SAAU,CAAC,CAEhE;AACAZ,cAAc,CAACO,OAAO,CAAC,CAACM,UAAU,CAAEC,UAAU,GAAK,CACjD,GAAI9D,KAAK,GAAK8D,UAAU,EAAIT,eAAe,CAACG,GAAG,CAACM,UAAU,CAAC,CAAE,OAE7D,KAAM,CAAAC,QAAQ,CAAGC,IAAI,CAACC,IAAI,CACxBD,IAAI,CAACE,GAAG,CAAChB,KAAK,CAACS,QAAQ,CAAGE,UAAU,CAACF,QAAQ,CAAE,CAAC,CAAC,CACjDK,IAAI,CAACE,GAAG,CAAChB,KAAK,CAACU,SAAS,CAAGC,UAAU,CAACD,SAAS,CAAE,CAAC,CACpD,CAAC,CAED,GAAIG,QAAQ,CAAG,GAAG,CAAE,CAClBN,IAAI,CAACU,IAAI,CAACN,UAAU,CAAC,CACrBR,eAAe,CAACe,GAAG,CAACN,UAAU,CAAC,CACjC,CACF,CAAC,CAAC,CAEF,GAAIL,IAAI,CAAClH,MAAM,EAAI,CAAC,CAAE,CACpB;AACA,KAAM,CAAA8H,MAAM,CAAGZ,IAAI,CAACa,MAAM,CAAC,CAACC,GAAG,CAAExF,CAAC,GAAK,CACrCwF,GAAG,CAACC,MAAM,CAAGR,IAAI,CAACS,GAAG,CAACF,GAAG,CAACC,MAAM,CAAEzF,CAAC,CAAC4E,QAAQ,CAAC,CAC7CY,GAAG,CAACG,MAAM,CAAGV,IAAI,CAACW,GAAG,CAACJ,GAAG,CAACG,MAAM,CAAE3F,CAAC,CAAC4E,QAAQ,CAAC,CAC7CY,GAAG,CAACK,MAAM,CAAGZ,IAAI,CAACS,GAAG,CAACF,GAAG,CAACK,MAAM,CAAE7F,CAAC,CAAC6E,SAAS,CAAC,CAC9CW,GAAG,CAACM,MAAM,CAAGb,IAAI,CAACW,GAAG,CAACJ,GAAG,CAACM,MAAM,CAAE9F,CAAC,CAAC6E,SAAS,CAAC,CAC9C,MAAO,CAAAW,GAAG,CACZ,CAAC,CAAE,CACDC,MAAM,CAAEM,QAAQ,CAChBJ,MAAM,CAAE,CAACI,QAAQ,CACjBF,MAAM,CAAEE,QAAQ,CAChBD,MAAM,CAAE,CAACC,QACX,CAAC,CAAC,CAEF;AACA,KAAM,CAAApL,OAAO,CAAG,GAAG,CACnB,KAAM,CAAAqL,OAAO,CAAG,CACd,CAACV,MAAM,CAACG,MAAM,CAAG9K,OAAO,CAAE2K,MAAM,CAACO,MAAM,CAAGlL,OAAO,CAAC,CAClD,CAAC2K,MAAM,CAACG,MAAM,CAAG9K,OAAO,CAAE2K,MAAM,CAACQ,MAAM,CAAGnL,OAAO,CAAC,CAClD,CAAC2K,MAAM,CAACK,MAAM,CAAGhL,OAAO,CAAE2K,MAAM,CAACQ,MAAM,CAAGnL,OAAO,CAAC,CAClD,CAAC2K,MAAM,CAACK,MAAM,CAAGhL,OAAO,CAAE2K,MAAM,CAACO,MAAM,CAAGlL,OAAO,CAAC,CACnD,CAED0J,SAAS,CAACe,IAAI,CAAC,CACbY,OAAO,CACPC,SAAS,CAAEvB,IAAI,CAAClH,MAAM,CACtB0I,MAAM,CAAExB,IACV,CAAC,CAAC,CACJ,CAEAJ,eAAe,CAACe,GAAG,CAACpE,KAAK,CAAC,CAC5B,CAAC,CAAC,CAEF;AACAoD,SAAS,CAACG,OAAO,CAAC,CAACE,IAAI,CAAEzD,KAAK,GAAK,CACjC,KAAM,CAAAgF,SAAS,CAAGhB,IAAI,CAACS,GAAG,CAAChB,IAAI,CAACuB,SAAS,CAAG,CAAC,CAAE,CAAC,CAAC,CAAE;AACnD,KAAM,CAAAD,OAAO,CAAG3P,CAAC,CAAC2P,OAAO,CAACtB,IAAI,CAACsB,OAAO,CAAE,CACtC7J,KAAK,sBAAA2B,MAAA,CAAuB,GAAG,CAAGmI,SAAS,CAAG,GAAG,KAAG,CACpDE,SAAS,wBAAArI,MAAA,CAAyB,GAAG,CAAGmI,SAAS,CAAG,GAAG,KAAG,CAC1DG,WAAW,CAAE,GAAG,CAAGH,SAAS,CAAG,GAAG,CAClCI,MAAM,CAAE,CAAC,CACTC,SAAS,CAAE,gBACb,CAAC,CAAC,CAACC,KAAK,CAAC5J,GAAG,CAAC,CAEbqJ,OAAO,CAACQ,WAAW,oCAAA1I,MAAA,CAAoC4G,IAAI,CAACuB,SAAS,EAAI,CACvEQ,SAAS,CAAE,KAAK,CAChBC,SAAS,CAAE,QAAQ,CACnBJ,SAAS,CAAE,mBACb,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF,MAAO,IAAM,CACX;AACA3J,GAAG,CAACgK,SAAS,CAAEC,KAAK,EAAK,CACvB,GAAIA,KAAK,CAACC,OAAO,EAAID,KAAK,CAACC,OAAO,CAACP,SAAS,GAAK,gBAAgB,CAAE,CACjE3J,GAAG,CAACmK,WAAW,CAACF,KAAK,CAAC,CACxB,CACF,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAE,CAACjK,GAAG,CAAEmH,OAAO,CAAEC,cAAc,CAAC,CAAC,CAElC,MAAO,KAAI,CACb,CAAC,CAED;AACA,KAAM,CAAAgD,YAAY,CAAGC,KAAA,EAAiB,IAAhB,CAAElD,OAAQ,CAAC,CAAAkD,KAAA,CAC/B,KAAM,CAAArK,GAAG,CAAG5G,MAAM,CAAC,CAAC,CACpB,KAAM,CAAA2D,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CAExB;AACAtB,SAAS,CAAC,IAAM,CACd,GAAI,CAACqO,OAAO,EAAIA,OAAO,CAACtG,MAAM,GAAK,CAAC,CAAE,OAEtC;AACA,KAAM,CAAAyG,cAAc,CAAGH,OAAO,CAACI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC5E,KAAM,CAAA6C,QAAQ,CAAG,EAAE,CAEnB;AACAhD,cAAc,CAACO,OAAO,CAACL,KAAK,EAAI,CAC9B,KAAM,CAAAlF,GAAG,CAAGkF,KAAK,CAACS,QAAQ,CAC1B,KAAM,CAAAzF,GAAG,CAAGgF,KAAK,CAACU,SAAS,CAE3B;AACA,KAAM,CAAAqC,SAAS,CAAGD,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAI,CACzC,MAAO,CAAAnC,IAAI,CAACoC,GAAG,CAACD,OAAO,CAACE,SAAS,CAAGrI,GAAG,CAAC,CAAG,CAAC,EACrCgG,IAAI,CAACoC,GAAG,CAACD,OAAO,CAACG,SAAS,CAAGpI,GAAG,CAAC,CAAG,CAAC,CAC9C,CAAC,CAAC,CAEF,GAAI,CAAC+H,SAAS,EAAIjD,cAAc,CAACC,MAAM,CAAClE,CAAC,EACvCiF,IAAI,CAACoC,GAAG,CAACrH,CAAC,CAAC4E,QAAQ,CAAG3F,GAAG,CAAC,CAAG,CAAC,EAC9BgG,IAAI,CAACoC,GAAG,CAACrH,CAAC,CAAC6E,SAAS,CAAG1F,GAAG,CAAC,CAAG,CAChC,CAAC,CAAC3B,MAAM,CAAG,CAAC,CAAE,CACZ;AACAyJ,QAAQ,CAAC7B,IAAI,CAAC,CACZkC,SAAS,CAAErI,GAAG,CACdsI,SAAS,CAAEpI,GAAG,CACdqI,KAAK,CAAEvD,cAAc,CAACC,MAAM,CAAClE,CAAC,EAC5BiF,IAAI,CAACoC,GAAG,CAACrH,CAAC,CAAC4E,QAAQ,CAAG3F,GAAG,CAAC,CAAG,CAAC,EAC9BgG,IAAI,CAACoC,GAAG,CAACrH,CAAC,CAAC6E,SAAS,CAAG1F,GAAG,CAAC,CAAG,CAChC,CAAC,CAAC3B,MACJ,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF,MAAO,IAAM,CACX;AAAA,CACD,CACH,CAAC,CAAE,CAACsG,OAAO,CAAEnH,GAAG,CAAC,CAAC,CAElB,MAAO,KAAI,CAAE;AACf,CAAC,CAED;AACA,KAAM,CAAA8K,sBAAsB,CAAGC,KAAA,EAAgC,IAA/B,CAAEC,SAAS,CAAEC,WAAY,CAAC,CAAAF,KAAA,CACxD,KAAM,CAAAhO,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CAExB;AACA,KAAM,CAAA8Q,qBAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,QAAQ,CAAG,EAAE,CAEnB;AACA,GAAIH,SAAS,GAAK,MAAM,CAAE,CACxBG,QAAQ,CAAC1C,IAAI,CAAC,CACZvC,KAAK,CAAE,qBAAqB,CAC5BkF,WAAW,CAAE,qFAAqF,CAClGC,IAAI,cAAE5O,IAAA,CAACL,QAAQ,EAACkC,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAAE,CAAE,CAC5D,CAAC,CAAC,CACF8L,QAAQ,CAAC1C,IAAI,CAAC,CACZvC,KAAK,CAAE,sBAAsB,CAC7BkF,WAAW,CAAE,8EAA8E,CAC3FC,IAAI,cAAE5O,IAAA,CAACP,WAAW,EAACoC,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAAE,CAAE,CAC/D,CAAC,CAAC,CACJ,CAEA;AACA4L,WAAW,CAACpD,OAAO,CAACyD,MAAM,EAAI,CAC5B,GAAIA,MAAM,CAACA,MAAM,GAAK,gBAAgB,EAAIA,MAAM,CAACrH,QAAQ,GAAK,MAAM,CAAE,CACpEkH,QAAQ,CAAC1C,IAAI,CAAC,CACZvC,KAAK,CAAE,sBAAsB,CAC7BkF,WAAW,CAAE,gGAAgG,CAC7GC,IAAI,cAAE5O,IAAA,CAACN,eAAe,EAACmC,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAK,CAAE,CAAE,CAClE,CAAC,CAAC,CACJ,CAEA,GAAIiM,MAAM,CAACA,MAAM,GAAK,eAAe,EAAIA,MAAM,CAACrH,QAAQ,GAAK,MAAM,CAAE,CACnEkH,QAAQ,CAAC1C,IAAI,CAAC,CACZvC,KAAK,CAAE,qBAAqB,CAC5BkF,WAAW,CAAE,8FAA8F,CAC3GC,IAAI,cAAE5O,IAAA,CAACL,QAAQ,EAACkC,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAK,CAAE,CAAE,CAC9D,CAAC,CAAC,CACJ,CAEA,GAAIiM,MAAM,CAACA,MAAM,GAAK,kBAAkB,EAAIA,MAAM,CAACrH,QAAQ,GAAK,MAAM,CAAE,CACtEkH,QAAQ,CAAC1C,IAAI,CAAC,CACZvC,KAAK,CAAE,gBAAgB,CACvBkF,WAAW,CAAE,8FAA8F,CAC3GC,IAAI,cAAE5O,IAAA,CAACrB,aAAa,EAACkD,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAAE,CAAE,CACjE,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CAEF;AACA,GAAI8L,QAAQ,CAACtK,MAAM,GAAK,CAAC,CAAE,CACzBsK,QAAQ,CAAC1C,IAAI,CAAC,CACZvC,KAAK,CAAE,oBAAoB,CAC3BkF,WAAW,CAAE,mEAAmE,CAChFC,IAAI,cAAE5O,IAAA,CAAClB,QAAQ,EAAC+C,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAK,CAAE,CAAE,CAC3D,CAAC,CAAC,CACJ,CAEA,MAAO,CAAA8L,QAAQ,CACjB,CAAC,CAED,KAAM,CAAAA,QAAQ,CAAGD,qBAAqB,CAAC,CAAC,CAExC,mBACEvO,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACjB5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAAC2M,YAAY,MAAAlN,QAAA,CAAC,kCAE/D,CAAY,CAAC,CAEZ8M,QAAQ,CAACnL,GAAG,CAAC,CAACwL,OAAO,CAAElH,KAAK,gBAC3B3H,KAAA,CAAChD,GAAG,EAEF2E,EAAE,CAAE,CACFC,OAAO,CAAE,MAAM,CACfG,EAAE,CAAE,GAAG,CACP2E,CAAC,CAAE,CAAC,CACJpF,YAAY,CAAE,CAAC,CACfF,eAAe,CAAE,qBACnB,CAAE,CAAAM,QAAA,eAEF5B,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CAAEgB,EAAE,CAAE,GAAG,CAAEJ,EAAE,CAAE,GAAI,CAAE,CAAAb,QAAA,CAC3BmN,OAAO,CAACH,IAAI,CACV,CAAC,cACN1O,KAAA,CAAChD,GAAG,EAAA0E,QAAA,eACF5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,CAC5CmN,OAAO,CAACtF,KAAK,CACJ,CAAC,cACbzJ,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CACjDmN,OAAO,CAACJ,WAAW,CACV,CAAC,EACV,CAAC,GAnBD9G,KAoBF,CACN,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACA,KAAM,CAAAmH,mBAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAACC,aAAa,CAAEC,gBAAgB,CAAC,CAAG9S,QAAQ,CAAC,CAAC,CAAC,CAAC,CAEtD,KAAM,CAAA+S,eAAe,CAAG7S,WAAW,CAAC,MAAOuJ,GAAG,CAAEE,GAAG,GAAK,CACtD;AACA,KAAM,CAAAqJ,QAAQ,IAAA1K,MAAA,CAAMmB,GAAG,CAACwJ,OAAO,CAAC,CAAC,CAAC,MAAA3K,MAAA,CAAIqB,GAAG,CAACsJ,OAAO,CAAC,CAAC,CAAC,CAAE,CACtD,GAAIJ,aAAa,CAACG,QAAQ,CAAC,CAAE,CAC3B,MAAO,CAAAH,aAAa,CAACG,QAAQ,CAAC,CAChC,CAEA,GAAI,CACF,KAAM,CAAA9K,QAAQ,CAAG,KAAM,CAAAxE,KAAK,CAACyE,GAAG,+CAAgD,CAC9EC,MAAM,CAAE,CACNqB,GAAG,CACHG,GAAG,CAAED,GAAG,CACRpB,MAAM,CAAE,MAAM,CACd2K,IAAI,CAAE,EAAE,CAAE;AACVxK,cAAc,CAAE,CAClB,CAAC,CACDC,OAAO,CAAE,CACP,iBAAiB,CAAE,gBAAgB,CACnC,YAAY,CAAE,wBAChB,CACF,CAAC,CAAC,CAEF,GAAIT,QAAQ,CAACU,IAAI,CAAE,CACjB;AACA,KAAM,CAAAuK,YAAY,CAAGjL,QAAQ,CAACU,IAAI,CAClC,GAAI,CAAAwK,SAAS,CAAG,EAAE,CAElB;AACA,GAAID,YAAY,CAAChJ,OAAO,CAAE,CACxBiJ,SAAS,CAAGD,YAAY,CAAChJ,OAAO,CAACiC,OAAO,EAC7B+G,YAAY,CAAChJ,OAAO,CAACgC,IAAI,EACzBgH,YAAY,CAAChJ,OAAO,CAAC+B,IAAI,EACzBiH,YAAY,CAAChJ,OAAO,CAACkJ,MAAM,EAC3BF,YAAY,CAAChJ,OAAO,CAACmJ,cAAc,EACnCH,YAAY,CAAChJ,OAAO,CAACkC,KAAK,CACvC,CAEA;AACA,GAAI,CAAC+G,SAAS,EAAID,YAAY,CAACnJ,YAAY,CAAE,CAC3CoJ,SAAS,CAAGD,YAAY,CAACnJ,YAAY,CAACsC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACrD,CAEA;AACA,GAAI,CAAC8G,SAAS,CAAE,CACdA,SAAS,gBAAA9K,MAAA,CAAkBmB,GAAG,CAACwJ,OAAO,CAAC,CAAC,CAAC,OAAA3K,MAAA,CAAKqB,GAAG,CAACsJ,OAAO,CAAC,CAAC,CAAC,CAAE,CAChE,CAEA;AACAH,gBAAgB,CAACS,IAAI,EAAA1O,aAAA,CAAAA,aAAA,IAChB0O,IAAI,MACP,CAACP,QAAQ,EAAG,CACVjJ,IAAI,CAAEqJ,SAAS,CACfI,QAAQ,CAAEL,YAAY,CAACnJ,YAAY,EAAI,EAAE,CACzCG,OAAO,CAAEgJ,YAAY,CAAChJ,OAAO,EAAI,CAAC,CACpC,CAAC,EACD,CAAC,CAEH,MAAO,CACLJ,IAAI,CAAEqJ,SAAS,CACfI,QAAQ,CAAEL,YAAY,CAACnJ,YAAY,EAAI,EAAE,CACzCG,OAAO,CAAEgJ,YAAY,CAAChJ,OAAO,EAAI,CAAC,CACpC,CAAC,CACH,CACF,CAAE,MAAO5D,KAAK,CAAE,CACduC,OAAO,CAACvC,KAAK,CAAC,6BAA6B,CAAEA,KAAK,CAAC,CACrD,CAEA;AACA,MAAO,CACLwD,IAAI,gBAAAzB,MAAA,CAAiBmB,GAAG,CAACwJ,OAAO,CAAC,CAAC,CAAC,OAAA3K,MAAA,CAAKqB,GAAG,CAACsJ,OAAO,CAAC,CAAC,CAAC,CAAE,CACxDO,QAAQ,CAAE,EAAE,CACZrJ,OAAO,CAAE,CAAC,CACZ,CAAC,CACH,CAAC,CAAE,CAAC0I,aAAa,CAAC,CAAC,CAEnB,MAAO,CAAEE,eAAe,CAAEF,aAAc,CAAC,CAC3C,CAAC,CAED;AACA,KAAM,CAAAY,aAAa,CAAGC,KAAA,EAAoE,IAAnE,CAAE/E,KAAK,CAAEgF,sBAAsB,CAAG,KAAK,CAAEC,YAAY,CAAG,IAAK,CAAC,CAAAF,KAAA,CACnF,KAAM,CAAAxP,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAsS,UAAU,CAAGlF,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAC/C,KAAM,CAAEmE,eAAe,CAAEF,aAAc,CAAC,CAAGD,mBAAmB,CAAC,CAAC,CAChE,KAAM,CAACpJ,QAAQ,CAAEsK,WAAW,CAAC,CAAG9T,QAAQ,CAAC,IAAI,CAAC,CAC9C,KAAM,CAAC+T,OAAO,CAAEC,UAAU,CAAC,CAAGhU,QAAQ,CAAC,CAAC4T,YAAY,CAAC,CAErD3T,SAAS,CAAC,IAAM,CACd,GAAI2T,YAAY,CAAE,CAChBE,WAAW,CAACF,YAAY,CAAC,CACzBI,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,KAAM,CAAAhB,QAAQ,IAAA1K,MAAA,CAAMqG,KAAK,CAACS,QAAQ,CAAC6D,OAAO,CAAC,CAAC,CAAC,MAAA3K,MAAA,CAAIqG,KAAK,CAACU,SAAS,CAAC4D,OAAO,CAAC,CAAC,CAAC,CAAE,CAC7E,GAAIJ,aAAa,CAACG,QAAQ,CAAC,CAAE,CAC3Bc,WAAW,CAACjB,aAAa,CAACG,QAAQ,CAAC,CAAC,CACpCgB,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CAEA;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpCD,UAAU,CAAC,IAAI,CAAC,CAChB,KAAM,CAAAxI,MAAM,CAAG,KAAM,CAAAuH,eAAe,CAACpE,KAAK,CAACS,QAAQ,CAAET,KAAK,CAACU,SAAS,CAAC,CACrEyE,WAAW,CAACtI,MAAM,CAAC,CACnBwI,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAEDC,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAACtF,KAAK,CAAEiF,YAAY,CAAEb,eAAe,CAAEF,aAAa,CAAC,CAAC,CAEzD;AACA,KAAM,CAAAT,WAAW,CAAG,EAAE,CACtB,GAAIzD,KAAK,CAAC,eAAe,CAAC,CAAG,GAAG,CAAE,CAChCyD,WAAW,CAACxC,IAAI,CAAC,CACf6C,MAAM,CAAE,gBAAgB,CACxBrJ,KAAK,IAAAd,MAAA,CAAKqG,KAAK,CAAC,eAAe,CAAC,OAAK,CACrC6D,IAAI,cAAE5O,IAAA,CAACnB,gBAAgB,EAAC0D,QAAQ,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAK,CAAE,CAAE,CAAC,CACnF4E,QAAQ,CAAE,MACZ,CAAC,CAAC,CACJ,CAEA,GAAIuD,KAAK,CAAC,eAAe,CAAC,CAAG,GAAG,CAAE,CAChCyD,WAAW,CAACxC,IAAI,CAAC,CACf6C,MAAM,CAAE,eAAe,CACvBrJ,KAAK,IAAAd,MAAA,CAAKqG,KAAK,CAAC,eAAe,CAAC,MAAI,CACpC6D,IAAI,cAAE5O,IAAA,CAACpB,WAAW,EAAC2D,QAAQ,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAK,CAAE,CAAE,CAAC,CACjF4E,QAAQ,CAAEuD,KAAK,CAAC,eAAe,CAAC,CAAG,EAAE,CAAG,MAAM,CAAG,QACnD,CAAC,CAAC,CACJ,CAEA,GAAIA,KAAK,CAAC,iBAAiB,CAAC,CAAG,CAAC,CAAE,CAChCyD,WAAW,CAACxC,IAAI,CAAC,CACf6C,MAAM,CAAE,kBAAkB,CAC1BrJ,KAAK,IAAAd,MAAA,CAAKqG,KAAK,CAAC,iBAAiB,CAAC,MAAI,CACtC6D,IAAI,cAAE5O,IAAA,CAACrB,aAAa,EAAC4D,QAAQ,CAAC,OAAO,CAACV,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAAE,CAAE,CAAC,CACjF4E,QAAQ,CAAEuD,KAAK,CAAC,iBAAiB,CAAC,CAAG,CAAC,CAAG,MAAM,CAAG,QACpD,CAAC,CAAC,CACJ,CAEA,mBACE7K,KAAA,CAAChD,GAAG,EACF2E,EAAE,CAAE,CACFF,KAAK,CAAE,MAAM,CACb+E,QAAQ,CAAEqJ,sBAAsB,CAAG,GAAG,CAAG,GAAG,CAC5CO,SAAS,CAAE,QACb,CAAE,CACFpD,SAAS,CAAC,eAAe,CAAAtL,QAAA,eAEzB1B,KAAA,CAAC/C,UAAU,EACT+E,OAAO,CAAC,WAAW,CACnBC,UAAU,CAAC,MAAM,CACjBN,EAAE,CAAE,CACFI,EAAE,CAAE,GAAG,CACPc,KAAK,CAAEkN,UAAU,CAAG3P,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CAAGtC,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAI,CACzEd,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpB2H,GAAG,CAAE,CAAC,CACNzI,QAAQ,CAAE,QAAQ,CAClBsF,GAAG,CAAE,CAAC,CACNlF,eAAe,CAAE,OAAO,CACxBD,MAAM,CAAE,EAAE,CACVkP,EAAE,CAAE,GACN,CAAE,CAAA3O,QAAA,eAEF5B,IAAA,CAACrB,aAAa,EAAC4D,QAAQ,CAAC,OAAO,CAAE,CAAC,CACjC0N,UAAU,CAAG,sBAAsB,CAAG,qBAAqB,EAClD,CAAC,CAEZE,OAAO,cACNjQ,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEQ,EAAE,CAAE,CAAE,CAAE,CAAAZ,QAAA,eACxD5B,IAAA,CAAC9C,GAAG,EACF2E,EAAE,CAAE,CACFF,KAAK,CAAE,EAAE,CACThB,MAAM,CAAE,EAAE,CACVa,YAAY,CAAE,KAAK,CACnB4F,MAAM,CAAE,uBAAuB,CAC/BC,cAAc,CAAE/G,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAC1C2E,SAAS,CAAE,yBAAyB,CACpC1E,EAAE,CAAE,CACN,CAAE,CACH,CAAC,cACF7C,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,qBAEnD,CAAY,CAAC,EACV,CAAC,CACJgE,QAAQ,cACV1F,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACjB5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,CAChDgE,QAAQ,CAACO,IAAI,CACJ,CAAC,CACZP,QAAQ,CAACgK,QAAQ,eAChB5P,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAClB,EAAE,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEG,EAAE,CAAE,GAAI,CAAE,CAAAL,QAAA,CACpFgE,QAAQ,CAACgK,QAAQ,CACR,CACb,EACE,CAAC,CACJ,IAAI,cAER5P,IAAA,CAACzC,OAAO,EAACsE,EAAE,CAAE,CAAEI,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cAE5B/B,KAAA,CAACxC,IAAI,EAAC8S,SAAS,MAACC,OAAO,CAAE,CAAE,CAAC5O,EAAE,CAAE,CAAEI,EAAE,CAAE,GAAI,CAAE,CAAAL,QAAA,eAC1C1B,KAAA,CAACxC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,eACf5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,UAErD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EAC5CmJ,KAAK,CAAC,eAAe,CAAC,CAAC,KAC1B,EAAY,CAAC,EACT,CAAC,cACP7K,KAAA,CAACxC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,eACf5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,WAErD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EAC5CmJ,KAAK,CAAC,eAAe,CAAC,CAAC,IAC1B,EAAY,CAAC,EACT,CAAC,cACP7K,KAAA,CAACxC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,eACf5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,aAErD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EAC5CmJ,KAAK,CAAC,iBAAiB,CAAC,CAAC,IAC5B,EAAY,CAAC,EACT,CAAC,cACP7K,KAAA,CAACxC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,eACf5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,aAErD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EAC5CmJ,KAAK,CAACS,QAAQ,CAAC6D,OAAO,CAAC,CAAC,CAAC,CAAC,IAAE,CAACtE,KAAK,CAACU,SAAS,CAAC4D,OAAO,CAAC,CAAC,CAAC,EAC9C,CAAC,EACT,CAAC,EACH,CAAC,CAENb,WAAW,CAACpK,MAAM,CAAG,CAAC,eACrBlE,KAAA,CAAAE,SAAA,EAAAwB,QAAA,eACE5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACa,KAAK,CAAC,gBAAgB,CAAClB,EAAE,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEG,EAAE,CAAE,GAAI,CAAE,CAAAL,QAAA,CAAC,eAExF,CAAY,CAAC,CAEZ4M,WAAW,CAACjL,GAAG,CAAC,CAACsL,MAAM,CAAE8B,GAAG,gBAC3BzQ,KAAA,CAAChD,GAAG,EAAW2E,EAAE,CAAE,CAAEC,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,GAAI,CAAE,CAAAL,QAAA,EACnEiN,MAAM,CAACD,IAAI,cACZ1O,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACL,EAAE,CAAE,CAAE+O,EAAE,CAAE,GAAI,CAAE,CAAAhP,QAAA,EACzCiN,MAAM,CAACA,MAAM,CAAC,IAAE,cAAA7O,IAAA,WAAA4B,QAAA,CAASiN,MAAM,CAACrJ,KAAK,CAAS,CAAC,EACtC,CAAC,GAJLmL,GAKL,CACN,CAAC,EACF,CACH,CAEAV,UAAU,EAAI,CAACF,sBAAsB,eACpC7P,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CACPY,EAAE,CAAE,GAAG,CACPmE,CAAC,CAAE,CAAC,CACJtF,eAAe,CAAE,wBAAwB,CACzCE,YAAY,CAAE,CAAC,CACfqP,UAAU,cAAAnM,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CACnD,CAAE,CAAAhB,QAAA,eACA5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,SAAS,CAACL,EAAE,CAAE,CAAEC,OAAO,CAAE,OAAO,CAAEK,UAAU,CAAE,QAAS,CAAE,CAAAP,QAAA,CAAC,iBAE9E,CAAY,CAAC,cACb5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,0FAE5B,CAAY,CAAC,EACV,CACN,CAEAmO,sBAAsB,eACrB/P,IAAA,CAACqO,sBAAsB,EACrBE,SAAS,CAAE0B,UAAU,CAAG,MAAM,CAAG,KAAM,CACvCzB,WAAW,CAAEA,WAAY,CAC1B,CACF,EACE,CAAC,CAEV,CAAC,CAED,KAAM,CAAAsC,QAAQ,CAAGC,KAAA,EAAiB,IAAhB,CAAErG,OAAQ,CAAC,CAAAqG,KAAA,CAC3B,KAAM,CAAAzQ,KAAK,CAAG3C,QAAQ,CAAC,CAAC,CACxB,KAAM,CAACwS,OAAO,CAAEC,UAAU,CAAC,CAAGhU,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAAC4U,gBAAgB,CAAEC,mBAAmB,CAAC,CAAG7U,QAAQ,CAAC,IAAI,CAAC,CAC9D,KAAM,CAAC8U,YAAY,CAAEC,eAAe,CAAC,CAAG/U,QAAQ,CAAC,IAAI,CAAC,CACtD,KAAM,CAACgV,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGjV,QAAQ,CAAC,KAAK,CAAC,CAC/D,KAAM,CAAC2M,QAAQ,CAAEuI,WAAW,CAAC,CAAGlV,QAAQ,CAAC,MAAM,CAAC,CAAE;AAClD,KAAM,CAACmV,YAAY,CAAEC,eAAe,CAAC,CAAGpV,QAAQ,CAAC,CAAEqV,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAE,CAAElK,QAAQ,CAAE,MAAO,CAAC,CAAC,CAEhGnL,SAAS,CAAC,IAAM,CACd,GAAIqO,OAAO,EAAIA,OAAO,CAACtG,MAAM,CAAG,CAAC,CAAE,CACjC;AACA,KAAM,CAAAuN,KAAK,CAAGxM,UAAU,CAAC,IAAM,CAC7BiL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,IAAI,CAAC,CAER,MAAO,IAAM,CACXhL,YAAY,CAACuM,KAAK,CAAC,CACnB;AACAvB,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CACH,CAAC,IAAM,CACL;AACA,KAAM,CAAAuB,KAAK,CAAGxM,UAAU,CAAC,IAAM,CAC7BiL,UAAU,CAAC,KAAK,CAAC,CACnB,CAAC,CAAE,GAAG,CAAC,CAEP,MAAO,IAAMhL,YAAY,CAACuM,KAAK,CAAC,CAClC,CACF,CAAC,CAAE,CAACjH,OAAO,CAAC,CAAC,CAEb;AACA,KAAM,CAAAkH,gBAAgB,CAAGtV,WAAW,CAAEsJ,QAAQ,EAAK,CACjD,GAAI,CAAC8E,OAAO,EAAIA,OAAO,CAACtG,MAAM,GAAK,CAAC,CAAE,MAAO,KAAI,CAEjD,GAAI,CAAAyN,OAAO,CAAG,IAAI,CAClB,GAAI,CAAAC,WAAW,CAAGnF,QAAQ,CAE1BjC,OAAO,CAACU,OAAO,CAACL,KAAK,EAAI,CACvB,KAAM,CAAAa,QAAQ,CAAGC,IAAI,CAACC,IAAI,CACxBD,IAAI,CAACE,GAAG,CAAChB,KAAK,CAACS,QAAQ,CAAG5F,QAAQ,CAACC,GAAG,CAAE,CAAC,CAAC,CAC1CgG,IAAI,CAACE,GAAG,CAAChB,KAAK,CAACU,SAAS,CAAG7F,QAAQ,CAACG,GAAG,CAAE,CAAC,CAC5C,CAAC,CAED,GAAI6F,QAAQ,CAAGkG,WAAW,CAAE,CAC1BA,WAAW,CAAGlG,QAAQ,CACtBiG,OAAO,CAAG9G,KAAK,CACjB,CACF,CAAC,CAAC,CAEF;AACA,GAAI+G,WAAW,CAAG,GAAG,CAAE,CACrBN,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,8EAA8E,CACvFlK,QAAQ,CAAE,MACZ,CAAC,CAAC,CACJ,CAEA,MAAO,CAAAqK,OAAO,CAChB,CAAC,CAAE,CAACnH,OAAO,CAAC,CAAC,CAEb;AACA,KAAM,CAAAqH,oBAAoB,CAAGzV,WAAW,CAAEsJ,QAAQ,EAAK,CACrDqL,mBAAmB,CAACrL,QAAQ,CAAC,CAC7B,KAAM,CAAAiM,OAAO,CAAGD,gBAAgB,CAAChM,QAAQ,CAAC,CAC1CuL,eAAe,CAACU,OAAO,CAAC,CACxBR,mBAAmB,CAAC,IAAI,CAAC,CAC3B,CAAC,CAAE,CAACO,gBAAgB,CAAC,CAAC,CAEtB;AACA,KAAM,CAAAI,uBAAuB,CAAGA,CAAA,GAAM,CACpCR,eAAe,CAAC7B,IAAI,EAAA1O,aAAA,CAAAA,aAAA,IAAU0O,IAAI,MAAE8B,IAAI,CAAE,KAAK,EAAG,CAAC,CACrD,CAAC,CAED;AACA,KAAM,CAAAQ,oBAAoB,CAAI7I,QAAQ,EAAK,CACzCkI,WAAW,CAAClI,QAAQ,CAAC,CACvB,CAAC,CAED,GAAI,CAACsB,OAAO,EAAIA,OAAO,CAACtG,MAAM,GAAK,CAAC,CAAE,CACpC,mBACEpE,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CAAEF,KAAK,CAAE,MAAM,CAAEhB,MAAM,CAAE,GAAG,CAAEa,YAAY,CAAE,CAAE,CAAE,CAAAI,QAAA,cACvD5B,IAAA,CAAC5C,QAAQ,EAAC8E,OAAO,CAAC,aAAa,CAACP,KAAK,CAAC,MAAM,CAAChB,MAAM,CAAC,MAAM,CAAE,CAAC,CAC1D,CAAC,CAEV,CAEA;AACA,KAAM,CAAAuR,aAAa,CAAGxH,OAAO,CAACI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAAC5G,MAAM,CAClF,KAAM,CAAA+N,YAAY,CAAGzH,OAAO,CAACI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAAC5G,MAAM,CAEjF;AACA,KAAM,CAAAgO,WAAW,CAAG1H,OAAO,CAACyB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,eAAe,CAAC,CAAE,CAAC,CAAC,CAAGL,OAAO,CAACtG,MAAM,CACpG,KAAM,CAAAkO,YAAY,CAAG5H,OAAO,CAACyB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,eAAe,CAAC,CAAE,CAAC,CAAC,CAAGL,OAAO,CAACtG,MAAM,CAErG,mBACElE,KAAA,CAAAE,SAAA,EAAAwB,QAAA,eACE5B,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACjB1B,KAAA,CAACxC,IAAI,EAAC8S,SAAS,MAACC,OAAO,CAAE,CAAE7G,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAE0I,EAAE,CAAE,CAAE,CAAE,CAAA3Q,QAAA,eAC/C5B,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,cAC9B1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,CAAEgD,EAAE,CAAE,GAAG,CAAEC,EAAE,CAAE,CAAE,CAAC,CACrBrI,YAAY,CAAE,CAAC,CACfgR,UAAU,4BAAA9N,MAAA,CAA6BpE,KAAK,CAACoC,OAAO,CAACC,KAAK,CAAC2G,KAAK,YAAA5E,MAAA,CAAUpE,KAAK,CAACoC,OAAO,CAACC,KAAK,CAAC2G,KAAK,YAAU,CAC7GuH,UAAU,cAAAnM,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CAAE,CACnDjC,MAAM,CAAE,MAAM,CACdmB,OAAO,CAAE,MAAM,CACfyI,aAAa,CAAE,QAAQ,CACvBxI,cAAc,CAAE,QAAQ,CACxBO,UAAU,CAAE,eAAe,CAC3B,SAAS,CAAE,CACTiH,SAAS,CAAE,kBAAkB,CAC7B9H,SAAS,CAAE,gCACb,CACF,CAAE,CAAAG,QAAA,eAEF5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACa,KAAK,CAAC,gBAAgB,CAAClB,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,CAAC,iBAEtE,CAAY,CAAC,cACb5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAAAhB,QAAA,CACxEsQ,aAAa,CACJ,CAAC,EACR,CAAC,CACJ,CAAC,cAEPlS,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,cAC9B1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,CAAC,CACJpF,YAAY,CAAE,CAAC,CACfgR,UAAU,4BAAA9N,MAAA,CAA6BpE,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACwG,KAAK,YAAA5E,MAAA,CAAUpE,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACwG,KAAK,YAAU,CACjHuH,UAAU,cAAAnM,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAI,CACrD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,gBAEvD,CAAY,CAAC,cACb5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAK,CAAAhB,QAAA,CAC1EuQ,YAAY,CACH,CAAC,EACR,CAAC,CACJ,CAAC,cAEPnS,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,cAC9B1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,CAAC,CACJpF,YAAY,CAAE,CAAC,CACfgR,UAAU,4BAAA9N,MAAA,CAA6BpE,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACsG,KAAK,YAAA5E,MAAA,CAAUpE,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACsG,KAAK,YAAU,CAC3GuH,UAAU,cAAAnM,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAI,CAClD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,eAEvD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAK,CAAAhB,QAAA,EACvEwQ,WAAW,CAAC/C,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,cAAArP,IAAA,CAAC7C,UAAU,EAACsV,SAAS,CAAC,MAAM,CAACvQ,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,IAAE,CAAY,CAAC,EAC3E,CAAC,EACR,CAAC,CACJ,CAAC,cAEP5B,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAC0I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,cAC9B1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,CAAC,CACJpF,YAAY,CAAE,CAAC,CACfgR,UAAU,4BAAA9N,MAAA,CAA6BpE,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACqG,KAAK,YAAA5E,MAAA,CAAUpE,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACqG,KAAK,YAAU,CACjHuH,UAAU,cAAAnM,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAI,CACrD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,gBAEvD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAK,CAAAhB,QAAA,EAC1E0Q,YAAY,CAACjD,OAAO,CAAC,CAAC,CAAC,CAAC,GAAC,cAAArP,IAAA,CAAC7C,UAAU,EAACsV,SAAS,CAAC,MAAM,CAACvQ,OAAO,CAAC,OAAO,CAAAN,QAAA,CAAC,GAAC,CAAY,CAAC,EAC3E,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,CACJ,CAAC,cAEN1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEX,QAAQ,CAAE,UAAU,CAAEP,MAAM,CAAE,GAAG,CAAEa,YAAY,CAAE,CAAC,CAAEE,QAAQ,CAAE,QAAS,CAAE,CAAAE,QAAA,EACjFuO,OAAO,eACNnQ,IAAA,CAACjC,IAAI,EAAC2J,EAAE,CAAEyI,OAAQ,CAACuC,OAAO,CAAE,GAAI,CAAA9Q,QAAA,cAC9B5B,IAAA,CAAC9C,GAAG,EACF2E,EAAE,CAAE,CACFX,QAAQ,CAAE,UAAU,CACpBsF,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPrF,KAAK,CAAE,CAAC,CACRD,MAAM,CAAE,CAAC,CACTW,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxBT,eAAe,CAAE,uBAAuB,CACxCD,MAAM,CAAE,IACV,CAAE,CAAAO,QAAA,cAEF1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAE8Q,SAAS,CAAE,QAAS,CAAE,CAAA/Q,QAAA,eAC/B5B,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CAAEX,QAAQ,CAAE,UAAU,CAAES,KAAK,CAAE,EAAE,CAAEhB,MAAM,CAAE,EAAE,CAAE0I,MAAM,CAAE,QAAS,CAAE,CAAAzH,QAAA,cACzE5B,IAAA,CAAC9C,GAAG,EACF2E,EAAE,CAAE,CACFX,QAAQ,CAAE,UAAU,CACpBsF,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACP9E,KAAK,CAAE,MAAM,CACbhB,MAAM,CAAE,MAAM,CACda,YAAY,CAAE,KAAK,CACnB4F,MAAM,CAAE,uBAAuB,CAC/BC,cAAc,CAAE/G,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAC1C2E,SAAS,CAAE,yBAAyB,CACpC,iBAAiB,CAAE,CACjB,IAAI,CAAE,CAAEgC,SAAS,CAAE,cAAe,CAAC,CACnC,MAAM,CAAE,CAAEA,SAAS,CAAE,gBAAiB,CACxC,CACF,CAAE,CACH,CAAC,CACC,CAAC,cACNvJ,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACL,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CAAC,qBAE3C,CAAY,CAAC,EACV,CAAC,CACH,CAAC,CACF,CACP,cAED1B,KAAA,CAAC3D,YAAY,EACXqW,MAAM,CAAE,CAAC,IAAI,CAAE,IAAI,CAAE,CACrBtD,IAAI,CAAE,CAAE,CACRuD,eAAe,CAAE,IAAK,CACtB7R,KAAK,CAAE,CAAEL,MAAM,CAAE,MAAM,CAAEgB,KAAK,CAAE,MAAM,CAAEH,YAAY,CAAE,CAAE,CAAE,CAC1DsR,WAAW,CAAE,KAAM,CAAAlR,QAAA,EAGlBmH,QAAQ,GAAK,MAAM,eAClB7I,KAAA,CAAAE,SAAA,EAAAwB,QAAA,eACE5B,IAAA,CAACxD,SAAS,EACRuW,WAAW,CAAC,qJAAqJ,CACjKC,GAAG,CAAC,+DAA+D,CACnEC,UAAU,CAAC,MAAM,CACjB3I,OAAO,CAAE,EAAG,CACb,CAAC,cACFtK,IAAA,CAACxD,SAAS,EACRuW,WAAW,CAAC,6DAA6D,CACzEC,GAAG,CAAC,8KAA8K,CAClLtS,OAAO,CAAE,GAAI,CACb4J,OAAO,CAAE,EAAG,CACb,CAAC,EACF,CACH,CAEAvB,QAAQ,GAAK,WAAW,eACvB/I,IAAA,CAACxD,SAAS,EACRuW,WAAW,CAAC,6DAA6D,CACzEC,GAAG,CAAC,8KAA8K,CAClL1I,OAAO,CAAE,EAAG,CACb,CACF,CAEAvB,QAAQ,GAAK,SAAS,eACrB/I,IAAA,CAACxD,SAAS,EACRuW,WAAW,CAAC,yFAAyF,CACrGC,GAAG,CAAC,kDAAkD,CACtD1I,OAAO,CAAE,EAAG,CACb,CACF,cAGDtK,IAAA,CAACpD,UAAU,EAAAgF,QAAA,CACR8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CzH,GAAG,CAAC,CAACwH,KAAK,CAAElD,KAAK,gBAChB7H,IAAA,CAACvD,YAAY,EAEXmW,MAAM,CAAE,CAAC7H,KAAK,CAACS,QAAQ,CAAET,KAAK,CAACU,SAAS,CAAE,CAC1CyH,MAAM,CAAE,CAAE,CACVC,WAAW,CAAE,CACXpQ,KAAK,CAAE,SAAS,CAChBgK,SAAS,CAAE,SAAS,CACpBC,WAAW,CAAE,GAAG,CAChBC,MAAM,CAAE,CAAC,CACTvM,OAAO,CAAE,CAAC,CACVwM,SAAS,CAAE,iCACb,CAAE,CACFkG,aAAa,CAAE,CACbC,SAAS,CAAG/N,CAAC,EAAK,CAChBA,CAAC,CAACC,MAAM,CAAC+N,QAAQ,CAAC,CAChBJ,MAAM,CAAE,EAAE,CACVlG,WAAW,CAAE,GAAG,CAChBC,MAAM,CAAE,CACV,CAAC,CAAC,CACJ,CAAC,CACDsG,QAAQ,CAAGjO,CAAC,EAAK,CACfA,CAAC,CAACC,MAAM,CAAC+N,QAAQ,CAAC,CAChBJ,MAAM,CAAE,CAAC,CACTlG,WAAW,CAAE,GAAG,CAChBC,MAAM,CAAE,CACV,CAAC,CAAC,CACJ,CACF,CAAE,CAAArL,QAAA,cAEF5B,IAAA,CAACtD,KAAK,EACJ2L,QAAQ,CAAE,GAAI,CACd3B,QAAQ,CAAE,GAAI,CACdiB,SAAS,CAAE,GAAI,CACf6L,OAAO,CAAE,IAAK,CACdC,cAAc,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CACzBvG,SAAS,CAAC,iCAAiC,CAAAtL,QAAA,cAE3C5B,IAAA,CAAC6P,aAAa,EAAC9E,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC1B,CAAC,UAAArG,MAAA,CArCKmD,KAAK,CAsCN,CACf,CAAC,CACM,CAAC,cAGb7H,IAAA,CAACpD,UAAU,EAAAgF,QAAA,CACR8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CzH,GAAG,CAAC,CAACwH,KAAK,CAAElD,KAAK,gBAChB7H,IAAA,CAACvD,YAAY,EAEXmW,MAAM,CAAE,CAAC7H,KAAK,CAACS,QAAQ,CAAET,KAAK,CAACU,SAAS,CAAE,CAC1CyH,MAAM,CAAE,CAAE,CACVC,WAAW,CAAE,CACXpQ,KAAK,CAAE,SAAS,CAChBgK,SAAS,CAAE,SAAS,CACpBC,WAAW,CAAE,GAAG,CAChBC,MAAM,CAAE,CAAC,CACTvM,OAAO,CAAE,GAAG,CACZwM,SAAS,CAAE,iBACb,CAAE,CACFkG,aAAa,CAAE,CACbC,SAAS,CAAG/N,CAAC,EAAK,CAChBA,CAAC,CAACC,MAAM,CAAC+N,QAAQ,CAAC,CAChBJ,MAAM,CAAE,CAAC,CACTlG,WAAW,CAAE,IAAI,CACjBC,MAAM,CAAE,CACV,CAAC,CAAC,CACJ,CAAC,CACDsG,QAAQ,CAAGjO,CAAC,EAAK,CACfA,CAAC,CAACC,MAAM,CAAC+N,QAAQ,CAAC,CAChBJ,MAAM,CAAE,CAAC,CACTlG,WAAW,CAAE,GAAG,CAChBC,MAAM,CAAE,CACV,CAAC,CAAC,CACJ,CACF,CAAE,CAAArL,QAAA,cAEF5B,IAAA,CAACtD,KAAK,EACJ2L,QAAQ,CAAE,GAAI,CACd3B,QAAQ,CAAE,GAAI,CACdiB,SAAS,CAAE,GAAI,CACf6L,OAAO,CAAE,IAAK,CACdC,cAAc,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CACzBvG,SAAS,CAAC,iCAAiC,CAAAtL,QAAA,cAE3C5B,IAAA,CAAC6P,aAAa,EAAC9E,KAAK,CAAEA,KAAM,CAAE,CAAC,CAC1B,CAAC,SAAArG,MAAA,CArCImD,KAAK,CAsCL,CACf,CAAC,CACM,CAAC,CAGZmJ,gBAAgB,EAAIE,YAAY,eAC/BlR,IAAA,CAACpD,UAAU,EAAAgF,QAAA,cAET5B,IAAA,CAACjD,MAAM,EACLmE,QAAQ,CAAE,CAAC8P,gBAAgB,CAACnL,GAAG,CAAEmL,gBAAgB,CAACjL,GAAG,CAAE,CACvD6I,IAAI,CAAE3R,CAAC,CAACyW,OAAO,CAAC,CACdxG,SAAS,CAAE,wCAAwC,CACnDyG,IAAI,weAUH,CACDC,QAAQ,CAAE,CAAC,EAAE,CAAE,EAAE,CAAC,CAClBC,UAAU,CAAE,CAAC,CAAC,CAAE,CAAC,CACnB,CAAC,CAAE,CAAAjS,QAAA,cAEH5B,IAAA,CAACtD,KAAK,EACJ2L,QAAQ,CAAE,GAAI,CACd3B,QAAQ,CAAE,GAAI,CACdiB,SAAS,CAAE,GAAI,CACf6L,OAAO,CAAE,IAAK,CACdC,cAAc,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,CACzBvG,SAAS,CAAC,kBAAkB,CAAAtL,QAAA,cAE5B1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAE8F,SAAS,CAAE,OAAO,CAAEmM,SAAS,CAAE,MAAO,CAAE,CAAAlS,QAAA,eACjD5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAAC2M,YAAY,MAAAlN,QAAA,CAC3DoP,gBAAgB,CAAC7K,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC1B,CAAC,cACb1I,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAACgR,SAAS,MAAAnS,QAAA,CACzDoP,gBAAgB,CAAC7K,IAAI,CACZ,CAAC,cACbnG,IAAA,CAACzC,OAAO,EAACsE,EAAE,CAAE,CAAEW,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cAC5BtC,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAC6R,SAAS,MAAAnS,QAAA,EAAC,0DACoB,CAAC,CAACiK,IAAI,CAACC,IAAI,CACjED,IAAI,CAACE,GAAG,CAACmF,YAAY,CAAC1F,QAAQ,CAAGwF,gBAAgB,CAACnL,GAAG,CAAE,CAAC,CAAC,CACzDgG,IAAI,CAACE,GAAG,CAACmF,YAAY,CAACzF,SAAS,CAAGuF,gBAAgB,CAACjL,GAAG,CAAE,CAAC,CAC3D,CAAC,CAAG,GAAG,EAAEsJ,OAAO,CAAC,CAAC,CAAC,CAAC,YACtB,EAAY,CAAC,cACbrP,IAAA,CAAC6P,aAAa,EAAC9E,KAAK,CAAEmG,YAAa,CAACnB,sBAAsB,CAAE,IAAK,CAAE,CAAC,EACjE,CAAC,CACD,CAAC,CACF,CAAC,CAIC,CACb,cAGD/P,IAAA,CAACwK,WAAW,EAACE,OAAO,CAAEA,OAAQ,CAAE,CAAC,cAGjC1K,IAAA,CAAC2N,YAAY,EAACjD,OAAO,CAAEA,OAAQ,CAAE,CAAC,cAGlC1K,IAAA,CAACoD,cAAc,EAACE,gBAAgB,CAAEyO,oBAAqB,CAAE,CAAC,cAG1D/R,IAAA,CAAC6I,cAAc,EAACE,QAAQ,CAAEA,QAAS,CAACC,aAAa,CAAEiJ,oBAAqB,CAAE,CAAC,cAG3EjS,IAAA,CAAC8J,WAAW,GAAE,CAAC,cAGf9J,IAAA,CAACK,SAAS,GAAE,CAAC,EACD,CAAC,cAGfL,IAAA,CAACxB,QAAQ,EACPiT,IAAI,CAAEF,YAAY,CAACE,IAAK,CACxBuC,gBAAgB,CAAE,IAAK,CACvBvM,OAAO,CAAEuK,uBAAwB,CACjCiC,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAAvS,QAAA,cAE3D5B,IAAA,CAAC1B,KAAK,EACJmJ,OAAO,CAAEuK,uBAAwB,CACjCxK,QAAQ,CAAE+J,YAAY,CAAC/J,QAAS,CAChC3F,EAAE,CAAE,CAAEF,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,CAErB2P,YAAY,CAACG,OAAO,CAChB,CAAC,CACA,CAAC,EACR,CAAC,CAGLV,gBAAgB,EAAIE,YAAY,EAAIE,gBAAgB,eACnDpR,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAC,CAAER,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,cACxB1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,CAAC,CACJpF,YAAY,CAAE,CAAC,CACfgR,UAAU,4BAAA9N,MAAA,CAA6BpE,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAACgC,KAAK,YAAA5E,MAAA,CAAUpE,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAACgC,KAAK,YAAU,CACjHpI,QAAQ,CAAE,UAAU,CACpBQ,QAAQ,CAAE,QACZ,CAAE,CAAAE,QAAA,eAEF5B,IAAA,CAAC9C,GAAG,EACF2E,EAAE,CAAE,CACFX,QAAQ,CAAE,UAAU,CACpBsF,GAAG,CAAE,CAAC,CACNpF,KAAK,CAAE,CAAC,CACRO,KAAK,CAAE,OAAO,CACdhB,MAAM,CAAE,OAAO,CACf6R,UAAU,CAAE,sEAAsE,CAClFhR,YAAY,CAAE,YAAY,CAC1BH,MAAM,CAAE,CACV,CAAE,CACH,CAAC,cAEFnB,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEX,QAAQ,CAAE,UAAU,CAAEG,MAAM,CAAE,CAAE,CAAE,CAAAO,QAAA,eAC3C1B,KAAA,CAAC/C,UAAU,EACT+E,OAAO,CAAC,IAAI,CACZuQ,SAAS,CAAC,IAAI,CACd3D,YAAY,MACZjN,EAAE,CAAE,CACFM,UAAU,CAAE,GAAG,CACfY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CACjCd,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpB2H,GAAG,CAAE,CACP,CAAE,CAAA/H,QAAA,eAEF5B,IAAA,CAACT,cAAc,GAAE,CAAC,IAAC,CAACyR,gBAAgB,CAAC7K,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAC7C,CAAC,cAEb1I,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAC6R,SAAS,MAAChR,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CACzDoP,gBAAgB,CAAC7K,IAAI,CACZ,CAAC,cAEbnG,IAAA,CAACzC,OAAO,EAACsE,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BtC,KAAA,CAACxC,IAAI,EAAC8S,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA7O,QAAA,eACzB1B,KAAA,CAACxC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAAC2I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,eACvB5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAAC4M,YAAY,MAAC3M,UAAU,CAAC,QAAQ,CAAAP,QAAA,CAAC,uBAE1D,CAAY,CAAC,cAEb1B,KAAA,CAAChD,GAAG,EACF2E,EAAE,CAAE,CACF+E,CAAC,CAAE,CAAC,CACJpF,YAAY,CAAE,CAAC,CACfF,eAAe,CAAE4P,YAAY,CAAClG,gBAAgB,GAAK,CAAC,CAChD,wBAAwB,CACxB,wBAAwB,CAC5B6F,UAAU,cAAAnM,MAAA,CAAewM,YAAY,CAAClG,gBAAgB,GAAK,CAAC,CACxD1K,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CACxBtC,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAI,CAAE,CAChCX,EAAE,CAAE,CACN,CAAE,CAAAL,QAAA,eAEF5B,IAAA,CAAC7C,UAAU,EACT+E,OAAO,CAAC,WAAW,CACnBC,UAAU,CAAC,MAAM,CACjBN,EAAE,CAAE,CACFkB,KAAK,CAAEmO,YAAY,CAAClG,gBAAgB,GAAK,CAAC,CACtC1K,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CACxBtC,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAC5B,CAAE,CAAAhB,QAAA,CAEDsP,YAAY,CAAClG,gBAAgB,GAAK,CAAC,CAChC,sBAAsB,CACtB,qBAAqB,CACf,CAAC,cAEbhL,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACL,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACvCsP,YAAY,CAAClG,gBAAgB,GAAK,CAAC,CAChC,mIAAmI,CACnI,0IAA0I,CACpI,CAAC,EACV,CAAC,cAENhL,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAAC4M,YAAY,MAAAlN,QAAA,CAAC,wBAE7C,CAAY,CAAC,cAEb1B,KAAA,CAACxC,IAAI,EAAC8S,SAAS,MAACC,OAAO,CAAE,CAAE,CAAC5O,EAAE,CAAE,CAAEI,EAAE,CAAE,CAAE,CAAE,CAAAL,QAAA,eACxC5B,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,cACf1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,GAAG,CACN+L,SAAS,CAAE,QAAQ,CACnByB,SAAS,cAAA1P,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAI,CACjD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAACnB,gBAAgB,EAACgD,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACM,IAAI,CAACJ,IAAI,CAAEX,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cACrEjC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,UAEnD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EACzCsP,YAAY,CAAC,eAAe,CAAC,CAAC,KACjC,EAAY,CAAC,EACR,CAAC,CACJ,CAAC,cAEPlR,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,cACf1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,GAAG,CACN+L,SAAS,CAAE,QAAQ,CACnByB,SAAS,cAAA1P,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAI,CACpD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAACpB,WAAW,EAACiD,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACO,OAAO,CAACL,IAAI,CAAEX,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cACnEjC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,WAEnD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EACzCsP,YAAY,CAAC,eAAe,CAAC,CAAC,IACjC,EAAY,CAAC,EACR,CAAC,CACJ,CAAC,cAEPlR,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,cACf1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,GAAG,CACN+L,SAAS,CAAE,QAAQ,CACnByB,SAAS,cAAA1P,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CAClD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAACrB,aAAa,EAACkD,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAI,CAAEX,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cACnEjC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,aAEnD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EACzCsP,YAAY,CAAC,iBAAiB,CAAC,CAAC,IACnC,EAAY,CAAC,EACR,CAAC,CACJ,CAAC,cAEPlR,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,CAAE,CAAAhI,QAAA,cACf1B,KAAA,CAAC7C,KAAK,EACJsJ,SAAS,CAAE,CAAE,CACb9E,EAAE,CAAE,CACF+E,CAAC,CAAE,GAAG,CACN+L,SAAS,CAAE,QAAQ,CACnByB,SAAS,cAAA1P,MAAA,CAAepE,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CACpD,CAAE,CAAAhB,QAAA,eAEF5B,IAAA,CAACT,cAAc,EAACsC,EAAE,CAAE,CAAEkB,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CAAEX,EAAE,CAAE,GAAI,CAAE,CAAE,CAAC,cACtEjC,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAACa,KAAK,CAAC,gBAAgB,CAAAnB,QAAA,CAAC,UAEnD,CAAY,CAAC,cACb1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAAP,QAAA,EACzC,CAACiK,IAAI,CAACC,IAAI,CACTD,IAAI,CAACE,GAAG,CAACmF,YAAY,CAAC1F,QAAQ,CAAGwF,gBAAgB,CAACnL,GAAG,CAAE,CAAC,CAAC,CACzDgG,IAAI,CAACE,GAAG,CAACmF,YAAY,CAACzF,SAAS,CAAGuF,gBAAgB,CAACjL,GAAG,CAAE,CAAC,CAC3D,CAAC,CAAG,GAAG,EAAEsJ,OAAO,CAAC,CAAC,CAAC,CAAC,KACtB,EAAY,CAAC,EACR,CAAC,CACJ,CAAC,EACH,CAAC,EACH,CAAC,cAEPnP,KAAA,CAACxC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAAC2I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,eACvB5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAAC4M,YAAY,MAAC3M,UAAU,CAAC,QAAQ,CAAAP,QAAA,CAAC,iCAE1D,CAAY,CAAC,cAEb5B,IAAA,CAACqO,sBAAsB,EACrBE,SAAS,CAAE2C,YAAY,CAAClG,gBAAgB,GAAK,CAAC,CAAG,MAAM,CAAG,KAAM,CAChEwD,WAAW,CAAE,CACX,IAAI0C,YAAY,CAAC,eAAe,CAAC,CAAG,GAAG,CAAG,CAAC,CACzCrC,MAAM,CAAE,gBAAgB,CACxBrJ,KAAK,IAAAd,MAAA,CAAKwM,YAAY,CAAC,eAAe,CAAC,OAAK,CAC5C1J,QAAQ,CAAE,MACZ,CAAC,CAAC,CAAG,EAAE,CAAC,CACR,IAAI0J,YAAY,CAAC,eAAe,CAAC,CAAG,GAAG,CAAG,CAAC,CACzCrC,MAAM,CAAE,eAAe,CACvBrJ,KAAK,IAAAd,MAAA,CAAKwM,YAAY,CAAC,eAAe,CAAC,MAAI,CAC3C1J,QAAQ,CAAE0J,YAAY,CAAC,eAAe,CAAC,CAAG,EAAE,CAAG,MAAM,CAAG,QAC1D,CAAC,CAAC,CAAG,EAAE,CAAC,CACR,IAAIA,YAAY,CAAC,iBAAiB,CAAC,CAAG,CAAC,CAAG,CAAC,CACzCrC,MAAM,CAAE,kBAAkB,CAC1BrJ,KAAK,IAAAd,MAAA,CAAKwM,YAAY,CAAC,iBAAiB,CAAC,MAAI,CAC7C1J,QAAQ,CAAE0J,YAAY,CAAC,iBAAiB,CAAC,CAAG,CAAC,CAAG,MAAM,CAAG,QAC3D,CAAC,CAAC,CAAG,EAAE,CAAC,CACR,CACH,CAAC,cAEFlR,IAAA,CAAC9C,GAAG,EAAC2E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,cACjB5B,IAAA,CAACpC,MAAM,EACLsE,OAAO,CAAC,UAAU,CAClBa,KAAK,CAAC,SAAS,CACfV,OAAO,CAAEA,CAAA,GAAMgP,mBAAmB,CAAC,KAAK,CAAE,CAC1CgD,SAAS,cAAErU,IAAA,CAACR,SAAS,GAAE,CAAE,CACzBqC,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,CACf,yBAED,CAAQ,CAAC,CACN,CAAC,EACF,CAAC,EACH,CAAC,EACJ,CAAC,EACD,CAAC,CACL,CACN,cAED1B,KAAA,CAAChD,GAAG,EAAC2E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAb,QAAA,eACjB5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,IAAI,CAAC4M,YAAY,MAAC3M,UAAU,CAAC,QAAQ,CAAAP,QAAA,CAAC,qBAE1D,CAAY,CAAC,cAEb1B,KAAA,CAACxC,IAAI,EAAC8S,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA7O,QAAA,eACzB5B,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAAC2I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,cACvB1B,KAAA,CAAC7C,KAAK,EAACsJ,SAAS,CAAE,CAAE,CAAC9E,EAAE,CAAE,CAAE+E,CAAC,CAAE,CAAC,CAAEpF,YAAY,CAAE,CAAE,CAAE,CAAAI,QAAA,eACjD1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAACY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACC,KAAK,CAACC,IAAK,CAACkM,YAAY,MAAAlN,QAAA,EAAC,mBAC7E,CAACsQ,aAAa,CAAC,GAClC,EAAY,CAAC,cACblS,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAC6R,SAAS,MAAAnS,QAAA,CAAC,6MAItC,CAAY,CAAC,cACb5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,cACzB5B,IAAA,WAAA4B,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,CAC3B,CAAC,cACb1B,KAAA,OAAIc,KAAK,CAAE,CAAEsT,WAAW,CAAE,MAAM,CAAEjL,MAAM,CAAE,OAAQ,CAAE,CAAAzH,QAAA,eAClD5B,IAAA,OAAA4B,QAAA,cACE1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,oBACR,CAChB,CAAC8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CmB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,eAAe,CAAC,CAAE,CAAC,CAAC,EACzDmH,aAAa,EAAI,CAAC,CAAC,EAAE7C,OAAO,CAAC,CAAC,CAAC,CACjC,KACH,EAAY,CAAC,CACX,CAAC,cACLrP,IAAA,OAAA4B,QAAA,cACE1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,qBACP,CACjB,CAAC8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CmB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,eAAe,CAAC,CAAE,CAAC,CAAC,EACzDmH,aAAa,EAAI,CAAC,CAAC,EAAE7C,OAAO,CAAC,CAAC,CAAC,CACjC,IACH,EAAY,CAAC,CACX,CAAC,cACLrP,IAAA,OAAA4B,QAAA,cACE1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,uBACL,CACnB,CAAC8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CmB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,iBAAiB,CAAC,CAAE,CAAC,CAAC,EAC3DmH,aAAa,EAAI,CAAC,CAAC,EAAE7C,OAAO,CAAC,CAAC,CAAC,CACjC,IACH,EAAY,CAAC,CACX,CAAC,EACH,CAAC,EACA,CAAC,CACJ,CAAC,cAEPrP,IAAA,CAACtC,IAAI,EAACgT,IAAI,MAAC9G,EAAE,CAAE,EAAG,CAAC2I,EAAE,CAAE,CAAE,CAAA3Q,QAAA,cACvB1B,KAAA,CAAC7C,KAAK,EAACsJ,SAAS,CAAE,CAAE,CAAC9E,EAAE,CAAE,CAAE+E,CAAC,CAAE,CAAC,CAAEpF,YAAY,CAAE,CAAE,CAAE,CAAAI,QAAA,eACjD1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,WAAW,CAACC,UAAU,CAAC,MAAM,CAACY,KAAK,CAAEzC,KAAK,CAACoC,OAAO,CAACI,OAAO,CAACF,IAAK,CAACkM,YAAY,MAAAlN,QAAA,EAAC,kBAChF,CAACuQ,YAAY,CAAC,GAChC,EAAY,CAAC,cACbnS,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAC6R,SAAS,MAAAnS,QAAA,CAAC,6LAGtC,CAAY,CAAC,cACb5B,IAAA,CAAC7C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,cACzB5B,IAAA,WAAA4B,QAAA,CAAQ,sBAAoB,CAAQ,CAAC,CAC3B,CAAC,cACb1B,KAAA,OAAIc,KAAK,CAAE,CAAEsT,WAAW,CAAE,MAAM,CAAEjL,MAAM,CAAE,OAAQ,CAAE,CAAAzH,QAAA,eAClD5B,IAAA,OAAA4B,QAAA,cACE1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,oBACR,CAChB,CAAC8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CmB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,eAAe,CAAC,CAAE,CAAC,CAAC,EACzDoH,YAAY,EAAI,CAAC,CAAC,EAAE9C,OAAO,CAAC,CAAC,CAAC,CAChC,KACH,EAAY,CAAC,CACX,CAAC,cACLrP,IAAA,OAAA4B,QAAA,cACE1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,qBACP,CACjB,CAAC8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CmB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,eAAe,CAAC,CAAE,CAAC,CAAC,EACzDoH,YAAY,EAAI,CAAC,CAAC,EAAE9C,OAAO,CAAC,CAAC,CAAC,CAChC,IACH,EAAY,CAAC,CACX,CAAC,cACLrP,IAAA,OAAA4B,QAAA,cACE1B,KAAA,CAAC/C,UAAU,EAAC+E,OAAO,CAAC,OAAO,CAAAN,QAAA,EAAC,uBACL,CACnB,CAAC8I,OAAO,CACLI,MAAM,CAACC,KAAK,EAAIA,KAAK,CAACC,gBAAgB,GAAK,CAAC,CAAC,CAC7CmB,MAAM,CAAC,CAACkG,GAAG,CAAEtH,KAAK,GAAKsH,GAAG,CAAGtH,KAAK,CAAC,iBAAiB,CAAC,CAAE,CAAC,CAAC,EAC3DoH,YAAY,EAAI,CAAC,CAAC,EAAE9C,OAAO,CAAC,CAAC,CAAC,CAChC,IACH,EAAY,CAAC,CACX,CAAC,EACH,CAAC,EACA,CAAC,CACJ,CAAC,EACH,CAAC,EACJ,CAAC,EACN,CAAC,CAEP,CAAC,CAED,cAAe,CAAAyB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}