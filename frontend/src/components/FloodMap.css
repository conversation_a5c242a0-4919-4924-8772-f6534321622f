/* ========================================
   ENHANCED FLOOD MAP STYLES
   Modern, Attractive & Interactive Design
   ======================================== */

/* ===== GLOBAL MAP STYLES ===== */
.leaflet-container {
  background: linear-gradient(135deg, #0c1445 0%, #1a1a2e 50%, #16213e 100%);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

/* ===== ENHANCED MARKER ANIMATIONS ===== */
@keyframes pulse-high-risk {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 15px rgba(255, 68, 68, 0.2);
    transform: scale(1.1);
  }
  100% {
    box-shadow: 0 0 0 25px rgba(255, 68, 68, 0);
    transform: scale(1);
  }
}

@keyframes pulse-search {
  0% {
    box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 12px rgba(58, 134, 255, 0.1), 0 6px 16px rgba(0,0,0,0.4);
    transform: scale(1.05);
  }
  100% {
    box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);
    transform: scale(1);
  }
}

@keyframes glow-low-risk {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 230, 118, 0.5);
  }
  50% {
    box-shadow: 0 0 15px rgba(0, 230, 118, 0.8);
  }
}

/* ===== MARKER STYLES ===== */
.high-risk-marker {
  animation: pulse-high-risk 3s infinite;
  filter: drop-shadow(0 0 8px rgba(255, 68, 68, 0.6));
}

.low-risk-marker {
  animation: glow-low-risk 4s infinite;
  filter: drop-shadow(0 0 5px rgba(0, 230, 118, 0.4));
}

.search-location-marker {
  animation: pulse-search 2s infinite;
}

/* ===== POPUP ENHANCEMENTS ===== */
.scrollable-popup .leaflet-popup-content {
  overflow-y: auto;
  max-height: 400px;
  margin-right: 0;
  padding-right: 8px;
  scrollbar-width: thin;
}

/* Ensure popup content is properly sized */
.popup-content {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Ensure popup is positioned correctly relative to the map */
.leaflet-popup {
  position: absolute;
  margin-bottom: 30px; /* Provide space for the popup tip */
  transform-origin: bottom center;
}

/* Style the scrollbar */
.scrollable-popup .leaflet-popup-content::-webkit-scrollbar {
  width: 6px;
}

.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-thumb {
  background: rgba(58, 134, 255, 0.3);
  border-radius: 3px;
}

.scrollable-popup .leaflet-popup-content::-webkit-scrollbar-thumb:hover {
  background: rgba(58, 134, 255, 0.5);
}

/* Ensure popup content has proper spacing */
.scrollable-popup .leaflet-popup-content {
  padding: 2px;
}

/* ===== GLASSMORPHISM POPUP EFFECTS ===== */
.enhanced-popup .leaflet-popup-content-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  padding: 4px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-popup .leaflet-popup-content-wrapper:hover {
  transform: translateY(-2px);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.enhanced-popup .leaflet-popup-tip {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* Standard popup styling */
.leaflet-popup-content-wrapper {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  padding: 2px;
  overflow: hidden;
}

.leaflet-popup-tip {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

/* ===== ENHANCED POPUP ANIMATIONS ===== */
.leaflet-popup {
  animation: popup-slide-up 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-popup .leaflet-popup {
  animation: popup-glassmorphism-entrance 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes popup-slide-up {
  from {
    opacity: 0;
    transform: scale(0.85) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes popup-glassmorphism-entrance {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(30px) rotateX(10deg);
    backdrop-filter: blur(0px);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02) translateY(-5px) rotateX(0deg);
    backdrop-filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0) rotateX(0deg);
    backdrop-filter: blur(20px);
  }
}

@keyframes popup-fade-in {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Ensure popup stays within map bounds */
.leaflet-popup-content-wrapper {
  max-height: 80vh;
  max-width: 80vw;
}

/* Ensure popup is positioned correctly */
.leaflet-popup {
  margin-bottom: 20px;
}

/* Add animation to popups */
.leaflet-fade-anim .leaflet-popup {
  transition: opacity 0.25s linear;
  opacity: 0;
}

.leaflet-fade-anim .leaflet-map-pane .leaflet-popup {
  opacity: 1;
}

/* ===== ENHANCED MAP CONTROLS ===== */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15) !important;
  border-radius: 12px !important;
  overflow: hidden;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9) !important;
}

.leaflet-control-zoom a {
  background: rgba(255, 255, 255, 0.95) !important;
  border: none !important;
  color: #2c3e50 !important;
  font-weight: bold;
  transition: all 0.3s ease !important;
  backdrop-filter: blur(10px);
}

.leaflet-control-zoom a:hover {
  background: linear-gradient(135deg, #3A86FF, #06FFA5) !important;
  color: white !important;
  transform: scale(1.05);
}

.leaflet-control-attribution {
  background: rgba(0, 0, 0, 0.7) !important;
  color: rgba(255, 255, 255, 0.8) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.leaflet-control-attribution a {
  color: #3A86FF !important;
  text-decoration: none;
}

.leaflet-control-attribution a:hover {
  color: #06FFA5 !important;
}

/* ===== ENHANCED POPUP CLOSE BUTTON ===== */
.leaflet-container a.leaflet-popup-close-button {
  font-size: 20px;
  padding: 6px 8px 0 0;
  color: #3A86FF;
  font-weight: bold;
  transition: all 0.3s ease;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 4px;
}

.leaflet-container a.leaflet-popup-close-button:hover {
  color: white;
  background: linear-gradient(135deg, #FF595E, #FF8E53);
  transform: scale(1.1) rotate(90deg);
  box-shadow: 0 4px 12px rgba(255, 89, 94, 0.4);
}

/* ===== TILE LAYER ENHANCEMENTS ===== */
.leaflet-tile {
  transition: opacity 0.3s ease;
}

.leaflet-tile-loaded {
  animation: tile-fade-in 0.5s ease-out;
}

@keyframes tile-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* ===== RISK HEAT MAP ZONES ===== */
.risk-heat-zone {
  transition: all 0.3s ease;
  cursor: pointer;
}

.risk-heat-zone:hover {
  opacity: 0.8 !important;
  transform: scale(1.02);
  filter: brightness(1.1);
}

.risk-zone-tooltip {
  background: rgba(255, 68, 68, 0.95) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px !important;
  font-weight: bold;
  font-size: 12px;
  padding: 8px 12px !important;
  box-shadow: 0 4px 12px rgba(255, 68, 68, 0.4) !important;
  backdrop-filter: blur(10px);
}

.risk-zone-tooltip::before {
  border-top-color: rgba(255, 68, 68, 0.95) !important;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
  .leaflet-popup-content-wrapper {
    max-width: 90vw !important;
    max-height: 70vh !important;
  }

  .leaflet-control-zoom {
    transform: scale(0.9);
  }

  .high-risk-marker,
  .low-risk-marker {
    transform: scale(0.8);
  }
}

@media (max-width: 480px) {
  .leaflet-popup-content-wrapper {
    max-width: 95vw !important;
    max-height: 60vh !important;
  }

  .leaflet-control-zoom {
    transform: scale(0.8);
  }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.leaflet-container:focus {
  outline: 3px solid #3A86FF;
  outline-offset: 2px;
}

.leaflet-popup-content-wrapper {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.leaflet-tile-container {
  will-change: transform;
}

/* ===== FULLSCREEN STYLES ===== */
#flood-map-container:fullscreen {
  background: #000 !important;
  border-radius: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

#flood-map-container:fullscreen .leaflet-container {
  height: 100vh !important;
  width: 100vw !important;
  border-radius: 0 !important;
}

/* Webkit fullscreen */
#flood-map-container:-webkit-full-screen {
  background: #000 !important;
  border-radius: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

#flood-map-container:-webkit-full-screen .leaflet-container {
  height: 100vh !important;
  width: 100vw !important;
  border-radius: 0 !important;
}

/* Mozilla fullscreen */
#flood-map-container:-moz-full-screen {
  background: #000 !important;
  border-radius: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

#flood-map-container:-moz-full-screen .leaflet-container {
  height: 100vh !important;
  width: 100vw !important;
  border-radius: 0 !important;
}

/* Microsoft fullscreen */
#flood-map-container:-ms-fullscreen {
  background: #000 !important;
  border-radius: 0 !important;
  border: none !important;
  box-shadow: none !important;
}

#flood-map-container:-ms-fullscreen .leaflet-container {
  height: 100vh !important;
  width: 100vw !important;
  border-radius: 0 !important;
}

/* Enhanced fullscreen controls positioning */
#flood-map-container:fullscreen .fullscreen-controls,
#flood-map-container:-webkit-full-screen .fullscreen-controls,
#flood-map-container:-moz-full-screen .fullscreen-controls,
#flood-map-container:-ms-fullscreen .fullscreen-controls {
  position: fixed !important;
  top: 20px !important;
  right: 20px !important;
  z-index: 10000 !important;
}

.leaflet-zoom-anim .leaflet-zoom-animated {
  will-change: transform;
}

/* ===== DARK MODE COMPATIBILITY ===== */
@media (prefers-color-scheme: dark) {
  .leaflet-popup-content-wrapper {
    background: rgba(30, 30, 30, 0.95) !important;
    color: white !important;
  }

  .enhanced-popup .leaflet-popup-content-wrapper {
    background: rgba(20, 20, 20, 0.95) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
  }
}
