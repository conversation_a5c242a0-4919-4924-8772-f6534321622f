import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CircleMarker,
  <PERSON>up,
  useMap,
  LayerGroup,
  ZoomControl,
  Rectangle,
  Marker,
  Polygon
} from 'react-leaflet';
import L from 'leaflet';
import './FloodMap.css'; // Import custom CSS for map
import {
  Box,
  Typography,
  Skeleton,
  Paper,
  Chip,
  Divider,
  Card,
  CardContent,
  Grid,
  useTheme,
  Button,
  Tooltip,
  IconButton,
  Fade,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Alert,
  Collapse,
  Snackbar,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import TerrainIcon from '@mui/icons-material/Terrain';
import ThunderstormIcon from '@mui/icons-material/Thunderstorm';
import InfoIcon from '@mui/icons-material/Info';
import SatelliteIcon from '@mui/icons-material/Satellite';
import DarkModeIcon from '@mui/icons-material/DarkMode';
import MapIcon from '@mui/icons-material/Map';
import LayersIcon from '@mui/icons-material/Layers';
import MyLocationIcon from '@mui/icons-material/MyLocation';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import SearchIcon from '@mui/icons-material/Search';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import CloseIcon from '@mui/icons-material/Close';
import WarningIcon from '@mui/icons-material/Warning';
import EngineeringIcon from '@mui/icons-material/Engineering';
import HomeIcon from '@mui/icons-material/Home';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit';
import { useSpring, animated } from 'react-spring';
import axios from 'axios';

// Legend component
const MapLegend = () => {
  const theme = useTheme();
  const [expanded, setExpanded] = useState(false);

  const legendAnimation = useSpring({
    opacity: expanded ? 1 : 0.9,
    height: expanded ? 280 : 40,
    config: { tension: 200, friction: 20 }
  });

  return (
    <animated.div style={{
      position: 'absolute',
      bottom: 20,
      right: 10,
      zIndex: 1000,
      backgroundColor: 'white',
      padding: '10px 15px',
      borderRadius: 8,
      boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
      overflow: 'hidden',
      width: 220,
      ...legendAnimation
    }}>
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: expanded ? 1 : 0
      }}>
        <Typography variant="subtitle1" fontWeight="bold">
          Map Legend
        </Typography>
        <IconButton
          size="small"
          onClick={() => setExpanded(!expanded)}
          sx={{
            backgroundColor: expanded ? 'rgba(0,0,0,0.05)' : 'transparent',
            transition: 'all 0.2s ease'
          }}
        >
          <InfoIcon fontSize="small" />
        </IconButton>
      </Box>

      {expanded && (
        <>
          <Divider sx={{ my: 1 }} />

          <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 1 }}>
            Flood Risk Indicators
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <Box sx={{
              width: 16,
              height: 16,
              borderRadius: '50%',
              backgroundColor: theme.palette.error.main,
              mr: 1
            }} />
            <Typography variant="body2">High Risk Area</Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <Box sx={{
              width: 16,
              height: 16,
              borderRadius: '50%',
              backgroundColor: theme.palette.success.main,
              mr: 1
            }} />
            <Typography variant="body2">Low Risk Area</Typography>
          </Box>

          <Typography variant="subtitle2" fontWeight="bold" sx={{ mt: 2 }}>
            Risk Factors
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <ThunderstormIcon fontSize="small" sx={{ mr: 1, color: theme.palette.info.main }} />
            <Typography variant="body2">Heavy Rainfall (>200mm)</Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <TerrainIcon fontSize="small" sx={{ mr: 1, color: theme.palette.warning.main }} />
            <Typography variant="body2">Low Elevation (&lt;100m)</Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
            <WaterDropIcon fontSize="small" sx={{ mr: 1, color: theme.palette.error.main }} />
            <Typography variant="body2">High Water Level (>8m)</Typography>
          </Box>

          <Typography variant="caption" sx={{ display: 'block', mt: 2, color: theme.palette.text.secondary }}>
            Click on markers for detailed information about specific locations.
          </Typography>
        </>
      )}
    </animated.div>
  );
};

// Search component
const LocationSearch = ({ onLocationSelect }) => {
  const map = useMap();
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState([]);
  const [searching, setSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [error, setError] = useState(null);

  // Geocoding API (using OpenStreetMap Nominatim)
  const searchLocation = useCallback(async (query) => {
    if (!query || query.trim().length < 3) return;

    setSearching(true);
    setError(null);

    // Clear any existing timeout to prevent memory leaks
    let searchTimeout;

    try {
      // Add "India" to the search query to focus on Indian locations
      const response = await axios.get(`https://nominatim.openstreetmap.org/search`, {
        params: {
          q: `${query}, India`,
          format: 'json',
          limit: 5,
          countrycodes: 'in', // Limit to India
          addressdetails: 1
        },
        headers: {
          'Accept-Language': 'en-US,en;q=0.9',
          'User-Agent': 'FloodRiskPredictionApp'
        }
      });

      if (response.data && response.data.length > 0) {
        setSearchResults(response.data);
        setShowResults(true);
      } else {
        setSearchResults([]);
        setError('No locations found. Try a different search term.');
      }
    } catch (err) {
      console.error('Error searching for location:', err);
      setError('Error searching for location. Please try again.');
    } finally {
      // Ensure searching state is properly reset with a slight delay
      // This prevents UI flicker if multiple searches happen quickly
      searchTimeout = setTimeout(() => {
        setSearching(false);
      }, 300);
    }

    return () => {
      if (searchTimeout) clearTimeout(searchTimeout);
    };
  }, []);

  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
    if (e.target.value.trim().length === 0) {
      setShowResults(false);
    }
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    searchLocation(searchQuery);
  };

  const handleLocationClick = (location) => {
    const lat = parseFloat(location.lat);
    const lng = parseFloat(location.lon);

    // Fly to the location
    map.flyTo([lat, lng], 12, {
      duration: 1.5
    });

    // Pass the selected location to parent component
    onLocationSelect({
      lat,
      lng,
      name: location.display_name,
      type: location.type,
      importance: location.importance,
      address: location.address
    });

    // Clear search results
    setShowResults(false);
  };

  return (
    <Box sx={{
      position: 'absolute',
      top: 10,
      left: 10,
      zIndex: 1000,
      width: 300,
      maxWidth: 'calc(100% - 20px)'
    }}>
      <Paper
        elevation={3}
        sx={{
          p: 1.5,
          borderRadius: 2,
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }}
      >
        <form onSubmit={handleSearchSubmit}>
          <TextField
            fullWidth
            placeholder="Search for a place in India..."
            value={searchQuery}
            onChange={handleSearchChange}
            variant="outlined"
            size="small"
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon color="action" />
                </InputAdornment>
              ),
              endAdornment: searching ? (
                <InputAdornment position="end">
                  <Box
                    sx={{
                      width: 20,
                      height: 20,
                      borderRadius: '50%',
                      border: '2px solid transparent',
                      borderTopColor: theme.palette.primary.main,
                      animation: 'spin 1s linear infinite',
                    }}
                  />
                </InputAdornment>
              ) : searchQuery ? (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    onClick={() => {
                      setSearchQuery('');
                      setShowResults(false);
                    }}
                  >
                    <CloseIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null,
              sx: {
                borderRadius: 2,
                '&.Mui-focused': {
                  boxShadow: `0 0 0 2px ${theme.palette.primary.main}20`
                }
              }
            }}
          />
        </form>

        {error && (
          <Alert
            severity="warning"
            sx={{ mt: 1, borderRadius: 1 }}
            onClose={() => setError(null)}
          >
            {error}
          </Alert>
        )}

        <Collapse in={showResults && searchResults.length > 0}>
          <List sx={{ mt: 1, maxHeight: 300, overflow: 'auto' }}>
            {searchResults.map((result, index) => (
              <ListItem
                key={index}
                button
                onClick={() => handleLocationClick(result)}
                sx={{
                  borderRadius: 1,
                  mb: 0.5,
                  '&:hover': {
                    backgroundColor: theme.palette.action.hover
                  }
                }}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <LocationOnIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary={result.address?.city || result.address?.town || result.address?.village || result.address?.state || result.display_name.split(',')[0]}
                  secondary={result.display_name}
                  secondaryTypographyProps={{
                    noWrap: true,
                    style: { fontSize: '0.75rem' }
                  }}
                />
              </ListItem>
            ))}
          </List>
        </Collapse>
      </Paper>
    </Box>
  );
};

// Map style toggle component
const MapStyleToggle = ({ mapStyle, onStyleChange }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        position: 'absolute',
        top: 10,
        left: 10,
        zIndex: 1000,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: 2,
        padding: 1,
        boxShadow: '0 8px 24px rgba(0, 0, 0, 0.15)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}
    >
      <ToggleButtonGroup
        value={mapStyle}
        exclusive
        onChange={(event, newStyle) => {
          if (newStyle !== null) {
            onStyleChange(newStyle);
          }
        }}
        size="small"
        sx={{
          '& .MuiToggleButton-root': {
            border: 'none',
            borderRadius: 1.5,
            margin: 0.25,
            padding: '6px 12px',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: theme.palette.primary.light + '20',
              transform: 'scale(1.05)'
            },
            '&.Mui-selected': {
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              '&:hover': {
                backgroundColor: theme.palette.primary.dark
              }
            }
          }
        }}
      >
        <ToggleButton value="dark" aria-label="dark theme">
          <Tooltip title="Dark Theme" placement="bottom">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <DarkModeIcon fontSize="small" />
              <Typography variant="caption" sx={{ display: { xs: 'none', sm: 'block' } }}>
                Dark
              </Typography>
            </Box>
          </Tooltip>
        </ToggleButton>

        <ToggleButton value="satellite" aria-label="satellite view">
          <Tooltip title="Satellite View" placement="bottom">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <SatelliteIcon fontSize="small" />
              <Typography variant="caption" sx={{ display: { xs: 'none', sm: 'block' } }}>
                Satellite
              </Typography>
            </Box>
          </Tooltip>
        </ToggleButton>

        <ToggleButton value="terrain" aria-label="terrain view">
          <Tooltip title="Terrain View" placement="bottom">
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
              <TerrainIcon fontSize="small" />
              <Typography variant="caption" sx={{ display: { xs: 'none', sm: 'block' } }}>
                Terrain
              </Typography>
            </Box>
          </Tooltip>
        </ToggleButton>
      </ToggleButtonGroup>
    </Box>
  );
};

// Fullscreen toggle component
const FullscreenToggle = ({ isFullscreen, onToggle }) => {
  const theme = useTheme();

  return (
    <Box
      sx={{
        position: 'absolute',
        top: { xs: 10, sm: 15 },
        right: { xs: 10, sm: 15 },
        zIndex: 1000,
        display: 'flex',
        flexDirection: 'column',
        gap: 1
      }}
    >
      <Tooltip title={isFullscreen ? "Exit Fullscreen" : "Enter Fullscreen"} placement="left">
        <IconButton
          onClick={onToggle}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(58, 134, 255, 0.2)',
            borderRadius: 2,
            width: { xs: 40, sm: 48 },
            height: { xs: 40, sm: 48 },
            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: 'rgba(58, 134, 255, 0.1)',
              transform: 'translateY(-2px)',
              boxShadow: '0 6px 20px rgba(58, 134, 255, 0.25)',
              borderColor: theme.palette.primary.main
            },
            '&:active': {
              transform: 'translateY(0px)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)'
            }
          }}
        >
          {isFullscreen ? (
            <FullscreenExitIcon
              sx={{
                color: theme.palette.primary.main,
                fontSize: { xs: 20, sm: 24 }
              }}
            />
          ) : (
            <FullscreenIcon
              sx={{
                color: theme.palette.primary.main,
                fontSize: { xs: 20, sm: 24 }
              }}
            />
          )}
        </IconButton>
      </Tooltip>
    </Box>
  );
};

// Map controls component
const MapControls = () => {
  const map = useMap();
  const theme = useTheme();

  const handleZoomIn = () => {
    map.zoomIn();
  };

  const handleZoomOut = () => {
    map.zoomOut();
  };

  const handleLocate = () => {
    map.locate({ setView: true, maxZoom: 10 });
  };

  return (
    <Box sx={{
      position: 'absolute',
      top: 10,
      right: 10,
      zIndex: 1000,
      display: 'flex',
      flexDirection: 'column',
      gap: 1
    }}>
      <Tooltip title="Zoom In" placement="left">
        <IconButton
          onClick={handleZoomIn}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              transform: 'scale(1.1)'
            }
          }}
        >
          <ZoomInIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="Zoom Out" placement="left">
        <IconButton
          onClick={handleZoomOut}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: theme.palette.primary.main,
              color: 'white',
              transform: 'scale(1.1)'
            }
          }}
        >
          <ZoomOutIcon />
        </IconButton>
      </Tooltip>

      <Tooltip title="My Location" placement="left">
        <IconButton
          onClick={handleLocate}
          sx={{
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            transition: 'all 0.3s ease',
            '&:hover': {
              backgroundColor: theme.palette.success.main,
              color: 'white',
              transform: 'scale(1.1)'
            }
          }}
        >
          <MyLocationIcon />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

// Heat map overlay component for risk visualization
const RiskHeatMap = ({ mapData }) => {
  const map = useMap();
  const [heatMapVisible, setHeatMapVisible] = useState(true);

  useEffect(() => {
    if (!mapData || mapData.length === 0 || !heatMapVisible) return;

    // Create heat map zones based on risk density
    const highRiskPoints = mapData.filter(point => point.Flood_Prediction === 1);
    const riskZones = [];

    // Group nearby high-risk points into zones
    const processedPoints = new Set();

    highRiskPoints.forEach((point, index) => {
      if (processedPoints.has(index)) return;

      const zone = [point];
      const zoneCenter = { lat: point.Latitude, lng: point.Longitude };

      // Find nearby points within 0.5 degrees
      highRiskPoints.forEach((otherPoint, otherIndex) => {
        if (index === otherIndex || processedPoints.has(otherIndex)) return;

        const distance = Math.sqrt(
          Math.pow(point.Latitude - otherPoint.Latitude, 2) +
          Math.pow(point.Longitude - otherPoint.Longitude, 2)
        );

        if (distance < 0.5) {
          zone.push(otherPoint);
          processedPoints.add(otherIndex);
        }
      });

      if (zone.length >= 2) {
        // Create a risk zone polygon
        const bounds = zone.reduce((acc, p) => {
          acc.minLat = Math.min(acc.minLat, p.Latitude);
          acc.maxLat = Math.max(acc.maxLat, p.Latitude);
          acc.minLng = Math.min(acc.minLng, p.Longitude);
          acc.maxLng = Math.max(acc.maxLng, p.Longitude);
          return acc;
        }, {
          minLat: Infinity,
          maxLat: -Infinity,
          minLng: Infinity,
          maxLng: -Infinity
        });

        // Expand bounds slightly for better visualization
        const padding = 0.1;
        const polygon = [
          [bounds.minLat - padding, bounds.minLng - padding],
          [bounds.minLat - padding, bounds.maxLng + padding],
          [bounds.maxLat + padding, bounds.maxLng + padding],
          [bounds.maxLat + padding, bounds.minLng - padding]
        ];

        riskZones.push({
          polygon,
          intensity: zone.length,
          points: zone
        });
      }

      processedPoints.add(index);
    });

    // Add risk zones to map
    riskZones.forEach((zone, index) => {
      const intensity = Math.min(zone.intensity / 5, 1); // Normalize intensity
      const polygon = L.polygon(zone.polygon, {
        color: `rgba(255, 68, 68, ${0.3 + intensity * 0.4})`,
        fillColor: `rgba(255, 107, 107, ${0.2 + intensity * 0.3})`,
        fillOpacity: 0.3 + intensity * 0.4,
        weight: 2,
        className: 'risk-heat-zone'
      }).addTo(map);

      polygon.bindTooltip(`High Risk Zone<br/>Risk Points: ${zone.intensity}`, {
        permanent: false,
        direction: 'center',
        className: 'risk-zone-tooltip'
      });
    });

    return () => {
      // Clean up polygons when component unmounts
      map.eachLayer((layer) => {
        if (layer.options && layer.options.className === 'risk-heat-zone') {
          map.removeLayer(layer);
        }
      });
    };
  }, [map, mapData, heatMapVisible]);

  return null;
};

// Risk clusters component
const RiskClusters = ({ mapData }) => {
  const map = useMap();
  const theme = useTheme();

  // Group points by risk level and proximity
  useEffect(() => {
    if (!mapData || mapData.length === 0) return;

    // Find clusters of high-risk areas
    const highRiskPoints = mapData.filter(point => point.Flood_Prediction === 1);
    const clusters = [];

    // Simple clustering algorithm (this is a simplified version)
    highRiskPoints.forEach(point => {
      const lat = point.Latitude;
      const lng = point.Longitude;

      // Check if point is already in a cluster
      const inCluster = clusters.some(cluster => {
        return Math.abs(cluster.centerLat - lat) < 1 &&
               Math.abs(cluster.centerLng - lng) < 1;
      });

      if (!inCluster && highRiskPoints.filter(p =>
        Math.abs(p.Latitude - lat) < 1 &&
        Math.abs(p.Longitude - lng) < 1
      ).length > 3) {
        // Create a new cluster if there are at least 3 high-risk points in proximity
        clusters.push({
          centerLat: lat,
          centerLng: lng,
          count: highRiskPoints.filter(p =>
            Math.abs(p.Latitude - lat) < 1 &&
            Math.abs(p.Longitude - lng) < 1
          ).length
        });
      }
    });

    return () => {
      // Cleanup if needed
    };
  }, [mapData, map]);

  return null; // Visual representation is handled by the markers
};

// Risk mitigation measures component
const RiskMitigationMeasures = ({ riskLevel, riskFactors }) => {
  const theme = useTheme();

  // Define mitigation measures based on risk factors
  const getMitigationMeasures = () => {
    const measures = [];

    // General measures based on risk level
    if (riskLevel === 'high') {
      measures.push({
        title: 'Evacuation Planning',
        description: 'Develop and practice evacuation plans. Identify safe routes and emergency shelters.',
        icon: <HomeIcon sx={{ color: theme.palette.error.main }} />
      });
      measures.push({
        title: 'Early Warning System',
        description: 'Install flood early warning systems and stay updated with weather forecasts.',
        icon: <WarningIcon sx={{ color: theme.palette.error.main }} />
      });
    }

    // Specific measures based on risk factors
    riskFactors.forEach(factor => {
      if (factor.factor === 'Heavy Rainfall' && factor.severity === 'high') {
        measures.push({
          title: 'Drainage Improvement',
          description: 'Ensure proper drainage systems are in place and regularly maintained to handle heavy rainfall.',
          icon: <EngineeringIcon sx={{ color: theme.palette.info.main }} />
        });
      }

      if (factor.factor === 'Low Elevation' && factor.severity === 'high') {
        measures.push({
          title: 'Elevated Structures',
          description: 'Consider raising the foundation of buildings or using stilts in flood-prone low-lying areas.',
          icon: <HomeIcon sx={{ color: theme.palette.warning.main }} />
        });
      }

      if (factor.factor === 'High Water Level' && factor.severity === 'high') {
        measures.push({
          title: 'Flood Barriers',
          description: 'Install temporary or permanent flood barriers, sandbags, or flood walls to protect property.',
          icon: <WaterDropIcon sx={{ color: theme.palette.error.main }} />
        });
      }
    });

    // Add general measures if none specific were found
    if (measures.length === 0) {
      measures.push({
        title: 'Regular Monitoring',
        description: 'Monitor weather forecasts and water levels during monsoon season.',
        icon: <InfoIcon sx={{ color: theme.palette.info.main }} />
      });
    }

    return measures;
  };

  const measures = getMitigationMeasures();

  return (
    <Box sx={{ mt: 2 }}>
      <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
        Recommended Mitigation Measures:
      </Typography>

      {measures.map((measure, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            mb: 1.5,
            p: 1,
            borderRadius: 1,
            backgroundColor: 'rgba(0, 0, 0, 0.02)'
          }}
        >
          <Box sx={{ mr: 1.5, mt: 0.5 }}>
            {measure.icon}
          </Box>
          <Box>
            <Typography variant="body2" fontWeight="medium">
              {measure.title}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              {measure.description}
            </Typography>
          </Box>
        </Box>
      ))}
    </Box>
  );
};

// Get location name from coordinates
const useReverseGeocoding = () => {
  const [locationCache, setLocationCache] = useState({});

  const getLocationName = useCallback(async (lat, lng) => {
    // Check cache first
    const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;
    if (locationCache[cacheKey]) {
      return locationCache[cacheKey];
    }

    try {
      const response = await axios.get(`https://nominatim.openstreetmap.org/reverse`, {
        params: {
          lat,
          lon: lng,
          format: 'json',
          zoom: 10, // Adjust zoom level for appropriate place name detail
          addressdetails: 1
        },
        headers: {
          'Accept-Language': 'en-US,en;q=0.9',
          'User-Agent': 'FloodRiskPredictionApp'
        }
      });

      if (response.data) {
        // Extract the most relevant name from the response
        const locationData = response.data;
        let placeName = '';

        // Try to get the most specific name first
        if (locationData.address) {
          placeName = locationData.address.village ||
                     locationData.address.town ||
                     locationData.address.city ||
                     locationData.address.county ||
                     locationData.address.state_district ||
                     locationData.address.state;
        }

        // If no specific name found, use the display name
        if (!placeName && locationData.display_name) {
          placeName = locationData.display_name.split(',')[0];
        }

        // If still no name, use coordinates
        if (!placeName) {
          placeName = `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`;
        }

        // Cache the result
        setLocationCache(prev => ({
          ...prev,
          [cacheKey]: {
            name: placeName,
            fullName: locationData.display_name || '',
            address: locationData.address || {}
          }
        }));

        return {
          name: placeName,
          fullName: locationData.display_name || '',
          address: locationData.address || {}
        };
      }
    } catch (error) {
      console.error('Error in reverse geocoding:', error);
    }

    // Return a default if geocoding fails
    return {
      name: `Location at ${lat.toFixed(4)}, ${lng.toFixed(4)}`,
      fullName: '',
      address: {}
    };
  }, [locationCache]);

  return { getLocationName, locationCache };
};

// Enhanced popup content
const EnhancedPopup = ({ point, showMitigationMeasures = false, locationName = null }) => {
  const theme = useTheme();
  const isHighRisk = point.Flood_Prediction === 1;
  const { getLocationName, locationCache } = useReverseGeocoding();
  const [location, setLocation] = useState(null);
  const [loading, setLoading] = useState(!locationName);

  useEffect(() => {
    if (locationName) {
      setLocation(locationName);
      setLoading(false);
      return;
    }

    // Check if we already have this location in cache
    const cacheKey = `${point.Latitude.toFixed(4)},${point.Longitude.toFixed(4)}`;
    if (locationCache[cacheKey]) {
      setLocation(locationCache[cacheKey]);
      setLoading(false);
      return;
    }

    // Fetch location name
    const fetchLocationName = async () => {
      setLoading(true);
      const result = await getLocationName(point.Latitude, point.Longitude);
      setLocation(result);
      setLoading(false);
    };

    fetchLocationName();
  }, [point, locationName, getLocationName, locationCache]);

  // Determine risk factors
  const riskFactors = [];
  if (point['Rainfall (mm)'] > 200) {
    riskFactors.push({
      factor: 'Heavy Rainfall',
      value: `${point['Rainfall (mm)']} mm`,
      icon: <ThunderstormIcon fontSize="small" sx={{ color: theme.palette.info.main }} />,
      severity: 'high'
    });
  }

  if (point['Elevation (m)'] < 100) {
    riskFactors.push({
      factor: 'Low Elevation',
      value: `${point['Elevation (m)']} m`,
      icon: <TerrainIcon fontSize="small" sx={{ color: theme.palette.warning.main }} />,
      severity: point['Elevation (m)'] < 50 ? 'high' : 'medium'
    });
  }

  if (point['Water Level (m)'] > 6) {
    riskFactors.push({
      factor: 'High Water Level',
      value: `${point['Water Level (m)']} m`,
      icon: <WaterDropIcon fontSize="small" sx={{ color: theme.palette.error.main }} />,
      severity: point['Water Level (m)'] > 8 ? 'high' : 'medium'
    });
  }

  return (
    <Box
      sx={{
        width: '100%',
        maxWidth: showMitigationMeasures ? 350 : 280,
        overflowX: 'hidden'
      }}
      className="popup-content"
    >
      <Typography
        variant="subtitle1"
        fontWeight="bold"
        sx={{
          mb: 0.5,
          color: isHighRisk ? theme.palette.error.main : theme.palette.success.main,
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          position: 'sticky',
          top: 0,
          backgroundColor: 'white',
          zIndex: 10,
          py: 0.5
        }}
      >
        <WaterDropIcon fontSize="small" />
        {isHighRisk ? 'High Flood Risk Area' : 'Low Flood Risk Area'}
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', alignItems: 'center', my: 1 }}>
          <Box
            sx={{
              width: 16,
              height: 16,
              borderRadius: '50%',
              border: '2px solid transparent',
              borderTopColor: theme.palette.primary.main,
              animation: 'spin 1s linear infinite',
              mr: 1
            }}
          />
          <Typography variant="body2" color="text.secondary">
            Loading location...
          </Typography>
        </Box>
      ) : location ? (
        <Box sx={{ mb: 1 }}>
          <Typography variant="subtitle2" fontWeight="medium">
            {location.name}
          </Typography>
          {location.fullName && (
            <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
              {location.fullName}
            </Typography>
          )}
        </Box>
      ) : null}

      <Divider sx={{ mb: 1.5 }} />

      <Grid container spacing={1} sx={{ mb: 1.5 }}>
        <Grid item xs={6}>
          <Typography variant="caption" color="text.secondary">
            Rainfall
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {point['Rainfall (mm)']} mm
          </Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="caption" color="text.secondary">
            Elevation
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {point['Elevation (m)']} m
          </Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="caption" color="text.secondary">
            Water Level
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {point['Water Level (m)']} m
          </Typography>
        </Grid>
        <Grid item xs={6}>
          <Typography variant="caption" color="text.secondary">
            Coordinates
          </Typography>
          <Typography variant="body2" fontWeight="medium">
            {point.Latitude.toFixed(2)}, {point.Longitude.toFixed(2)}
          </Typography>
        </Grid>
      </Grid>

      {riskFactors.length > 0 && (
        <>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
            Risk Factors:
          </Typography>

          {riskFactors.map((factor, idx) => (
            <Box key={idx} sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
              {factor.icon}
              <Typography variant="body2" sx={{ ml: 0.5 }}>
                {factor.factor}: <strong>{factor.value}</strong>
              </Typography>
            </Box>
          ))}
        </>
      )}

      {isHighRisk && !showMitigationMeasures && (
        <Box sx={{
          mt: 1.5,
          p: 1,
          backgroundColor: 'rgba(255, 89, 94, 0.1)',
          borderRadius: 1,
          borderLeft: `3px solid ${theme.palette.error.main}`
        }}>
          <Typography variant="caption" sx={{ display: 'block', fontWeight: 'medium' }}>
            Recommendation:
          </Typography>
          <Typography variant="body2">
            This area requires flood mitigation measures and close monitoring during heavy rainfall.
          </Typography>
        </Box>
      )}

      {showMitigationMeasures && (
        <RiskMitigationMeasures
          riskLevel={isHighRisk ? 'high' : 'low'}
          riskFactors={riskFactors}
        />
      )}
    </Box>
  );
};

const FloodMap = ({ mapData }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [searchedLocation, setSearchedLocation] = useState(null);
  const [nearestPoint, setNearestPoint] = useState(null);
  const [showLocationInfo, setShowLocationInfo] = useState(false);
  const [mapStyle, setMapStyle] = useState('dark'); // 'dark', 'satellite', 'terrain'
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'info' });
  const [isFullscreen, setIsFullscreen] = useState(false);

  useEffect(() => {
    if (mapData && mapData.length > 0) {
      // Add a small delay to simulate loading for better UX, but ensure it stops
      const timer = setTimeout(() => {
        setLoading(false);
      }, 1000);

      return () => {
        clearTimeout(timer);
        // Ensure loading is set to false when component unmounts
        setLoading(false);
      };
    } else {
      // If no data, still set loading to false after a short delay
      const timer = setTimeout(() => {
        setLoading(false);
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [mapData]);

  // Find the nearest data point to a searched location
  const findNearestPoint = useCallback((location) => {
    if (!mapData || mapData.length === 0) return null;

    let nearest = null;
    let minDistance = Infinity;

    mapData.forEach(point => {
      const distance = Math.sqrt(
        Math.pow(point.Latitude - location.lat, 2) +
        Math.pow(point.Longitude - location.lng, 2)
      );

      if (distance < minDistance) {
        minDistance = distance;
        nearest = point;
      }
    });

    // Only consider it a match if it's reasonably close (within ~50km)
    if (minDistance > 0.5) {
      setNotification({
        open: true,
        message: 'No exact flood data for this location. Showing nearest available data point.',
        severity: 'info'
      });
    }

    return nearest;
  }, [mapData]);

  // Handle location selection from search
  const handleLocationSelect = useCallback((location) => {
    setSearchedLocation(location);
    const nearest = findNearestPoint(location);
    setNearestPoint(nearest);
    setShowLocationInfo(true);
  }, [findNearestPoint]);

  // Close notification
  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Handle map style change
  const handleMapStyleChange = (newStyle) => {
    setMapStyle(newStyle);
  };

  // Fullscreen functionality
  const toggleFullscreen = useCallback(() => {
    if (!isFullscreen) {
      // Enter fullscreen
      const mapElement = document.getElementById('flood-map-container');
      if (mapElement) {
        if (mapElement.requestFullscreen) {
          mapElement.requestFullscreen();
        } else if (mapElement.webkitRequestFullscreen) {
          mapElement.webkitRequestFullscreen();
        } else if (mapElement.mozRequestFullScreen) {
          mapElement.mozRequestFullScreen();
        } else if (mapElement.msRequestFullscreen) {
          mapElement.msRequestFullscreen();
        }
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if (document.webkitExitFullscreen) {
        document.webkitExitFullscreen();
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen();
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen();
      }
    }
  }, [isFullscreen]);

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  if (!mapData || mapData.length === 0) {
    return (
      <Box sx={{ width: '100%', height: 500, borderRadius: 2 }}>
        <Skeleton variant="rectangular" width="100%" height="100%" />
      </Box>
    );
  }

  // Count high and low risk areas
  const highRiskCount = mapData.filter(point => point.Flood_Prediction === 1).length;
  const lowRiskCount = mapData.filter(point => point.Flood_Prediction === 0).length;

  // Calculate statistics
  const avgRainfall = mapData.reduce((sum, point) => sum + point['Rainfall (mm)'], 0) / mapData.length;
  const avgElevation = mapData.reduce((sum, point) => sum + point['Elevation (m)'], 0) / mapData.length;

  return (
    <>
      <Box sx={{
        mb: { xs: 4, sm: 5, md: 6 },
        p: { xs: 2, sm: 3 },
        borderRadius: 4,
        background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.02) 0%, rgba(76, 201, 240, 0.02) 100%)',
        border: '1px solid rgba(58, 134, 255, 0.08)'
      }}>
        <Grid container spacing={{ xs: 4, sm: 5, md: 6 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={4}
              sx={{
                p: { xs: 3, sm: 4 },
                borderRadius: 4,
                background: `linear-gradient(135deg, ${theme.palette.error.light}15 0%, rgba(255,255,255,0.9) 100%)`,
                borderLeft: `6px solid ${theme.palette.error.main}`,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 32px rgba(255, 99, 132, 0.2)'
                }
              }}
            >
              {/* Decorative background element */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  right: -20,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: `radial-gradient(circle, ${theme.palette.error.light}20 0%, rgba(0,0,0,0) 70%)`,
                  zIndex: 0
                }}
              />

              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    fontSize: '1rem'
                  }}
                >
                  High Risk Areas
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: theme.palette.error.main,
                    mb: 1
                  }}
                >
                  {highRiskCount}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontStyle: 'italic' }}
                >
                  Critical zones identified
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={4}
              sx={{
                p: { xs: 3, sm: 4 },
                borderRadius: 4,
                background: `linear-gradient(135deg, ${theme.palette.success.light}15 0%, rgba(255,255,255,0.9) 100%)`,
                borderLeft: `6px solid ${theme.palette.success.main}`,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 32px rgba(76, 175, 80, 0.2)'
                }
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  right: -20,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: `radial-gradient(circle, ${theme.palette.success.light}20 0%, rgba(0,0,0,0) 70%)`,
                  zIndex: 0
                }}
              />

              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    fontSize: '1rem'
                  }}
                >
                  Low Risk Areas
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: theme.palette.success.main,
                    mb: 1
                  }}
                >
                  {lowRiskCount}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontStyle: 'italic' }}
                >
                  Safe zones identified
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={4}
              sx={{
                p: { xs: 3, sm: 4 },
                borderRadius: 4,
                background: `linear-gradient(135deg, ${theme.palette.info.light}15 0%, rgba(255,255,255,0.9) 100%)`,
                borderLeft: `6px solid ${theme.palette.info.main}`,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 32px rgba(33, 150, 243, 0.2)'
                }
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  right: -20,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: `radial-gradient(circle, ${theme.palette.info.light}20 0%, rgba(0,0,0,0) 70%)`,
                  zIndex: 0
                }}
              />

              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    fontSize: '1rem'
                  }}
                >
                  Avg. Rainfall
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: theme.palette.info.main,
                    mb: 1
                  }}
                >
                  {avgRainfall.toFixed(1)}
                  <Typography component="span" variant="h5" sx={{ ml: 1, fontWeight: 600 }}>
                    mm
                  </Typography>
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontStyle: 'italic' }}
                >
                  Regional precipitation
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Paper
              elevation={4}
              sx={{
                p: { xs: 3, sm: 4 },
                borderRadius: 4,
                background: `linear-gradient(135deg, ${theme.palette.warning.light}15 0%, rgba(255,255,255,0.9) 100%)`,
                borderLeft: `6px solid ${theme.palette.warning.main}`,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 12px 32px rgba(255, 152, 0, 0.2)'
                }
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  right: -20,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  background: `radial-gradient(circle, ${theme.palette.warning.light}20 0%, rgba(0,0,0,0) 70%)`,
                  zIndex: 0
                }}
              />

              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Typography
                  variant="subtitle1"
                  color="text.secondary"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    fontSize: '1rem'
                  }}
                >
                  Avg. Elevation
                </Typography>
                <Typography
                  variant="h3"
                  sx={{
                    fontWeight: 800,
                    color: theme.palette.warning.main,
                    mb: 1
                  }}
                >
                  {avgElevation.toFixed(1)}
                  <Typography component="span" variant="h5" sx={{ ml: 1, fontWeight: 600 }}>
                    m
                  </Typography>
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontStyle: 'italic' }}
                >
                  Terrain altitude
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      <Box
        id="flood-map-container"
        sx={{
          position: 'relative',
          height: isFullscreen ? '100vh' : { xs: 500, sm: 600, md: 700 },
          borderRadius: isFullscreen ? 0 : 4,
          overflow: 'hidden',
          boxShadow: isFullscreen ? 'none' : '0 8px 32px rgba(0, 0, 0, 0.12)',
          border: isFullscreen ? 'none' : '1px solid rgba(58, 134, 255, 0.1)',
          background: isFullscreen
            ? '#000'
            : 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,255,0.9) 100%)',
          transition: 'all 0.3s ease',
          zIndex: isFullscreen ? 9999 : 'auto'
        }}
      >
        {loading && (
          <Fade in={loading} timeout={300}>
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'rgba(255,255,255,0.8)',
                zIndex: 1000
              }}
            >
              <Box sx={{ textAlign: 'center' }}>
                <Box sx={{ position: 'relative', width: 60, height: 60, margin: '0 auto' }}>
                  <Box
                    sx={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '100%',
                      borderRadius: '50%',
                      border: '3px solid transparent',
                      borderTopColor: theme.palette.primary.main,
                      animation: 'spin 1s linear infinite',
                      '@keyframes spin': {
                        '0%': { transform: 'rotate(0deg)' },
                        '100%': { transform: 'rotate(360deg)' }
                      }
                    }}
                  />
                </Box>
                <Typography variant="body1" sx={{ mt: 2 }}>
                  Loading map data...
                </Typography>
              </Box>
            </Box>
          </Fade>
        )}

        <MapContainer
          center={[22.0, 80.0]}
          zoom={5}
          scrollWheelZoom={true}
          style={{ height: '100%', width: '100%', borderRadius: 8 }}
          zoomControl={false}
        >
          {/* Dynamic tile layers based on map style */}
          {mapStyle === 'dark' && (
            <>
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors | &copy; <a href="https://carto.com/attributions">CARTO</a>'
                url="https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png"
                subdomains="abcd"
                maxZoom={19}
              />
              <TileLayer
                attribution='Imagery &copy; <a href="https://www.mapbox.com/">Mapbox</a>'
                url="https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw"
                opacity={0.2}
                maxZoom={19}
              />
            </>
          )}

          {mapStyle === 'satellite' && (
            <TileLayer
              attribution='Imagery &copy; <a href="https://www.mapbox.com/">Mapbox</a>'
              url="https://api.mapbox.com/styles/v1/mapbox/satellite-v9/tiles/{z}/{x}/{y}?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw"
              maxZoom={19}
            />
          )}

          {mapStyle === 'terrain' && (
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
              maxZoom={17}
            />
          )}

          {/* High risk markers with enhanced visual effects */}
          <LayerGroup>
            {mapData
              .filter(point => point.Flood_Prediction === 1)
              .map((point, index) => (
                <CircleMarker
                  key={`high-${index}`}
                  center={[point.Latitude, point.Longitude]}
                  radius={8}
                  pathOptions={{
                    color: '#FF4444',
                    fillColor: '#FF6B6B',
                    fillOpacity: 0.8,
                    weight: 3,
                    opacity: 1,
                    className: 'pulsing-marker high-risk-marker'
                  }}
                  eventHandlers={{
                    mouseover: (e) => {
                      e.target.setStyle({
                        radius: 12,
                        fillOpacity: 0.9,
                        weight: 4
                      });
                    },
                    mouseout: (e) => {
                      e.target.setStyle({
                        radius: 8,
                        fillOpacity: 0.8,
                        weight: 3
                      });
                    }
                  }}
                >
                  <Popup
                    minWidth={300}
                    maxWidth={350}
                    maxHeight={400}
                    autoPan={true}
                    autoPanPadding={[25, 25]}
                    className="scrollable-popup enhanced-popup"
                  >
                    <EnhancedPopup point={point} />
                  </Popup>
                </CircleMarker>
              ))}
          </LayerGroup>

          {/* Low risk markers with enhanced visual effects */}
          <LayerGroup>
            {mapData
              .filter(point => point.Flood_Prediction === 0)
              .map((point, index) => (
                <CircleMarker
                  key={`low-${index}`}
                  center={[point.Latitude, point.Longitude]}
                  radius={6}
                  pathOptions={{
                    color: '#00E676',
                    fillColor: '#4CAF50',
                    fillOpacity: 0.7,
                    weight: 2,
                    opacity: 0.9,
                    className: 'low-risk-marker'
                  }}
                  eventHandlers={{
                    mouseover: (e) => {
                      e.target.setStyle({
                        radius: 9,
                        fillOpacity: 0.85,
                        weight: 3
                      });
                    },
                    mouseout: (e) => {
                      e.target.setStyle({
                        radius: 6,
                        fillOpacity: 0.7,
                        weight: 2
                      });
                    }
                  }}
                >
                  <Popup
                    minWidth={300}
                    maxWidth={350}
                    maxHeight={400}
                    autoPan={true}
                    autoPanPadding={[25, 25]}
                    className="scrollable-popup enhanced-popup"
                  >
                    <EnhancedPopup point={point} />
                  </Popup>
                </CircleMarker>
              ))}
          </LayerGroup>

          {/* Searched location marker */}
          {searchedLocation && nearestPoint && (
            <LayerGroup>
              {/* Marker for the exact searched location */}
              <Marker
                position={[searchedLocation.lat, searchedLocation.lng]}
                icon={L.divIcon({
                  className: 'custom-div-icon search-location-marker',
                  html: `
                    <div style="
                      background: linear-gradient(45deg, #3A86FF, #06FFA5);
                      border: 3px solid white;
                      border-radius: 50%;
                      width: 16px;
                      height: 16px;
                      box-shadow: 0 0 0 6px rgba(58, 134, 255, 0.3), 0 4px 12px rgba(0,0,0,0.3);
                      animation: pulse-search 2s infinite;
                    "></div>
                  `,
                  iconSize: [16, 16],
                  iconAnchor: [8, 8]
                })}
              >
                <Popup
                  minWidth={300}
                  maxWidth={350}
                  maxHeight={400}
                  autoPan={true}
                  autoPanPadding={[30, 30]}
                  className="scrollable-popup"
                >
                  <Box sx={{ maxHeight: '380px', overflowY: 'auto' }}>
                    <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                      {searchedLocation.name.split(',')[0]}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      {searchedLocation.name}
                    </Typography>
                    <Divider sx={{ my: 1.5 }} />
                    <Typography variant="body2" paragraph>
                      Showing flood risk analysis for the nearest data point ({(Math.sqrt(
                        Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) +
                        Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)
                      ) * 111).toFixed(1)} km away).
                    </Typography>
                    <EnhancedPopup point={nearestPoint} showMitigationMeasures={true} />
                  </Box>
                </Popup>
              </Marker>

              {/* Line connecting to the nearest data point */}
              {/* We would need to use a Polyline here, but for simplicity we'll skip it */}
            </LayerGroup>
          )}

          {/* Add risk heat map overlay */}
          <RiskHeatMap mapData={mapData} />

          {/* Add risk clusters */}
          <RiskClusters mapData={mapData} />

          {/* Add search component */}
          <LocationSearch onLocationSelect={handleLocationSelect} />

          {/* Add map style toggle */}
          <MapStyleToggle mapStyle={mapStyle} onStyleChange={handleMapStyleChange} />

          {/* Add map controls */}
          <MapControls />

          {/* Add legend */}
          <MapLegend />
        </MapContainer>

        {/* Add fullscreen toggle */}
        <FullscreenToggle isFullscreen={isFullscreen} onToggle={toggleFullscreen} />

        {/* Notification for search results */}
        <Snackbar
          open={notification.open}
          autoHideDuration={6000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
        >
          <Alert
            onClose={handleCloseNotification}
            severity={notification.severity}
            sx={{ width: '100%' }}
          >
            {notification.message}
          </Alert>
        </Snackbar>
      </Box>

      {/* Searched Location Analysis */}
      {searchedLocation && nearestPoint && showLocationInfo && (
        <Box sx={{ mt: 3, mb: 4 }}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              borderRadius: 2,
              background: `linear-gradient(135deg, ${theme.palette.primary.light}10 0%, ${theme.palette.primary.light}01 100%)`,
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                right: 0,
                width: '150px',
                height: '150px',
                background: 'radial-gradient(circle, rgba(58,134,255,0.05) 0%, rgba(0,0,0,0) 70%)',
                borderRadius: '0 0 0 100%',
                zIndex: 0
              }}
            />

            <Box sx={{ position: 'relative', zIndex: 1 }}>
              <Typography
                variant="h4"
                component="h2"
                gutterBottom
                sx={{
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <LocationOnIcon /> {searchedLocation.name.split(',')[0]}
              </Typography>

              <Typography variant="body1" paragraph color="text.secondary">
                {searchedLocation.name}
              </Typography>

              <Divider sx={{ my: 2 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom fontWeight="medium">
                    Flood Risk Assessment
                  </Typography>

                  <Box
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      backgroundColor: nearestPoint.Flood_Prediction === 1
                        ? 'rgba(255, 89, 94, 0.1)'
                        : 'rgba(6, 214, 160, 0.1)',
                      borderLeft: `4px solid ${nearestPoint.Flood_Prediction === 1
                        ? theme.palette.error.main
                        : theme.palette.success.main}`,
                      mb: 2
                    }}
                  >
                    <Typography
                      variant="subtitle1"
                      fontWeight="bold"
                      sx={{
                        color: nearestPoint.Flood_Prediction === 1
                          ? theme.palette.error.main
                          : theme.palette.success.main
                      }}
                    >
                      {nearestPoint.Flood_Prediction === 1
                        ? 'High Flood Risk Area'
                        : 'Low Flood Risk Area'}
                    </Typography>

                    <Typography variant="body2" sx={{ mt: 1 }}>
                      {nearestPoint.Flood_Prediction === 1
                        ? 'This area has significant flood risk due to environmental and geographical factors. Residents should take precautionary measures.'
                        : 'This area has minimal flood risk under normal conditions. However, it\'s still important to stay informed during extreme weather events.'}
                    </Typography>
                  </Box>

                  <Typography variant="subtitle2" gutterBottom>
                    Environmental Factors:
                  </Typography>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={6}>
                      <Paper
                        elevation={1}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderTop: `3px solid ${theme.palette.info.main}`
                        }}
                      >
                        <ThunderstormIcon sx={{ color: theme.palette.info.main, mb: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          Rainfall
                        </Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {nearestPoint['Rainfall (mm)']} mm
                        </Typography>
                      </Paper>
                    </Grid>

                    <Grid item xs={6}>
                      <Paper
                        elevation={1}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderTop: `3px solid ${theme.palette.warning.main}`
                        }}
                      >
                        <TerrainIcon sx={{ color: theme.palette.warning.main, mb: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          Elevation
                        </Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {nearestPoint['Elevation (m)']} m
                        </Typography>
                      </Paper>
                    </Grid>

                    <Grid item xs={6}>
                      <Paper
                        elevation={1}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderTop: `3px solid ${theme.palette.error.main}`
                        }}
                      >
                        <WaterDropIcon sx={{ color: theme.palette.error.main, mb: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          Water Level
                        </Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {nearestPoint['Water Level (m)']} m
                        </Typography>
                      </Paper>
                    </Grid>

                    <Grid item xs={6}>
                      <Paper
                        elevation={1}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderTop: `3px solid ${theme.palette.primary.main}`
                        }}
                      >
                        <LocationOnIcon sx={{ color: theme.palette.primary.main, mb: 0.5 }} />
                        <Typography variant="body2" color="text.secondary">
                          Distance
                        </Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {(Math.sqrt(
                            Math.pow(nearestPoint.Latitude - searchedLocation.lat, 2) +
                            Math.pow(nearestPoint.Longitude - searchedLocation.lng, 2)
                          ) * 111).toFixed(1)} km
                        </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom fontWeight="medium">
                    Recommended Mitigation Measures
                  </Typography>

                  <RiskMitigationMeasures
                    riskLevel={nearestPoint.Flood_Prediction === 1 ? 'high' : 'low'}
                    riskFactors={[
                      ...(nearestPoint['Rainfall (mm)'] > 200 ? [{
                        factor: 'Heavy Rainfall',
                        value: `${nearestPoint['Rainfall (mm)']} mm`,
                        severity: 'high'
                      }] : []),
                      ...(nearestPoint['Elevation (m)'] < 100 ? [{
                        factor: 'Low Elevation',
                        value: `${nearestPoint['Elevation (m)']} m`,
                        severity: nearestPoint['Elevation (m)'] < 50 ? 'high' : 'medium'
                      }] : []),
                      ...(nearestPoint['Water Level (m)'] > 6 ? [{
                        factor: 'High Water Level',
                        value: `${nearestPoint['Water Level (m)']} m`,
                        severity: nearestPoint['Water Level (m)'] > 8 ? 'high' : 'medium'
                      }] : [])
                    ]}
                  />

                  <Box sx={{ mt: 3 }}>
                    <Button
                      variant="outlined"
                      color="primary"
                      onClick={() => setShowLocationInfo(false)}
                      startIcon={<CloseIcon />}
                      sx={{ mt: 2 }}
                    >
                      Close Location Analysis
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          </Paper>
        </Box>
      )}

      <Box sx={{ mt: 3 }}>
        <Typography variant="h6" gutterBottom fontWeight="medium">
          Flood Risk Analysis
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold" color={theme.palette.error.main} gutterBottom>
                High Risk Areas ({highRiskCount})
              </Typography>
              <Typography variant="body2" paragraph>
                These areas show significant flood risk due to factors like heavy rainfall, low elevation,
                or high water levels. Residents in these areas should be prepared for potential flooding
                during monsoon seasons.
              </Typography>
              <Typography variant="body2">
                <strong>Key characteristics:</strong>
              </Typography>
              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                <li>
                  <Typography variant="body2">
                    Average rainfall: {
                      (mapData
                        .filter(point => point.Flood_Prediction === 1)
                        .reduce((sum, point) => sum + point['Rainfall (mm)'], 0) /
                      (highRiskCount || 1)).toFixed(1)
                    } mm
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    Average elevation: {
                      (mapData
                        .filter(point => point.Flood_Prediction === 1)
                        .reduce((sum, point) => sum + point['Elevation (m)'], 0) /
                      (highRiskCount || 1)).toFixed(1)
                    } m
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    Average water level: {
                      (mapData
                        .filter(point => point.Flood_Prediction === 1)
                        .reduce((sum, point) => sum + point['Water Level (m)'], 0) /
                      (highRiskCount || 1)).toFixed(1)
                    } m
                  </Typography>
                </li>
              </ul>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper elevation={2} sx={{ p: 2, borderRadius: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold" color={theme.palette.success.main} gutterBottom>
                Low Risk Areas ({lowRiskCount})
              </Typography>
              <Typography variant="body2" paragraph>
                These areas have minimal flood risk due to favorable geographical and environmental conditions.
                They typically feature higher elevations, moderate rainfall, or effective drainage systems.
              </Typography>
              <Typography variant="body2">
                <strong>Key characteristics:</strong>
              </Typography>
              <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                <li>
                  <Typography variant="body2">
                    Average rainfall: {
                      (mapData
                        .filter(point => point.Flood_Prediction === 0)
                        .reduce((sum, point) => sum + point['Rainfall (mm)'], 0) /
                      (lowRiskCount || 1)).toFixed(1)
                    } mm
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    Average elevation: {
                      (mapData
                        .filter(point => point.Flood_Prediction === 0)
                        .reduce((sum, point) => sum + point['Elevation (m)'], 0) /
                      (lowRiskCount || 1)).toFixed(1)
                    } m
                  </Typography>
                </li>
                <li>
                  <Typography variant="body2">
                    Average water level: {
                      (mapData
                        .filter(point => point.Flood_Prediction === 0)
                        .reduce((sum, point) => sum + point['Water Level (m)'], 0) /
                      (lowRiskCount || 1)).toFixed(1)
                    } m
                  </Typography>
                </li>
              </ul>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default FloodMap;
