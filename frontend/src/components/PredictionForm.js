import React, { useState } from 'react';
import {
  Box,
  TextField,
  MenuItem,
  Button,
  Grid,
  Slider,
  Typography,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';

const PredictionForm = ({ options, onSubmit, loading }) => {
  const [formData, setFormData] = useState({
    rainfall: 150,
    temperature: 30,
    humidity: 85,
    discharge: 800,
    water_level: 6.5,
    elevation: 150,
    land_cover: '',
    soil_type: '',
    population_density: 1200,
    infrastructure: 'Yes',
    historical_floods: 'No'
  });

  const [validationAlert, setValidationAlert] = useState({
    open: false,
    message: '',
    severity: 'warning'
  });

  // Define validation limits for each field
  const fieldLimits = {
    rainfall: { min: 0, max: 500 },
    temperature: { min: 0, max: 50 },
    humidity: { min: 0, max: 100 },
    discharge: { min: 0, max: 2000 },
    water_level: { min: 0, max: 15 },
    elevation: { min: 0, max: 500 },
    population_density: { min: 0, max: 10000 }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    // Convert to number for validation if it's a numeric field
    const numericValue = parseFloat(value);

    // Check if this field has validation limits
    if (fieldLimits[name]) {
      const { min, max } = fieldLimits[name];

      // If value is empty, allow it (user might be typing)
      if (value === '') {
        setFormData(prev => ({ ...prev, [name]: '' }));
        return;
      }

      // If value is not a valid number, don't update
      if (isNaN(numericValue)) {
        return;
      }

      // Check if value is outside the allowed range
      if (numericValue < min || numericValue > max) {
        // Show validation alert
        setValidationAlert({
          open: true,
          message: `${name.replace('_', ' ').toUpperCase()}: Value must be between ${min} and ${max}`,
          severity: 'warning'
        });

        // Clamp the value within the allowed range
        const clampedValue = Math.min(Math.max(numericValue, min), max);
        setFormData(prev => ({ ...prev, [name]: clampedValue }));
      } else {
        // Value is within range, update normally
        setFormData(prev => ({ ...prev, [name]: numericValue }));
      }
    } else {
      // For non-numeric fields, update directly
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCloseAlert = () => {
    setValidationAlert(prev => ({ ...prev, open: false }));
  };

  const handleSliderChange = (name) => (e, newValue) => {
    // Ensure the slider value is within the defined limits
    if (fieldLimits[name]) {
      const { min, max } = fieldLimits[name];
      const clampedValue = Math.min(Math.max(newValue, min), max);
      setFormData(prev => ({ ...prev, [name]: clampedValue }));
    } else {
      setFormData(prev => ({ ...prev, [name]: newValue }));
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  return (
    <Box
      component="form"
      onSubmit={handleSubmit}
      sx={{
        mt: { xs: 2, sm: 3 },
        position: 'relative',
        zIndex: 1
      }}
    >
      <Grid
        container
        spacing={{ xs: 3, sm: 4, md: 5 }}
        sx={{
          '& .MuiGrid-item': {
            display: 'flex',
            flexDirection: 'column'
          }
        }}
      >
        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 1.5, sm: 2 },
            borderRadius: 2,
            bgcolor: 'rgba(58, 134, 255, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'primary.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'primary.light',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                💧
              </Box>
              Rainfall (mm)
            </Typography>
            <Slider
              value={formData.rainfall}
              onChange={handleSliderChange('rainfall')}
              aria-labelledby="rainfall-slider"
              valueLabelDisplay="auto"
              step={10}
              marks
              min={0}
              max={500}
              sx={{ mt: 1, mb: 1 }}
            />
            <TextField
              margin="dense"
              name="rainfall"
              value={formData.rainfall}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{
                min: 0,
                max: 500,
                step: 10,
                'aria-label': 'Rainfall in millimeters'
              }}
              helperText="Range: 0-500 mm"
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 1.5, sm: 2 },
            borderRadius: 2,
            bgcolor: 'rgba(255, 89, 94, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'secondary.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'secondary.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🌡️
              </Box>
              Temperature (°C)
            </Typography>
            <Slider
              value={formData.temperature}
              onChange={handleSliderChange('temperature')}
              aria-labelledby="temperature-slider"
              valueLabelDisplay="auto"
              step={1}
              marks
              min={0}
              max={50}
              sx={{
                mt: 1,
                mb: 1,
                color: 'secondary.main',
                '& .MuiSlider-thumb': {
                  borderColor: 'secondary.main',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #FF5A5F 0%, #FF9F1C 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="temperature"
              value={formData.temperature}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{
                min: 0,
                max: 50,
                step: 1,
                'aria-label': 'Temperature in Celsius'
              }}
              helperText="Range: 0-50°C"
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 1.5, sm: 2 },
            borderRadius: 2,
            bgcolor: 'rgba(76, 201, 240, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'info.main',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'info.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                💦
              </Box>
              Humidity (%)
            </Typography>
            <Slider
              value={formData.humidity}
              onChange={handleSliderChange('humidity')}
              aria-labelledby="humidity-slider"
              valueLabelDisplay="auto"
              step={5}
              marks
              min={0}
              max={100}
              sx={{
                mt: 1,
                mb: 1,
                color: 'info.main',
                '& .MuiSlider-thumb': {
                  borderColor: 'info.main',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #4CC9F0 0%, #3A86FF 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="humidity"
              value={formData.humidity}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{
                min: 0,
                max: 100,
                step: 5,
                'aria-label': 'Humidity percentage'
              }}
              helperText="Range: 0-100%"
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(76, 175, 80, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(76, 175, 80, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'success.main',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'success.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🌊
              </Box>
              River Discharge (m³/s)
            </Typography>
            <Slider
              value={formData.discharge}
              onChange={handleSliderChange('discharge')}
              aria-labelledby="discharge-slider"
              valueLabelDisplay="auto"
              step={50}
              marks
              min={0}
              max={2000}
              sx={{
                mt: 1,
                mb: 2,
                color: 'success.main',
                '& .MuiSlider-thumb': {
                  borderColor: 'success.main',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #4CAF50 0%, #66BB6A 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="discharge"
              value={formData.discharge}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{
                min: 0,
                max: 2000,
                step: 50,
                'aria-label': 'River discharge in cubic meters per second'
              }}
              helperText="Range: 0-2000 m³/s"
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(33, 150, 243, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(33, 150, 243, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'primary.main',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'primary.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                📏
              </Box>
              Water Level (m)
            </Typography>
            <Slider
              value={formData.water_level}
              onChange={handleSliderChange('water_level')}
              aria-labelledby="water-level-slider"
              valueLabelDisplay="auto"
              step={0.1}
              marks
              min={0}
              max={15}
              sx={{
                mt: 1,
                mb: 2,
                color: 'primary.main',
                '& .MuiSlider-thumb': {
                  borderColor: 'primary.main',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #2196F3 0%, #42A5F5 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="water_level"
              value={formData.water_level}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{
                min: 0,
                max: 15,
                step: 0.1,
                'aria-label': 'Water level in meters'
              }}
              helperText="Range: 0-15 m"
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(156, 39, 176, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(156, 39, 176, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'secondary.dark',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'secondary.dark',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                ⛰️
              </Box>
              Elevation (m)
            </Typography>
            <Slider
              value={formData.elevation}
              onChange={handleSliderChange('elevation')}
              aria-labelledby="elevation-slider"
              valueLabelDisplay="auto"
              step={10}
              marks
              min={0}
              max={500}
              sx={{
                mt: 1,
                mb: 2,
                color: 'secondary.dark',
                '& .MuiSlider-thumb': {
                  borderColor: 'secondary.dark',
                },
                '& .MuiSlider-track': {
                  background: 'linear-gradient(90deg, #9C27B0 0%, #BA68C8 100%)',
                }
              }}
            />
            <TextField
              margin="dense"
              name="elevation"
              value={formData.elevation}
              onChange={handleChange}
              type="number"
              size="small"
              fullWidth
              inputProps={{
                min: 0,
                max: 500,
                step: 10,
                'aria-label': 'Elevation in meters'
              }}
              helperText="Range: 0-500 m"
              sx={{ mt: 'auto' }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(255, 152, 0, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(255, 152, 0, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'warning.main',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'warning.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🌱
              </Box>
              Land Cover
            </Typography>
            <FormControl fullWidth sx={{ mt: 'auto' }}>
              <InputLabel id="land-cover-label">Select Land Cover</InputLabel>
              <Select
                labelId="land-cover-label"
                name="land_cover"
                value={formData.land_cover}
                onChange={handleChange}
                label="Select Land Cover"
                required
                sx={{ borderRadius: 2 }}
              >
                {options.land_cover.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(121, 85, 72, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(121, 85, 72, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: '#795548',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: '#795548',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🏔️
              </Box>
              Soil Type
            </Typography>
            <FormControl fullWidth sx={{ mt: 'auto' }}>
              <InputLabel id="soil-type-label">Select Soil Type</InputLabel>
              <Select
                labelId="soil-type-label"
                name="soil_type"
                value={formData.soil_type}
                onChange={handleChange}
                label="Select Soil Type"
                required
                sx={{ borderRadius: 2 }}
              >
                {options.soil_type.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(103, 58, 183, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(103, 58, 183, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: '#673AB7',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: '#673AB7',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                👥
              </Box>
              Population Density
            </Typography>
            <TextField
              fullWidth
              label="Population Density"
              name="population_density"
              value={formData.population_density}
              onChange={handleChange}
              type="number"
              required
              inputProps={{
                min: 0,
                max: 10000,
                step: 100,
                'aria-label': 'Population density per square kilometer'
              }}
              helperText="Range: 0-10,000 people/km²"
              sx={{
                mt: 'auto',
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2
                }
              }}
            />
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(0, 150, 136, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(0, 150, 136, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: '#009688',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: '#009688',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                🏗️
              </Box>
              Infrastructure Present
            </Typography>
            <FormControl fullWidth sx={{ mt: 'auto' }}>
              <InputLabel id="infrastructure-label">Select Infrastructure</InputLabel>
              <Select
                labelId="infrastructure-label"
                name="infrastructure"
                value={formData.infrastructure}
                onChange={handleChange}
                label="Select Infrastructure"
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="Yes">Yes</MenuItem>
                <MenuItem value="No">No</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Box sx={{
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(244, 67, 54, 0.05)',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgba(244, 67, 54, 0.1)'
          }}>
            <Typography
              variant="subtitle1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'error.main',
                display: 'flex',
                alignItems: 'center',
                mb: 2
              }}
            >
              <Box
                component="span"
                sx={{
                  mr: 1,
                  display: 'inline-flex',
                  bgcolor: 'error.main',
                  color: 'white',
                  p: 0.5,
                  borderRadius: 1,
                  fontSize: '0.875rem'
                }}
              >
                📊
              </Box>
              Historical Floods
            </Typography>
            <FormControl fullWidth sx={{ mt: 'auto' }}>
              <InputLabel id="historical-floods-label">Select History</InputLabel>
              <Select
                labelId="historical-floods-label"
                name="historical_floods"
                value={formData.historical_floods}
                onChange={handleChange}
                label="Select History"
                sx={{ borderRadius: 2 }}
              >
                <MenuItem value="Yes">Yes</MenuItem>
                <MenuItem value="No">No</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box sx={{
            mt: { xs: 4, sm: 5, md: 6 },
            mb: { xs: 2, sm: 3 },
            position: 'relative',
            display: 'flex',
            justifyContent: 'center',
            p: { xs: 2, sm: 3 },
            borderRadius: 3,
            bgcolor: 'rgba(58, 134, 255, 0.02)',
            border: '1px solid rgba(58, 134, 255, 0.1)'
          }}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              size="large"
              endIcon={loading ? <CircularProgress size={24} color="inherit" /> : <SendIcon />}
              disabled={loading || !formData.land_cover || !formData.soil_type}
              sx={{
                py: { xs: 2, sm: 2.5 },
                px: { xs: 6, sm: 8, md: 10 },
                borderRadius: 4,
                fontSize: { xs: '1.1rem', sm: '1.2rem' },
                fontWeight: 700,
                boxShadow: '0 12px 24px rgba(58, 134, 255, 0.3)',
                position: 'relative',
                overflow: 'hidden',
                background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',
                minWidth: { xs: '200px', sm: '250px' },
                '&::before': {
                  content: '""',
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '100%',
                  height: '100%',
                  background: 'linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%)',
                  opacity: 0,
                  transition: 'opacity 0.3s ease',
                },
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 16px 32px rgba(58, 134, 255, 0.4)',
                  '&::before': {
                    opacity: 1,
                  }
                },
                '&:active': {
                  transform: 'translateY(1px)',
                  boxShadow: '0 8px 20px rgba(58, 134, 255, 0.4)',
                },
                '&:disabled': {
                  background: 'linear-gradient(45deg, #ccc 30%, #ddd 90%)',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  transform: 'none'
                },
                transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
              }}
            >
              {loading ? 'Analyzing Data...' : 'Predict Flood Risk'}
            </Button>
          </Box>
        </Grid>
      </Grid>

      {/* Validation Alert Snackbar */}
      <Snackbar
        open={validationAlert.open}
        autoHideDuration={4000}
        onClose={handleCloseAlert}
        anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseAlert}
          severity={validationAlert.severity}
          variant="filled"
          sx={{
            width: '100%',
            fontWeight: 600,
            '& .MuiAlert-icon': {
              fontSize: '1.2rem'
            }
          }}
        >
          {validationAlert.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PredictionForm;
