import React from 'react';
import { Box, Typography, Grid, Paper } from '@mui/material';
import { Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const RiskFactorsChart = ({ riskAssessment }) => {
  const { factors } = riskAssessment;
  
  if (!factors || factors.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 3 }}>
        <Typography variant="body1" color="text.secondary">
          No risk factors to display.
        </Typography>
      </Box>
    );
  }

  // Prepare data for the chart
  const impactValues = {
    'Very High': 4,
    'High': 3,
    'Medium': 2,
    'Low': 1
  };

  const chartData = {
    labels: factors.map(f => f.factor),
    datasets: [
      {
        label: 'Risk Impact',
        data: factors.map(f => impactValues[f.impact] || 0),
        backgroundColor: factors.map(f => {
          switch (f.impact) {
            case 'Very High': return 'rgba(255, 99, 132, 0.8)';
            case 'High': return 'rgba(255, 159, 64, 0.8)';
            case 'Medium': return 'rgba(255, 205, 86, 0.8)';
            case 'Low': return 'rgba(75, 192, 192, 0.8)';
            default: return 'rgba(201, 203, 207, 0.8)';
          }
        }),
        borderColor: factors.map(f => {
          switch (f.impact) {
            case 'Very High': return 'rgb(255, 99, 132)';
            case 'High': return 'rgb(255, 159, 64)';
            case 'Medium': return 'rgb(255, 205, 86)';
            case 'Low': return 'rgb(75, 192, 192)';
            default: return 'rgb(201, 203, 207)';
          }
        }),
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Risk Factors Impact Analysis',
      },
      tooltip: {
        callbacks: {
          label: function(context) {
            const impact = ['', 'Low', 'Medium', 'High', 'Very High'][context.raw];
            return `Impact: ${impact}`;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 5,
        ticks: {
          callback: function(value) {
            return ['', 'Low', 'Medium', 'High', 'Very High'][value];
          }
        }
      }
    }
  };

  return (
    <Grid container spacing={{ xs: 4, sm: 5, md: 6 }}>
      <Grid item xs={12}>
        <Box sx={{
          height: { xs: 350, sm: 400 },
          p: { xs: 2, sm: 3 },
          borderRadius: 3,
          background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,255,0.9) 100%)',
          border: '1px solid rgba(58, 134, 255, 0.1)',
          position: 'relative',
          overflow: 'hidden'
        }}>
          {/* Decorative background elements */}
          <Box
            sx={{
              position: 'absolute',
              top: -20,
              right: -20,
              width: 80,
              height: 80,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',
            }}
          />
          <Bar data={chartData} options={options} />
        </Box>
      </Grid>

      <Grid item xs={12}>
        <Box sx={{
          mb: { xs: 3, sm: 4 },
          pb: 2,
          borderBottom: '2px solid rgba(58, 134, 255, 0.1)'
        }}>
          <Typography
            variant="h5"
            sx={{
              fontWeight: 700,
              color: 'primary.dark',
              mb: 1
            }}
          >
            Detailed Risk Factors
          </Typography>
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{ fontSize: '1rem' }}
          >
            Individual factor analysis with impact levels and threshold values
          </Typography>
        </Box>

        <Grid container spacing={{ xs: 3, sm: 4 }}>
          {factors.map((factor, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <Paper
                elevation={3}
                sx={{
                  p: { xs: 3, sm: 3.5 },
                  borderRadius: 3,
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  position: 'relative',
                  overflow: 'hidden',
                  background: `linear-gradient(135deg, ${
                    factor.impact === 'Very High' ? 'rgba(255, 99, 132, 0.05)' :
                    factor.impact === 'High' ? 'rgba(255, 159, 64, 0.05)' :
                    factor.impact === 'Medium' ? 'rgba(33, 150, 243, 0.05)' : 'rgba(76, 175, 80, 0.05)'
                  } 0%, rgba(255,255,255,0.8) 100%)`,
                  borderLeft: '4px solid',
                  borderColor:
                    factor.impact === 'Very High' ? 'error.main' :
                    factor.impact === 'High' ? 'warning.main' :
                    factor.impact === 'Medium' ? 'info.main' : 'success.main',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 8px 24px rgba(0, 0, 0, 0.12)'
                  }
                }}
              >
                {/* Decorative corner element */}
                <Box
                  sx={{
                    position: 'absolute',
                    top: -10,
                    right: -10,
                    width: 40,
                    height: 40,
                    borderRadius: '50%',
                    background: `radial-gradient(circle, ${
                      factor.impact === 'Very High' ? 'rgba(255, 99, 132, 0.1)' :
                      factor.impact === 'High' ? 'rgba(255, 159, 64, 0.1)' :
                      factor.impact === 'Medium' ? 'rgba(33, 150, 243, 0.1)' : 'rgba(76, 175, 80, 0.1)'
                    } 0%, rgba(0,0,0,0) 70%)`,
                  }}
                />

                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 700,
                    mb: 2,
                    color: 'text.primary'
                  }}
                >
                  {factor.factor}
                </Typography>

                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  mb: 2,
                  p: 1.5,
                  borderRadius: 2,
                  background: 'rgba(255, 255, 255, 0.7)'
                }}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mr: 1, fontWeight: 500 }}
                  >
                    Impact Level:
                  </Typography>
                  <Box
                    component="span"
                    sx={{
                      px: 2,
                      py: 0.5,
                      borderRadius: 2,
                      fontSize: '0.875rem',
                      fontWeight: 600,
                      color: 'white',
                      background:
                        factor.impact === 'Very High' ? 'error.main' :
                        factor.impact === 'High' ? 'warning.main' :
                        factor.impact === 'Medium' ? 'info.main' : 'success.main'
                    }}
                  >
                    {factor.impact}
                  </Box>
                </Box>

                <Box sx={{ mt: 'auto' }}>
                  <Typography
                    variant="body1"
                    sx={{
                      fontWeight: 600,
                      mb: 1,
                      color: 'text.primary'
                    }}
                  >
                    Value: {typeof factor.value === 'number' ? factor.value.toFixed(1) : factor.value}
                  </Typography>
                  {factor.threshold && (
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{
                        fontStyle: 'italic',
                        fontSize: '0.9rem'
                      }}
                    >
                      Threshold: {factor.threshold}
                    </Typography>
                  )}
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Grid>
    </Grid>
  );
};

export default RiskFactorsChart;
