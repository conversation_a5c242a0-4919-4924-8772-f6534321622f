import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Slider,
  Grid,
  Button,
  CircularProgress,
  Chip,
  useTheme,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import { useSpring, animated } from 'react-spring';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoIcon from '@mui/icons-material/Info';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import ThunderstormIcon from '@mui/icons-material/Thunderstorm';
import LocationOnIcon from '@mui/icons-material/LocationOn';

// Import the LocationSelector component
import LocationSelector from './LocationSelector';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

const TimelineRiskPredictor = ({ formData, onForecastGenerated }) => {
  const theme = useTheme();
  const [loading, setLoading] = useState(false);
  const [forecastData, setForecastData] = useState(null);
  const [timeRange, setTimeRange] = useState(48); // Default 48 hours
  const [selectedTimeIndex, setSelectedTimeIndex] = useState(null);
  const initialForecastGenerated = useRef(false);
  const [selectedLocation, setSelectedLocation] = useState({
    name: 'Delhi',
    state: 'Delhi',
    coordinates: [77.2090, 28.6139],
    userCoordinates: null // Will be populated when user shares their location
  });

  // Animation for the component
  const fadeIn = useSpring({
    from: { opacity: 0, transform: 'translateY(20px)' },
    to: { opacity: 1, transform: 'translateY(0)' },
    config: { tension: 280, friction: 20 },
    delay: 200
  });

  // Handle location selection
  const handleLocationSelect = useCallback((location) => {
    setSelectedLocation(location);
    // Reset selected time index when location changes
    setSelectedTimeIndex(null);

    // If we already have forecast data, show a message to the user
    if (forecastData) {
      // Clear the forecast data to avoid confusion
      setForecastData(null);
      // Reset the initial forecast flag so we don't automatically regenerate
      initialForecastGenerated.current = true;
    }
  }, [forecastData]);

  // Fetch real forecast data from the API
  const generateForecast = useCallback(() => {
    // Check if formData is available and has the required properties
    if (!formData || !formData.rainfall || !formData.water_level || !formData.discharge) {
      console.log("Form data is incomplete, cannot generate forecast");
      return;
    }

    setLoading(true);

    // Call the weather forecast API
    fetch('/api/weather-forecast', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...formData,
        hours: timeRange,
        // Use the selected location
        location: `${selectedLocation.name},${selectedLocation.state},India`,
        // If we have precise coordinates from geolocation, use them
        ...(selectedLocation.userCoordinates && {
          latitude: selectedLocation.userCoordinates[1],
          longitude: selectedLocation.userCoordinates[0]
        }),
        // Otherwise use the city coordinates
        ...(!selectedLocation.userCoordinates && {
          latitude: selectedLocation.coordinates[1],
          longitude: selectedLocation.coordinates[0]
        })
      }),
    })
      .then(response => {
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        return response.json();
      })
      .then(data => {
        // Set the forecast data from the API response
        setForecastData({
          timestamps: data.timestamps,
          rainfallData: data.rainfallData,
          waterLevelData: data.waterLevelData,
          dischargeData: data.dischargeData,
          riskScoreData: data.riskScoreData,
          temperatureData: data.temperatureData,
          humidityData: data.humidityData,
          location: data.summary?.location,
          country: data.summary?.country
        });

        // Notify parent component
        if (onForecastGenerated) {
          onForecastGenerated({
            maxRiskScore: data.summary.maxRiskScore,
            maxRiskTime: data.summary.maxRiskTime,
            riskTrend: data.summary.riskTrend,
            location: data.summary.location,
            country: data.summary.country
          });
        }

        setLoading(false);
      })
      .catch(error => {
        console.error('Error fetching forecast data:', error);
        setLoading(false);
      });
  }, [formData, timeRange, selectedLocation, onForecastGenerated]);

  // Handle time range change
  const handleTimeRangeChange = (_, newValue) => {
    setTimeRange(newValue);
  };

  // Handle chart click to select a specific time
  const handleChartClick = (_, elements) => {
    if (elements.length > 0) {
      setSelectedTimeIndex(elements[0].index);
    }
  };

  // Prepare chart data
  const chartData = forecastData ? {
    labels: forecastData.timestamps,
    datasets: [
      {
        label: 'Flood Risk Score',
        data: forecastData.riskScoreData,
        borderColor: 'rgba(58, 134, 255, 0.8)',
        borderWidth: 3,
        backgroundColor: (ctx) => {
          const gradient = ctx.chart.ctx.createLinearGradient(0, 0, 0, 400);
          gradient.addColorStop(0, 'rgba(76, 201, 240, 0.6)');
          gradient.addColorStop(1, 'rgba(58, 134, 255, 0.05)');
          return gradient;
        },
        fill: true,
        tension: 0.4,
        pointRadius: (ctx) => ctx.dataIndex === selectedTimeIndex ? 8 : 4,
        pointBackgroundColor: (ctx) => {
          const value = ctx.raw;
          if (value > 70) return theme.palette.error.main;
          if (value > 40) return theme.palette.warning.main;
          return theme.palette.success.main;
        },
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointHoverRadius: 8,
        pointHoverBackgroundColor: (ctx) => {
          const value = ctx.raw;
          if (value > 70) return theme.palette.error.dark;
          if (value > 40) return theme.palette.warning.dark;
          return theme.palette.success.dark;
        },
        pointHoverBorderColor: '#fff',
        pointHoverBorderWidth: 3,
      },
      // Add rainfall data as a secondary dataset
      {
        label: 'Rainfall (mm)',
        data: forecastData.rainfallData,
        borderColor: 'rgba(255, 89, 94, 0.7)',
        borderWidth: 2,
        backgroundColor: 'rgba(255, 89, 94, 0.1)',
        borderDash: [5, 5],
        fill: true,
        tension: 0.4,
        pointRadius: 0,
        yAxisID: 'y1',
      },
      // Add temperature data if available
      ...(forecastData.temperatureData ? [{
        label: 'Temperature (°C)',
        data: forecastData.temperatureData,
        borderColor: 'rgba(255, 159, 28, 0.7)',
        borderWidth: 2,
        backgroundColor: 'rgba(255, 159, 28, 0.0)',
        tension: 0.4,
        pointRadius: 2,
        pointBackgroundColor: 'rgba(255, 159, 28, 0.7)',
        pointBorderColor: 'rgba(255, 255, 255, 0.8)',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        pointHoverBackgroundColor: 'rgba(255, 159, 28, 0.9)',
        pointHoverBorderColor: '#fff',
        pointHoverBorderWidth: 2,
        yAxisID: 'y2',
        hidden: false, // Show by default for better visibility
      }] : [])
    ]
  } : null;

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index',
      intersect: false,
    },
    animations: {
      tension: {
        duration: 1000,
        easing: 'easeInOutCubic',
        from: 0.8,
        to: 0.4,
        loop: false
      }
    },
    plugins: {
      legend: {
        position: 'top',
        labels: {
          usePointStyle: true,
          padding: 15,
          font: {
            size: 12,
            weight: 'bold'
          }
        }
      },
      tooltip: {
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#333',
        bodyColor: '#333',
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 13
        },
        padding: 12,
        borderColor: 'rgba(58, 134, 255, 0.3)',
        borderWidth: 1,
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        boxPadding: 4,
        usePointStyle: true,
        callbacks: {
          label: function(context) {
            const value = context.raw;
            const datasetLabel = context.dataset.label;

            if (datasetLabel === 'Flood Risk Score') {
              let riskLevel = 'Low';
              let emoji = '✅';

              if (value > 70) {
                riskLevel = 'High';
                emoji = '⚠️';
              } else if (value > 40) {
                riskLevel = 'Medium';
                emoji = '⚠️';
              }

              return `${emoji} Risk Score: ${value.toFixed(1)} (${riskLevel})`;
            } else if (datasetLabel === 'Rainfall (mm)') {
              return `☔ Rainfall: ${value.toFixed(1)} mm`;
            } else if (datasetLabel === 'Temperature (°C)') {
              return `🌡️ Temperature: ${value.toFixed(1)} °C`;
            } else if (datasetLabel === 'Humidity (%)') {
              return `💧 Humidity: ${value.toFixed(0)}%`;
            } else {
              return `${datasetLabel}: ${value.toFixed(1)}`;
            }
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
        title: {
          display: true,
          text: 'Risk Score',
          font: {
            size: 14,
            weight: 'bold'
          },
          color: 'rgba(58, 134, 255, 0.8)'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.05)',
          borderDash: [5, 5]
        },
        ticks: {
          font: {
            size: 12
          },
          color: 'rgba(0, 0, 0, 0.6)'
        }
      },
      y1: {
        beginAtZero: true,
        position: 'right',
        max: Math.max(...forecastData?.rainfallData || [0]) * 1.2,
        title: {
          display: true,
          text: 'Rainfall (mm)',
          font: {
            size: 14,
            weight: 'bold'
          },
          color: 'rgba(255, 89, 94, 0.7)'
        },
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          },
          color: 'rgba(255, 89, 94, 0.7)'
        }
      },
      y2: {
        beginAtZero: false,
        position: 'right',
        min: Math.min(...(forecastData?.temperatureData || [20])) - 5,
        max: Math.max(...(forecastData?.temperatureData || [30])) + 5,
        title: {
          display: true,
          text: 'Temperature (°C)',
          font: {
            size: 14,
            weight: 'bold'
          },
          color: 'rgba(255, 159, 28, 0.7)'
        },
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 12
          },
          color: 'rgba(255, 159, 28, 0.7)'
        },
        display: true, // Always show the temperature axis
      },
      x: {
        title: {
          display: true,
          text: 'Time',
          font: {
            size: 14,
            weight: 'bold'
          }
        },
        ticks: {
          maxRotation: 45,
          minRotation: 45,
          font: {
            size: 11
          },
          color: 'rgba(0, 0, 0, 0.6)'
        },
        grid: {
          color: 'rgba(0, 0, 0, 0.05)'
        }
      }
    },
    onClick: handleChartClick
  };

  // Generate forecast only when the component mounts with valid form data
  useEffect(() => {
    // Only generate forecast if we have valid form data and haven't generated one yet
    if (formData &&
        formData.rainfall &&
        formData.water_level &&
        formData.discharge &&
        !initialForecastGenerated.current) {
      initialForecastGenerated.current = true;
      generateForecast();
    }
  }, [formData, generateForecast]);

  // Track location changes but don't automatically regenerate forecast
  useEffect(() => {
    // When location changes, we'll just update the UI to show the new location
    // but we won't automatically regenerate the forecast to avoid infinite loops
    // The user can click the "Generate Forecast" button to get data for the new location
    if (initialForecastGenerated.current) {
      console.log(`Location changed to ${selectedLocation.name}, ${selectedLocation.state}`);
    }
  }, [selectedLocation]);

  return (
    <animated.div style={fadeIn}>
      <Box sx={{ p: { xs: 4, sm: 5, md: 6 }, position: 'relative' }}>
        {/* Enhanced header section */}
        <Box sx={{
          mb: { xs: 4, sm: 5 },
          pb: 3,
          borderBottom: '2px solid rgba(58, 134, 255, 0.1)',
          position: 'relative'
        }}>
          {/* Decorative background element */}
          <Box
            sx={{
              position: 'absolute',
              top: -20,
              right: -20,
              width: 120,
              height: 120,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(76,201,240,0.08) 0%, rgba(0,0,0,0) 70%)',
              zIndex: 0
            }}
          />

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, position: 'relative', zIndex: 1 }}>
            <Box
              sx={{
                mr: { xs: 2, sm: 3 },
                p: { xs: 1.5, sm: 2 },
                borderRadius: 3,
                background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.15) 0%, rgba(76, 201, 240, 0.15) 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 4px 12px rgba(58, 134, 255, 0.2)'
              }}
            >
              <AccessTimeIcon sx={{ fontSize: { xs: 28, sm: 32 }, color: 'primary.main' }} />
            </Box>
            <Box>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 800,
                  color: theme.palette.primary.dark,
                  letterSpacing: '-0.8px',
                  mb: 0.5
                }}
              >
                Temporal Flood Risk Prediction
                <Tooltip title="This feature predicts how flood risk will change over time based on current conditions and weather forecasts">
                  <IconButton size="small" sx={{ ml: 1, mb: 1 }}>
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Typography>
              <Typography
                variant="subtitle1"
                sx={{
                  color: 'text.secondary',
                  fontWeight: 500
                }}
              >
                Real-time Weather-Based Risk Forecasting
              </Typography>
            </Box>
          </Box>

          <Typography
            variant="body1"
            sx={{
              fontSize: { xs: '1rem', sm: '1.1rem' },
              lineHeight: 1.7,
              color: 'text.secondary',
              maxWidth: '900px',
              position: 'relative',
              zIndex: 1
            }}
          >
            Analyze how flood risk evolves over the next hours and days based on your input parameters, real-time weather data, and advanced hydrological modeling.
          </Typography>
        </Box>

        {/* Location Selector */}
        <LocationSelector
          onLocationSelect={handleLocationSelect}
          initialLocation={selectedLocation}
        />

        <Box sx={{
          mb: { xs: 4, sm: 5 },
          p: { xs: 3, sm: 4 },
          borderRadius: 3,
          background: 'linear-gradient(135deg, rgba(58, 134, 255, 0.03) 0%, rgba(255,255,255,0.8) 100%)',
          border: '1px solid rgba(58, 134, 255, 0.1)'
        }}>
          <Grid container spacing={{ xs: 4, sm: 5 }}>
            <Grid item xs={12} sm={8}>
              <Box sx={{ mb: 2 }}>
                <Typography
                  variant="h6"
                  sx={{
                    fontWeight: 600,
                    color: 'primary.dark',
                    mb: 1
                  }}
                >
                  Forecast Time Range (hours)
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ mb: 2 }}
                >
                  Select the prediction timeframe for risk analysis
                </Typography>
              </Box>
              <Slider
                value={timeRange}
                onChange={handleTimeRangeChange}
                aria-labelledby="time-range-slider"
                valueLabelDisplay="auto"
                step={12}
                marks={[
                  { value: 12, label: '12h' },
                  { value: 24, label: '24h' },
                  { value: 48, label: '48h' },
                  { value: 72, label: '72h' }
                ]}
                min={12}
                max={72}
                sx={{
                  color: 'primary.main',
                  height: 8,
                  '& .MuiSlider-thumb': {
                    width: 24,
                    height: 24,
                    boxShadow: '0 4px 12px rgba(58, 134, 255, 0.3)'
                  },
                  '& .MuiSlider-track': {
                    height: 8,
                    borderRadius: 4,
                    background: 'linear-gradient(90deg, #3A86FF 0%, #4CC9F0 100%)'
                  },
                  '& .MuiSlider-rail': {
                    height: 8,
                    borderRadius: 4,
                    opacity: 0.3
                  },
                  '& .MuiSlider-mark': {
                    backgroundColor: 'primary.main',
                    height: 12,
                    width: 3,
                    borderRadius: 2
                  },
                  '& .MuiSlider-markLabel': {
                    fontWeight: 600,
                    color: 'primary.dark'
                  }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={4} sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: { xs: 'center', sm: 'flex-end' }
            }}>
              <Button
                variant="contained"
                color="primary"
                onClick={generateForecast}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
                sx={{
                  py: { xs: 1.5, sm: 2 },
                  px: { xs: 4, sm: 5 },
                  borderRadius: 3,
                  fontSize: { xs: '1rem', sm: '1.1rem' },
                  fontWeight: 600,
                  boxShadow: '0 8px 20px rgba(58, 134, 255, 0.3)',
                  background: 'linear-gradient(45deg, #3A86FF 30%, #4CC9F0 90%)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 12px 28px rgba(58, 134, 255, 0.4)'
                  },
                  '&:disabled': {
                    background: 'linear-gradient(45deg, #ccc 30%, #ddd 90%)',
                    transform: 'none'
                  },
                  transition: 'all 0.3s ease'
                }}
              >
                {forecastData ? 'Refresh Forecast' : 'Generate Forecast'}
              </Button>
            </Grid>
          </Grid>
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
            <CircularProgress />
          </Box>
        ) : forecastData ? (
          <>
            <Box
              sx={{
                height: 350,
                mb: 3,
                p: 2,
                borderRadius: 2,
                background: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(240,245,255,0.9) 100%)',
                boxShadow: 'inset 0 0 15px rgba(58, 134, 255, 0.1)',
                border: '1px solid rgba(58, 134, 255, 0.2)',
                position: 'relative',
                overflow: 'hidden'
              }}
            >
              {/* Decorative elements */}
              <Box
                sx={{
                  position: 'absolute',
                  top: -20,
                  right: -20,
                  width: 100,
                  height: 100,
                  borderRadius: '50%',
                  background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',
                }}
              />
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -30,
                  left: -30,
                  width: 150,
                  height: 150,
                  borderRadius: '50%',
                  background: 'radial-gradient(circle, rgba(255,89,94,0.1) 0%, rgba(0,0,0,0) 70%)',
                }}
              />

              {/* Chart title */}
              <Typography
                variant="h6"
                sx={{
                  mb: 2,
                  fontWeight: 600,
                  color: theme.palette.primary.dark,
                  textAlign: 'center',
                  textShadow: '0px 1px 2px rgba(0,0,0,0.05)'
                }}
              >
                Temporal Flood Risk Analysis
              </Typography>

              {/* The chart */}
              <Line data={chartData} options={chartOptions} />
            </Box>

            {selectedTimeIndex !== null && (
              <Box sx={{ mt: 2, mb: 3 }}>
                <Paper
                  elevation={3}
                  sx={{
                    p: 0,
                    overflow: 'hidden',
                    borderRadius: 3,
                    boxShadow: '0 8px 20px rgba(0, 0, 0, 0.08)'
                  }}
                >
                  {/* Header with timestamp */}
                  <Box
                    sx={{
                      p: 2,
                      background: 'linear-gradient(90deg, rgba(58,134,255,0.9) 0%, rgba(76,201,240,0.9) 100%)',
                      color: 'white',
                      position: 'relative',
                      overflow: 'hidden'
                    }}
                  >
                    {/* Decorative water ripple effect */}
                    <Box
                      sx={{
                        position: 'absolute',
                        top: -20,
                        right: -20,
                        width: 100,
                        height: 100,
                        borderRadius: '50%',
                        background: 'radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 70%)',
                      }}
                    />

                    <Typography variant="h6" sx={{ fontWeight: 600, textShadow: '0 1px 2px rgba(0,0,0,0.1)' }}>
                      <CalendarTodayIcon fontSize="small" sx={{ mr: 1, verticalAlign: 'middle' }} />
                      {forecastData.timestamps[selectedTimeIndex]}
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 0.5, opacity: 0.9 }}>
                      Detailed forecast information
                    </Typography>
                  </Box>

                  {/* Content with metrics */}
                  <Box sx={{ p: { xs: 3, sm: 4 } }}>
                    <Grid container spacing={{ xs: 3, sm: 4 }}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            background: `linear-gradient(135deg, ${
                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'rgba(255,89,94,0.1)' :
                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'rgba(251,133,0,0.1)' :
                              'rgba(6,214,160,0.1)'
                            } 0%, rgba(255,255,255,0.7) 100%)`,
                            border: `1px solid ${
                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'rgba(255,89,94,0.3)' :
                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'rgba(251,133,0,0.3)' :
                              'rgba(6,214,160,0.3)'
                            }`,
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                            Risk Score
                          </Typography>
                          <Typography variant="h4" sx={{
                            fontWeight: 'bold',
                            color: forecastData.riskScoreData[selectedTimeIndex] > 70 ? theme.palette.error.main :
                                  forecastData.riskScoreData[selectedTimeIndex] > 40 ? theme.palette.warning.main :
                                  theme.palette.success.main
                          }}>
                            {forecastData.riskScoreData[selectedTimeIndex].toFixed(1)}
                          </Typography>
                          <Chip
                            label={
                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'High Risk' :
                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'Medium Risk' : 'Low Risk'
                            }
                            size="small"
                            color={
                              forecastData.riskScoreData[selectedTimeIndex] > 70 ? 'error' :
                              forecastData.riskScoreData[selectedTimeIndex] > 40 ? 'warning' : 'success'
                            }
                            sx={{
                              mt: 1,
                              fontWeight: 'bold',
                              boxShadow: '0 2px 5px rgba(0,0,0,0.1)'
                            }}
                          />
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6} md={3}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            background: 'linear-gradient(135deg, rgba(255,89,94,0.1) 0%, rgba(255,255,255,0.7) 100%)',
                            border: '1px solid rgba(255,89,94,0.3)',
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                            <ThunderstormIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                            Rainfall
                          </Typography>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'rgba(255,89,94,0.8)' }}>
                            {forecastData.rainfallData[selectedTimeIndex].toFixed(1)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                            millimeters
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6} md={3}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            background: 'linear-gradient(135deg, rgba(76,201,240,0.1) 0%, rgba(255,255,255,0.7) 100%)',
                            border: '1px solid rgba(76,201,240,0.3)',
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                            <WaterDropIcon fontSize="small" sx={{ mr: 0.5, verticalAlign: 'middle' }} />
                            Water Level
                          </Typography>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'rgba(76,201,240,0.8)' }}>
                            {forecastData.waterLevelData[selectedTimeIndex].toFixed(2)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                            meters
                          </Typography>
                        </Box>
                      </Grid>

                      {forecastData.temperatureData && (
                        <Grid item xs={12} sm={6} md={3}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 2,
                              background: 'linear-gradient(135deg, rgba(255,159,28,0.1) 0%, rgba(255,255,255,0.7) 100%)',
                              border: '1px solid rgba(255,159,28,0.3)',
                              height: '100%',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center',
                              alignItems: 'center',
                              textAlign: 'center'
                            }}
                          >
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                              Temperature
                            </Typography>
                            <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'rgba(255,159,28,0.8)' }}>
                              {forecastData.temperatureData[selectedTimeIndex].toFixed(1)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                              °C
                            </Typography>
                          </Box>
                        </Grid>
                      )}

                      {forecastData.humidityData && (
                        <Grid item xs={12} sm={6} md={3}>
                          <Box
                            sx={{
                              p: 2,
                              borderRadius: 2,
                              background: 'linear-gradient(135deg, rgba(111,134,214,0.1) 0%, rgba(255,255,255,0.7) 100%)',
                              border: '1px solid rgba(111,134,214,0.3)',
                              height: '100%',
                              display: 'flex',
                              flexDirection: 'column',
                              justifyContent: 'center',
                              alignItems: 'center',
                              textAlign: 'center'
                            }}
                          >
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                              Humidity
                            </Typography>
                            <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'rgba(111,134,214,0.8)' }}>
                              {forecastData.humidityData[selectedTimeIndex].toFixed(0)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                              %
                            </Typography>
                          </Box>
                        </Grid>
                      )}

                      <Grid item xs={12} sm={6} md={3}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            background: 'linear-gradient(135deg, rgba(58,134,255,0.1) 0%, rgba(255,255,255,0.7) 100%)',
                            border: '1px solid rgba(58,134,255,0.3)',
                            height: '100%',
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                            textAlign: 'center'
                          }}
                        >
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1, fontWeight: 500 }}>
                            Discharge
                          </Typography>
                          <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'rgba(58,134,255,0.8)' }}>
                            {forecastData.dischargeData[selectedTimeIndex].toFixed(0)}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                            m³/s
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </Paper>
              </Box>
            )}

            <Box
              sx={{
                mt: 3,
                p: 2,
                borderRadius: 2,
                backgroundColor: 'rgba(76, 201, 240, 0.1)',
                border: '1px dashed rgba(58, 134, 255, 0.3)',
                display: 'flex',
                alignItems: 'flex-start'
              }}
            >
              <InfoIcon
                fontSize="small"
                sx={{
                  mr: 1.5,
                  mt: 0.3,
                  color: theme.palette.primary.main
                }}
              />
              <Box>
                <Typography variant="body2" sx={{ color: theme.palette.primary.dark, fontWeight: 500, mb: 0.5 }}>
                  Interactive Forecast Visualization for {forecastData.location || selectedLocation.name}, {selectedLocation.state}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  This forecast is based on real-time weather data and hydrological modeling for your selected location.
                  <strong> Click on any point in the chart</strong> to see detailed predictions for that specific time.
                  The blue line shows flood risk score, while the red dashed line shows rainfall intensity.
                  {forecastData.temperatureData && ' Temperature data is also available (toggle in chart legend).'}
                </Typography>
              </Box>
            </Box>
          </>
        ) : (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            height: 350,
            background: 'linear-gradient(135deg, rgba(240,245,255,0.8) 0%, rgba(230,240,255,0.8) 100%)',
            borderRadius: 3,
            border: '1px dashed rgba(58, 134, 255, 0.3)',
            position: 'relative',
            overflow: 'hidden',
            p: 3
          }}>
            {/* Decorative elements */}
            <Box
              sx={{
                position: 'absolute',
                top: -30,
                right: -30,
                width: 150,
                height: 150,
                borderRadius: '50%',
                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',
              }}
            />
            <Box
              sx={{
                position: 'absolute',
                bottom: -30,
                left: -30,
                width: 150,
                height: 150,
                borderRadius: '50%',
                background: 'radial-gradient(circle, rgba(76,201,240,0.1) 0%, rgba(0,0,0,0) 70%)',
              }}
            />

            <AccessTimeIcon sx={{
              fontSize: 80,
              color: 'rgba(58, 134, 255, 0.3)',
              mb: 3,
              filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
            }} />

            <Typography variant="h6" color="primary.dark" align="center" sx={{ mb: 1, fontWeight: 600 }}>
              Temporal Flood Risk Prediction
            </Typography>

            <Typography variant="body1" color="text.secondary" align="center" sx={{ mb: 3, maxWidth: 450 }}>
              Generate a forecast to visualize how flood risk may change over the next hours and days based on current conditions.
            </Typography>

            <Button
              variant="contained"
              color="primary"
              onClick={generateForecast}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <RefreshIcon />}
              sx={{
                px: 3,
                py: 1.2,
                borderRadius: 8,
                boxShadow: '0 4px 14px rgba(58, 134, 255, 0.3)',
                '&:hover': {
                  boxShadow: '0 6px 20px rgba(58, 134, 255, 0.4)',
                  transform: 'translateY(-2px)'
                },
                transition: 'all 0.3s ease'
              }}
            >
              Generate Forecast
            </Button>
          </Box>
        )}
      </Box>
    </animated.div>
  );
};

export default TimelineRiskPredictor;
